<?php

namespace App\Filters;

use App\Exceptions\GeneralException;
use App\Filters\Interfaces\FilterInterface;
use App\Models\Faq;
use App\Traits\Models\Translatable;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Schema;
use ReflectionClass;

class WithTranslationsFilter implements FilterInterface
{
    /**
     * @throws GeneralException
     */
    public function handle(Builder $query, Closure $next): mixed
    {
        return $next($query);

        if (!request()->has('withTranslations')) {
            return $next($query);
        }

        if (!has_trait($query->getModel(), Translatable::class)) {
            throw new GeneralException(
                message: __(
                    'response-messages.class_not_have_trait',
                    ['class' => $query->getModel()::class, 'trait' => Translatable::class]
                )
            );
        }
        $table = $query->getModel()->getTable();

        $columns = Schema::getColumnListing($table);

        // return $next($query);
    }

    public function getValue(): mixed
    {
        return request()->input('search');
    }
}
