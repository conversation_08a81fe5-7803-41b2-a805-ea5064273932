<?php

namespace App\Filters;

use App\Exceptions\GeneralException;
use App\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class SlugFilter implements FilterInterface
{
    /**
     * @throws GeneralException
     */
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('slug')) {
            return $next($query);
        }

        return $next($query)->where('slug', request()->input('slug'));
    }

    public function getValue(): mixed
    {
        return request()->input('slug');
    }
}
