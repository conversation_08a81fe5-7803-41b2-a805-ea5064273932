<?php

namespace App\Filters;

use App\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

use function request;

class TitleFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('title')) {
            return $next($query);
        }

        return $next($query)->where('title', request()->input('title'));
    }

    public function getValue(): mixed
    {
        return request()->input('title');
    }
}
