<?php

namespace App\Filters;

use App\Exceptions\GeneralException;
use App\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class NameFilter implements FilterInterface
{
    /**
     * @throws GeneralException
     */
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('name')) {
            return $next($query);
        }

        return $next($query)->where('name', request()->input('name'));
    }

    public function getValue(): mixed
    {
        return request()->input('name');
    }
}
