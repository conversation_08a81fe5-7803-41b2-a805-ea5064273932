<?php

namespace App\Filters;

use App\Enums\DatabaseConnectionsEnum;
use App\Filters\Interfaces\FilterInterface;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;

use App\Repositories\Language\LanguageRepositoryInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class AbbreviationRelationFilter implements FilterInterface, WithOptions
{
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('abbreviation')) {
            return $next($query);
        }

        return $next($query)->withWhereHas('languages', function ($q) {
            return $q->from('languages')->where(
                'abbreviation',
                request()->input('abbreviation')
            );
        });
    }

    public function getValue(): mixed
    {
        return request()->input('abbreviation');
    }

    public function getOptions(): OptionsResource
    {
        $options = resolve(
            LanguageRepositoryInterface::class
        )->getAllLanguages();

        return new OptionsResource(resource: $options, textColumn: 'name', valueColumn: 'abbreviation');
    }
}
