<?php

namespace App\Filters;

use Closure;
use App\Filters\Interfaces\WithType;
use Illuminate\Database\Eloquent\Builder;
use App\Filters\Interfaces\FilterInterface;

class IsPublishedFilter implements FilterInterface, WithType
{
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('isPublished')) {
            return $next($query);
        }

        $abbreviation = request()->boolean('isPublished');

        return $next($query)->when(
            !$abbreviation,
            fn($q) => $q->whereNull('publishedAt'),
            fn($q) => $q->whereNotNull('publishedAt')
        );
    }

    public function getValue(): mixed
    {
        return request()->filled('isPublished') ? (boolean) request()->get('isPublished') : null;
    }


    public function getType()
    {
        return "number";
    }
}