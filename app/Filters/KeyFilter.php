<?php

namespace App\Filters;

use App\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class KeyFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('key')) {
            return $next($query);
        }

        return $next($query)->where('key', request()->input('key'));
    }

    public function getValue(): mixed
    {
        return request()->input('key');
    }
}
