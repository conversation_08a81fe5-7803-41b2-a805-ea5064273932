<?php

namespace App\Filters;

use App\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class TypeFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('type')) {
            return $next($query);
        }

        return $next($query)->where('type', request()->input('type'));
    }

    public function getValue(): mixed
    {
        return request()->input('type');
    }




    
}
