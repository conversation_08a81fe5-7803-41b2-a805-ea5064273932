<?php

namespace App\Filters;

use Closure;
use App\Filters\Interfaces\WithType;
use Illuminate\Database\Eloquent\Builder;
use App\Filters\Interfaces\FilterInterface;

class PriceRangeFilter implements FilterInterface, WithType
{
    public function handle(Builder $query, Closure $next): mixed
    {
   
        if (request()->has('min') && request()->has('max')) {

            $min = request()->integer('min');
            $max = request()->integer('max');
          
            return $next($query)->whereHas('default.stocks.price', function ($query) use($max,$min) {
                // $query->whereBetween('price', [$min, $max]);
                $query->where('price' ,'<=', $max);
                $query->where('price' ,'>=', $min);

            })->whereHas('bundle.stocks.price', function ($query) use($max,$min)   {
                //$query->whereBetween('price', [$min, $max]);
                $query->where('price' ,'<=', $max);
                $query->where('price' ,'>=', $min);

            });

            

        }


        return $next($query);
    }

    public function getValue(): mixed
    {
        return request()->filled('min') && request()->filled('max') ? ['min' => request()->get('min'), 'max' => request()->get('max')] : null;
    }


    public function getType()
    {
        return "rang";
    }
}