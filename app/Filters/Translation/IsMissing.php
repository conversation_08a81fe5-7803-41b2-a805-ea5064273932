<?php

namespace App\Filters\Translation;

use App\Enums\DatabaseConnectionsEnum;
use App\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class IsMissing implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('is_missing')) {
            return $next($query);
        }

        if (request()->boolean('is_missing') == false) {
            return $next($query);
        }

        return $next($query)->withWhereHas(
                'languages', fn($query) => $query->from('languages')->whereNull('value_id')

           // 'languages',
         //   fn($q) => $q->from(DatabaseConnectionsEnum::INGOT_BROKERS->tableName('languages'))->whereNull('value_id')
        );
    }

    public function getValue(): mixed
    {
        return request()->filled('is_missing') ? request()->boolean('is_missing') : null;
    }
}