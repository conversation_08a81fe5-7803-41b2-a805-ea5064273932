<?php

namespace App\Filters\Translation;

use App\Filters\Interfaces\FilterInterface;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use App\Repositories\TranslationKey\TranslationKeyRepositoryInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class Group implements FilterInterface, WithOptions
{
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('group')) {
            return $next($query);
        }

        return $next($query)->where('group', request()->input('group'));
    }

    public function getValue(): mixed
    {
        return request()->input('group');
    }

    public function getOptions(): OptionsResource
    {
        $options = resolve(TranslationKeyRepositoryInterface::class)
            ->getAllGroupByColumn('group');

        return new OptionsResource(resource: $options, textColumn: 'group', valueColumn: 'group');
    }
}
