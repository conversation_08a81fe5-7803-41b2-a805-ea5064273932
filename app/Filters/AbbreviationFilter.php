<?php

namespace App\Filters;

use App\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class AbbreviationFilter implements FilterInterface
{

    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('abbreviation')) {
            return $next($query);
        }

        return $next($query)->where('abbreviation', request()->input('abbreviation'));
    }

    public function getValue(): mixed
    {
        return request()->input('abbreviation');
    }
}