<?php

namespace App\Http\Resources;

use App\Filters\Interfaces\WithOptions;
use App\Filters\Interfaces\SetAttributeOptionsIntreFace;
use App\Filters\SearchFilter;
use App\Traits\Models\HasFilters;
use App\Traits\Models\HasMeilisearchFilters;
use App\Traits\Models\Searchable;
use Illuminate\Http\Resources\Json\ResourceCollection;
use stdClass;

class PaginationCollection extends ResourceCollection
{
    private $filters;
    private $filtersModel;

    public function __construct($resource, protected $resourceCollection = null, $filtersModel = null)
    {
        parent::__construct($resource);
        $this->filtersModel = get_model($filtersModel);
        $this->filters = $this->getFilters();

    }

    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        return [
            'items' => !is_null($this->resourceCollection)
                ? $this->resourceCollection::collection($this->collection)
                : $this->collection,
            'filters' => $this->when(!blank((array) $this->filters), $this->filters),
            'pagination' => [
                'total' => $this->resource->total(),
                'count' => $this->resource->count(),
                'perPage' => $this->resource->perPage(),
                'page' => $this->resource->currentPage(),
                'lastPage' => $this->resource->lastPage(),
                // 'path' => $this->resource->path(),
            ],
        ];
    }

    private function getModelClassFromResource()
    {
        if ($this->resource->isEmpty()) {
            return null;
        }

        return get_model(get_class($this->resource->first()));
    }

    private function getFilters(): stdClass|null
    {
        if ($this->filtersModel) {
            $model = $this->filtersModel;
        } else {
            $model = $this->getModelClassFromResource();

        }

        if (!$model) {
            return null;
        }

        $filters = new stdClass();
        if (has_trait($model, HasFilters::class) && !isset($this->resource->meilisearchFilters)) {
            foreach ($model->allowedFilters() as $name => $class) {
                $object = resolve($class);

                if (!isset($filters->{$name})) {
                    $filters->{$name} = new stdClass();
                }
                if (has_interface($object, SetAttributeOptionsIntreFace::class)) {
                    $object->setAttributeOptions($name);
                }
                if (has_interface($object, WithOptions::class)) {
                    $filters->{$name}->options = $object->getOptions($this->resource, $name);
                }
                $filters->{$name}->value = $object->getValue();

                if (has_interface($object, \App\Filters\Interfaces\WithType::class)) {
                    $filters->{$name}->type = $object->getType();
                }
            }
        }

        if (has_trait($model, HasMeilisearchFilters::class) && isset($this->resource->meilisearchFilters)) {
            $modelFilters = $this->resource->meilisearchFilters;

            foreach ($modelFilters as $key => $filter) {

                if (!isset($filters->{$key})) {
                    $filters->{$key} = new stdClass();
                }

                $filters->{$key}->name = $filter->getName();
                $filters->{$key}->value = $filter->getValue();


                if (has_interface($filter, WithOptions::class)) {
                    $filters->{$key}->options = $filter->getOptions($this->resource);
                }



                if (has_interface($filter, \App\Filters\Interfaces\WithType::class)) {
                    $filters->{$key}->type = $filter->getType();
                }

                if (has_interface($filter, \App\Filters\Interfaces\WithConfig::class)) {
                    $filters->{$key}->config = $filter->getConfig();
                }

            }

        }


        if (has_trait($model, Searchable::class)) {
            $object = resolve(SearchFilter::class);

            if (!isset($filters->search)) {
                $filters->search = new stdClass();
            }

            $filters->search->value = $object->getValue();
        }

        return $filters;
    }
}