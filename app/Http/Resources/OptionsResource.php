<?php

namespace App\Http\Resources;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;
use Illuminate\Support\Arr;

class OptionsResource extends ApiJsonResource
{
    public function __construct(
        $resource,
        protected string|array $textColumn = '',
        protected string|array $valueColumn = '',
        protected array $meta = [],
        protected string $textSeparator = ' ',
    ) {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $resource = $this->resource instanceof Collection ? $this->resource : collect($this->resource);

        return $resource->map(function ($item) {
            return $this->transformItem($item);
        })->toArray();
    }

    /**
     * Transform a single item
     *
     * @param  mixed  $item
     * @return array
     */
    protected function transformItem($item): array
    {
        return [
            'text' => $this->getTextColumn($item),
            'value' => $this->getValueColumn($item),
            'meta' => $this->getMetaData($item),
        ];
    }

    /**
     * Get the text column value(s)
     *
     * @param  mixed  $item
     * @return string
     */
    protected function getTextColumn($item): string
    {
        if (is_array($this->textColumn)) {
            return collect($this->textColumn)
                ->map(fn($column) => $this->getPropertyValue($item, $column))
                ->filter()
                ->implode($this->textSeparator);
        }

        return $this->getPropertyValue($item, $this->textColumn) ?? '';
    }

    /**
     * Get the value column value(s)
     *
     * @param  mixed  $item
     * @return mixed
     */
    protected function getValueColumn($item): mixed
    {
        if (is_array($this->valueColumn)) {
            $values = collect($this->valueColumn)
                ->mapWithKeys(fn($value, $key) => [
                    is_numeric($key) ? $value : $key => $this->getPropertyValue($item, $value)
                ])
                ->all();

            return (object) $values;
        }

        return $this->getPropertyValue($item, $this->valueColumn);
    }

    /**
     * Get meta data for the item
     *
     * @param  mixed  $item
     * @return array|null
     */
    protected function getMetaData($item): array
    {
        if (blank($this->meta)) {
            return [];
        }

        $meta = [];

        foreach ($this->meta as $column) {
            if (is_array($column)) {
                $meta[key($column)] = $this->handleMediaColumn($item, key($column), head($column));
            } else {
                $meta[$column] = $this->handleRegularColumn($item, $column);
            }
        }

        return $meta;
    }

    /**
     * Handle media column in meta data
     *
     * @param  mixed  $item
     * @param  string  $key
     * @param  string  $column
     * @return mixed
     */
    protected function handleMediaColumn($item, string $key, string $column): mixed
    {
        return get_media_url($column, $item->{$key});
    }

    /**
     * Handle regular column in meta data
     *
     * @param  mixed  $item
     * @param  string  $column
     * @return mixed
     */
    protected function handleRegularColumn($item, string $column): mixed
    {
        if ($column === 'children') {
            return $this->handleChildrenColumn($item);
        }

        return $this->getPropertyValue($item, $column);
    }

    /**
     * Handle children column in meta data
     *
     * @param  mixed  $item
     * @return OptionsResource|null
     */
    protected function handleChildrenColumn($item): ?OptionsResource
    {
        if (!$item->relationLoaded('children') || empty($item->children)) {
            return null;
        }

        return new OptionsResource(
            resource: $item->children,
            textColumn: $this->textColumn,
            valueColumn: $this->valueColumn,
            meta: $this->meta,
            textSeparator: $this->textSeparator
        );
    }

    /**
     * Get property value from item using dot notation
     *
     * @param  mixed  $item
     * @param  string  $property
     * @return mixed
     */
    protected function getPropertyValue($item, string $property): mixed
    {
        if (empty($property)) {
            return null;
        }

        return Arr::get($item, $property);
    }
}