<?php

namespace App\Http\Resources\Website;



use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;
class BannerResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response = [
            'bannerId' => $this->bannerId,
            'url' => $this->url,
            'extra' => $this->extra
        ];
        if ($this->whenLoaded('media')) {
            $response["media"] = MediaResource::generate($this);
        }


        return $response;
    }
}