<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class ProductResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        //$response = parent::toArray($request);

        $response = [
            "productId" => $this->productId,
            "name" => $this->name,
            "brandId" => $this->brandId,
            'brand' => BrandResource::generate($this->whenLoaded('brand')),
            "productGroupId" => $this->productGroupId,
            "description" => $this->description,
            "type" => $this->type,
            "slug" => $this->slug,
            "isListed" => $this->isListed,
            "variationAttributes" => $this->variationAttributes,
            "avgRate" => $this->avgRate ? $this->avgRate : 0,
            'categories' => CategoryResource::generate($this->whenLoaded('categories')),
        ];

        if ($response['type'] == 'bundle') {
            $response['bundle'] = ProductBundleResource::generate($this->whenLoaded('bundle'));
            $response['stock'] = StockResource::generate($this->whenLoaded('activeStock'));
        } else {
            $response['variance'] = ProductDefaultResource::generate($this->whenLoaded('variance'));

        }
        if ($this->whenLoaded('media')) {
            $response['media'] = MediaResource::generate($this);
        }
        return $response;
    }
}