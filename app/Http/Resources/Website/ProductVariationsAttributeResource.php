<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\Website\OptionsResource;

class ProductVariationsAttributeResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        $response = [
            'attributeId' => $this->attributeId,
            'name' => $this->name,
            'prefix' => $this->prefix,
            'suffix' => $this->suffix,
            'type' => $this->type,
            'options' => OptionsResource::generate($this->options),
            'value' => ValueAttributeResource::generate($this->valueVariance),
            // 'attributeOptionsStock' => AttributeOptionsStockResource::collection($this->variances ?? []),
            //  'value' => !is_null($this->valueVariance->attributeOptionId) ? $this->valueVariance->attributeOptionId : $this->valueVariance->value
        ];

        //AttributeValueResource::generate($this->valueVariance),
        //$response['options'] = OptionsResource::generate($this->options);
        // $response['value'] = AttributeValueResource::generate($this->value);

        return $response;

    }
}