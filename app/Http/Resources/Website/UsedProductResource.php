<?php

namespace App\Http\Resources\Website;


use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\Lookup\UsedCategoryResource;

class UsedProductResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        $response = parent::toArray($request);
        $response['isPublished'] = (bool) $this->isPublished;
        $response['attributes'] = AttributeResource::generate($this->whenLoaded('attributes'));
        $response['price'] = $this->currencyPriceResource($this->price);
        $response['usedCategories'] = UsedCategoryResource::generate($this->whenLoaded('usedCategories'));
        return $response;
    }
}