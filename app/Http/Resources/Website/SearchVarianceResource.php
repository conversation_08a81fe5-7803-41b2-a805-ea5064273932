<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class SearchVarianceResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = [
            "varianceId" => $this->varianceId ?? null,
            "name" => $this->name ?? null,
            'brand' => $this->brand?->name ?? null,
            "description" => $this->description ?? null,
            "SKU" => $this->SKU ?? '',
            "media" => MediaResource::generate($this),
            "stock" => StockResource::generate($this->activeStock ?? null),
            "basePrice" => $this->currencyPriceResource($this->basePrice),
            'hasOffer' => $this->isOffer,
            'priceBeforeOffer' => $this->priceBeforeOffer ?? $this->currencyPriceResource($this->priceBeforeOffer),
            "product" => ProductResource::generate($this->product),
        ];

        return $response;
    }
}