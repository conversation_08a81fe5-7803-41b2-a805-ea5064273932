<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\Auth\RolesResource;
use App\Http\Resources\General\MediaResource;

use App\Http\Resources\ApiJsonResource;

class AccountResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
        $response['media'] = MediaResource::generate($this);
        $response["roles"] = RolesResource::generate($this->whenLoaded('roles'));
        $response['wallet'] = $this->currencyPriceResource($this->wallet);
        $response['awaitingApproval'] = $this->awaitingApproval ?? false;
        return $response;

    }
}