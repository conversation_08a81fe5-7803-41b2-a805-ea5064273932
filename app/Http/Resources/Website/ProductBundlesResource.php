<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class ProductBundlesResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        $response = [
            "bundleId" => $this->bundleId,
            "name" => $this?->variance?->name ?? '',
            "slug" => $this?->variance?->slug ?? '',
            "productId" => $this->productId,
        ];

        if ($this?->variance && $this->variance->relationLoaded('media')) {
            $response['media'] = MediaResource::generate($this?->variance ?? null);
        }

        if ($this?->variance && $this->variance->relationLoaded('attributesWithValue')) {
            $response['attributes'] = AttributeVarianceResource::generate($this->variance->attributesWithValue ?? []);
        } else {
            $response['attributes'] = [];
        }

        return $response;
    }
}