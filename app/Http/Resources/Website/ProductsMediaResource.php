<?php

namespace App\Http\Resources\Website;
use App\Http\Resources\ApiJsonResource;
class ProductsMediaResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'productMediaId' => $this->productMediaId,
            'media' => MediaResource::generate($this->whenLoaded('media')),
        ];
    }
}
