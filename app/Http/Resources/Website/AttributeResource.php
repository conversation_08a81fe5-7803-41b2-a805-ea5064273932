<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\Website\OptionsResource;

class AttributeResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        return [
            'attributeId' => $this->attributeId,
            'name' => $this->name,
            'prefix' => $this->prefix,
            'suffix' => $this->suffix,
            'options' => OptionsResource::generate($this->whenLoaded('options')),
            'value' => ValueResource::generate($this->whenLoaded('varianceAttributesValue')),
        ];

        // return $response;
    }
}