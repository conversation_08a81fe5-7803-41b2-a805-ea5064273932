<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\Website\OptionsResource;

class ProductVariationAttributeResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        return parent::toArray($request);


        $response = [
            'attributeId' => $this->attributeId,
            'name' => $this->name,
            'prefix' => $this->prefix,
            'suffix' => $this->suffix,

        ];

        $response['options'] = OptionsResource::generate($this->whenLoaded('options'));
        $response['value'] = AttributeValueResource::generate($this->whenLoaded('value'));

        return $response;

    }
}