<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class PaymentMethodResource extends ApiJsonResource
{

    public function toArray($request)
    {
        $response = parent::toArray($request);
        if (isset($response['media'])) {
            //use media resource
            $response['media'] = MediaResource::generate($this);
        }
        return $response;
    }
}
