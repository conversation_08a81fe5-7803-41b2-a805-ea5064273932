<?php

namespace App\Http\Resources\Website;
use App\Http\Resources\ApiJsonResource;
class BrandsMediaResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'brandMediaId' => $this->brandMediaId,
            'media' => MediaResource::generate($this->whenLoaded('media')),
        ];
    }
}
