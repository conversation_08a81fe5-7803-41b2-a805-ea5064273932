<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;


class ValueResource extends ApiJsonResource
{

    public function toArray($request)
    {
        // return parent::toArray($request);
        return [
            'text' => $this->option->name,
            'value' => $this->attributeOptionId,
            'meta' => [
                'slug' => $this->option->slug,
            ],
        ];
    }
}
