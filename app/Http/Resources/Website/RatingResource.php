<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class RatingResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        $countRatings = $this['ratings']->count() ?? 0;
        $sumRatings = $this['ratings']->sum('rating') ?? 0;

        $avgRating = $countRatings > 0 ? ($sumRatings / $countRatings) : 0;

        //$avgRating = $this['ratings']->sum('rating') ?? 0 / $this['ratings']->count() ?? 0;
        return ['ratings' => $this->ratings($this), 'avgRating' => $avgRating];


        /*$totalReviews = $this['ratings']->count() ?? 0;
        $response = [];
        for ($i = 5; $i >= 1; $i--) {
            $ratings = $this['ratings']->where('rating', $i)->all();
            $ratingCount = count($ratings);
            $percentage = ($ratingCount) ? ($ratingCount ?? 0 / $totalReviews) * 100 : 0;
            $response[] = [
                'rating' => $i,
                'percentage' => round($percentage, 2),
                'totalReviews' => $ratingCount,
                'reviews' => RateResource::collection($ratings)
            ];
        }

        return $response; */

    }



    private function ratings($allRatings)
    {
        $totalReviews = $allRatings['ratings']->count() ?? 0;
        $response = [];
        for ($i = 5; $i >= 1; $i--) {
            $ratings = $allRatings['ratings']->where('rating', $i)->all();
            $ratingCount = count($ratings);
            $percentage = ($ratingCount) ? ($ratingCount ?? 0 / $totalReviews) * 100 : 0;
            $response[] = [
                'rating' => $i,
                'percentage' => round($percentage, 2),
                'totalReviews' => $ratingCount,
                'reviews' => RateResource::collection($ratings)
            ];
        }

        return $response;
    }

}