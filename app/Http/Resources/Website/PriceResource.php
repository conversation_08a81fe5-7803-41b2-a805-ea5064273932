<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class PriceResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'priceId' => $this->priceId,
            'price' => $this->price,
            'currency' => CurrencyResource::generate($this->whenLoaded('currency')),

        ];
    }
}