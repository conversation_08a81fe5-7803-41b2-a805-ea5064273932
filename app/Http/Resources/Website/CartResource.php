<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Helper\Currency;


class CartResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
        $response["items"] = ItemCartResource::generate($response["items"]);
        $response['total'] = $this->calculateTotal($response['total']);
        return $response;



    }


    protected function calculateTotal($total)
    {

        $userCurrency = getCurrency();
        $currency = Currency::convert($total)->fromBase()->to($userCurrency);
        return [
            "value" => $currency->get(),
            "currency" => $userCurrency,
            "symbol" => $currency->symbol()
        ];

    }



}