<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\General\MediaResource;
use App\Http\Resources\ApiJsonResource;

class UsedCategoryResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        // $response = parent::toArray($request);

        return [
            "usedCategoryId" => $this->usedCategoryId,
            "name" => $this->name,
            "slug" => $this->slug,
            "brands" => BrandResource::generate($this->whenLoaded('brands')),
            "attributes" => AttributesResource::generate($this->whenLoaded('attributes')),
            "media" => $this->whenLoaded('media') ? MediaResource::generate($this) : [],
        ];

    }
}
