<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class OrderResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
        $response['orderItems'] = OrderItemsResource::generate($this->whenLoaded('orderItems'));
        $response['subTotal'] = $this->currencyPriceResource($this->subTotal);
        $response['total'] = $this->currencyPriceResource($this->total);
        $response['tax'] = $this->currencyPriceResource($this->tax);
        $response['shippingPrice'] = $this->currencyPriceResource($this->shippingPrice);

        $response['invoices'] = InvoiceResource::generate($this->whenLoaded('invoices'));
        $response['user'] = UserResource::generate($this->whenLoaded('user'));
        $response['address'] = AddressResource::generate($this->whenLoaded('address'));
        $response['deliveries'] = DeliveryResource::generate($this->whenLoaded('deliveries'));
        return $response;
    }
}