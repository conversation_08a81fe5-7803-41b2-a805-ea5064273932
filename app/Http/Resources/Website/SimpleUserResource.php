<?php

namespace App\Http\Resources\Website;

use App\Helper\Currency;
use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class SimpleUserResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {


        return [
            'userId' => $this->userId,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'media' => MediaResource::generate($this),
        ];



    }


    protected function priceArray($price)
    {
        $userCurrency = getCurrency();
        $currency = Currency::convert($price)->fromBase()->to($userCurrency);
        return [
            "value" => $currency->get(),
            "currency" => $userCurrency,
            "symbol" => $currency->symbol()
        ];

    }

}
