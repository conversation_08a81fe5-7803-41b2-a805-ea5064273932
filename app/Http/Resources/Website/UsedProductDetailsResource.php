<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\General\PriceResource;
use App\Http\Resources\ApiJsonResource;

class UsedProductDetailsResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = [
            "usedProductId" => $this->usedProductId,
            "name" => $this->name,
            "description" => $this->description,
            "price" => PriceResource::generate($this->whenLoaded('price')),
            "user" => UserResource::generate($this->whenLoaded('user')),
            'brand' => BrandResource::generate($this->whenLoaded('brand')),
            "createdAt" => $this->createdAt,
            'usedCategories' => UsedCategoryResource::generate($this->whenLoaded('usedCategories')),
            'attributes' => AttributeResource::generate($this->whenLoaded('attributes')),
            'media' => MediaResource::generate($this)

        ];

        return $response;
    }
}