<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class RateResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        return [
            'ratingId' => $this->ratingId,
            'review' => $this->review,
            'userId' => $this->userId,
            'status' => $this->status,
            'user' => SimpleUserResource::generate($this->whenLoaded('user')),

        ];



    }

}