<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class ProductDefaultResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        return [
            "varianceId" => $this->varianceId,
            "auctionId" => $this->auctionId,
            "name" => $this->name,
            "slug" => $this->slug,
            "brandId" => $this->brandId,
            "SKU" => $this->SKU,
            "type" => $this->type,
            "stock" => StockResource::generate($this->whenLoaded('activeStock')),
        ];



    }
}