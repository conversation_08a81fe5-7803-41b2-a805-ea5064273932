<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\PriceResource;

class OrderItemsResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        $response['orderItemsId'] = $this->orderItemsId;
        $response['orderId'] = $this->orderId;
        $response['model_type'] = $this->model_type;
        $response['model_id'] = $this->model_id;
        $response['snapshot'] = $this->model?->snapshot ?? [];
        $response['productOrderItemId'] = $this->model?->productOrderItemId ?? null;
        $response['productId'] = $this->model?->productId ?? null;
        $response['varianceId'] = $this->model?->varianceId ?? null;
        $response['brandId'] = $this->model?->brandId ?? null;
        $response['quantity'] = $this->model?->quantity ?? 0;
        $response['status'] = $this->model?->status ?? '';
        $response['discount'] = $this->model?->discount ?? 0;
        $response['stockId'] = $this->model?->stockId ?? null;
        // $response['priceId'] = $this->model->priceId;
        // $response['originalPriceId'] = $this->model->originalPriceId;
        $response['product'] = ProductResource::generate($this->model?->product ?? null);

        if (!is_null($this->model?->varianceId)) {
            $response['variance'] = ProductDefaultResource::generate($this->model->variance);
        }

        $response['price'] = $this->currencyPriceResource($this->model?->price ?? 0);
        $response['originalPrice'] = $this->currencyPriceResource($this->model?->originalPrice ?? 0);


        return $response;
    }

}