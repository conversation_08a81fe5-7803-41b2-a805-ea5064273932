<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaCollectionResource;
use App\Http\Resources\General\MediaResource;

class AutocompleteResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        //dd($this->resource);
        // $data = parent::toArray($request);
        // dd($data);
        $response = [
            "productId" => $this->productId,
            "SKU" => $this->variance?->SKU ?? '',
            "brandId" => $this->product?->brandId ?? null,
            "slug" => $this->slug ?? '',
            "name" => $this->name ?? [],
            "media" => [
                "cover" => MediaCollectionResource::generate($this->whenLoaded('covers')),
            ],
            "variance" => [
                "varianceId" => $this->variance?->varianceId,
                "slug" => $this->variance?->slug ?? '',
                "name" => $this->variance?->name ?? '',
                // "media" => MediaResource::generate($this),
            ],
            'score' => $this->score

        ];


        return $response;
    }
}