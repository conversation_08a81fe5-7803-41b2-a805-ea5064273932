<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Models\Stock;

class AttributeOptionsStockResource extends ApiJsonResource
{
    public function toArray($request)
    {
        return [
            "varianceId" => $this['varianceId'],
            "name" => $this['name'],
            "slug" => $this['slug'],
            "brandId" => $this['brandId'],
            "SKU" => $this['SKU'],
            "type" => $this['type'],
        ];
    }
}