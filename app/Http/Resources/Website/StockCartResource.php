<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\General\PriceResource;
use App\Http\Resources\ApiJsonResource;
class StockCartResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            "price" => PriceResource::generate($this->whenLoaded('price')),
        ];
    }
}
