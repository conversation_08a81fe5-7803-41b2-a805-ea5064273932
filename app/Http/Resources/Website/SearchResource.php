<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class SearchResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = [
            "productId" => $this->productId,
            "name" => $this->name ?? null,
            "brandId" => $this->brandId ?? null,
            'brand' => $this->brand?->name ?? null,
            "productGroupId" => $this->productGroupId,
            "description" => $this->description ?? null,
            "type" => $this->type ?? null,
            "slug" => $this->slug ?? null,
            "isListed" => $this->isListed ?? false,
            "SKU" => $this->variance?->SKU ?? '',
            // "variationAttributes" => $this->variationAttributes,
            "avgRate" => $this->avgRate ?? 0,
            //no need to load categories here
            //'categories' => CategoryResource::generate($this->whenLoaded('categories')),
            //"categoryId" => @$this->product ?? $this->product->categories->pluck('categoryId')->toArray(),
            "media" => $this->whenLoaded('media', function () {
                return $this->media->isNotEmpty()
                    ? MediaResource::generate($this)
                    : [];
            }),
            "colors" => $this->colors ?? null,
            'variance' => [
                "varianceId" => $this->variance?->varianceId ?? null,
                // "auctionId" => $this->auctionId,
                "name" => $this->variance?->name ?? null,
                "slug" => $this->variance?->slug ?? null,
                "brandId" => $this->brandId,
                "SKU" => $this->variance?->SKU ?? null,
                "type" => $this->type,
                // "stockraw" => $this->activeStock,
                "stock" => StockResource::generate($this->variance?->activeStock ?? null),
                "media" => MediaResource::generate($this->variance),
            ],
            "maxPrice" => $this->currencyPriceResource($this->maxPrice),
            "minPrice" => $this->currencyPriceResource($this->minPrice),
            "hasOffer" => (bool) $this->hasOffer,
            "hasStock" => (bool) $this->hasStock,
            "maxPriceBeforeOffer" => $this->currencyPriceResource($this->maxPriceBeforeOffer),
            "minPriceBeforeOffer" => $this->currencyPriceResource($this->minPriceBeforeOffer),
            "stock" => StockResource::generate($this?->activeStock ?? null),

        ];

        return $response;
    }
}