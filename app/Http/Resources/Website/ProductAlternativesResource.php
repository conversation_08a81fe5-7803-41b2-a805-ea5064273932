<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use Illuminate\Http\Resources\MissingValue;
use App\Http\Resources\General\MediaResource;
use App\Http\Resources\Website\AttributeResource;

class ProductAlternativesResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {
     

        $response = parent::toArray($request);
    
        if($this->whenLoaded('variance') && $this->variance !== null){
        
            $response= [
                "alternativeId" =>  $this->alternativeId,
                "varianceId" =>  $this->varianceId,
                "auctionId" =>  $this->auctionId,
                "name" =>  $this->variance->name,
                "slug" =>  $this->variance->slug,
                "brandId" =>  $this->variance->brandId,
                "SKU" =>  $this->variance->SKU,
                "type" =>  $this->variance->type,
                "stock"=> StockResource::generate($this->variance->stocks->first()),
                'attributes'=>   AttributeResource::generate($this->variance->attributes),
    
            ];

            if($this->whenLoaded('media')){
                $response["media"] = MediaResource::generate($this);
            }
           
            return  $response;

        }   


        
        return  $response   ;
    }
}