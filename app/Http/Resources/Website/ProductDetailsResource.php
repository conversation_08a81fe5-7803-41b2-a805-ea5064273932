<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class ProductDetailsResource extends ApiJsonResource
{
        /**
         * @param $request
         * @return array
         */
        public function toArray($request): array
        {

                $response = [
                        "productId" => $this->productId,
                        "name" => $this->name,
                        "brandId" => $this->brandId,
                        "brand" => BrandResource::generate($this->whenLoaded('brand')),
                        "description" => $this->description,
                        "type" => $this->type,
                        "slug" => $this->slug,
                        "isListed" => $this->isListed,
                        "SKU" => $this->SKU,
                        "avgRate" => $this->avgRate ?? 0,
                        "productGroupId" => $this->productGroupId,
                        "categories" => CategoryResource::generate($this->whenLoaded('categories')),
                        "shipping" => ShippingResource::generate($this->whenLoaded('shipping')),
                        "isPublished" => $this->isPublished,
                        "releaseAt" => $this->releaseAt,
                        "shippingId" => $this->shippingId,
                        "publishedAt" => $this->publishedAt,
                        "unPublishedAt" => $this->unPublishedAt,
                        "metaTitle" => $this->metaTitle,
                        "metaDescription" => $this->metaDescription,
                        "media" => $this->whenLoaded('media', function () {
                                return $this->media->isNotEmpty()
                                        ? MediaResource::generate($this)
                                        : [];
                        }),

                ];

                if ($this->type == 'alternative') {
                        $response["variance"] = ProductVarianceResource::generate($this->whenLoaded('variance'));
                        $response["variances"] = ProductVarianceResource::generate($this->whenLoaded('variances'));
                } elseif ($this->type == 'bundle') {
                        $response["bundles"] = ProductBundlesResource::generate($this->whenLoaded('bundles'));
                        $response["stock"] = StockResource::generate($this->whenLoaded('activeStock'));
                } else {
                        $response["variance"] = ProductVarianceResource::generate($this->whenLoaded('variance'));
                        $response["variances"] = [];

                        $response['metaTitle'] = $this->variances[0]?->metaTitle ?? '';
                        $response['metaDescription'] = $this->variances[0]?->metaDescription ?? '';
                }

                $response['variationAttributes'] = ProductVariationsAttributeResource::generate($this->variationsAttributes);


                // $response['attributes'] = AttributeProductResource::generate($this->whenLoaded('attributes'));
                $response['attributes'] = AttributeProductResource::generate($this->whenLoaded('attributesWithValue'));

                $response['paymentMethods'] = PaymentMethodResource::generate($this->paymentMethods);

                return $response;
        }
}