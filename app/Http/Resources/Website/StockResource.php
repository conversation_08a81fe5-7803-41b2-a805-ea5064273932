<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\PriceResource;

class StockResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        //  $response = parent::toArray($request);

        // dd($this);
        return [
            "stockId" => $this->stockId,
            "quantity" => $this->quantity,
            "maxPerUser" => $this->maxPerUser,
            "supplierId" => $this->supplierId,
            "isOffer" => $this->isOffer,

            // "priceBeforeOfferId" => $this->priceBeforeOfferId,
            "priceBeforeOffer" => $this->currencyPriceResource($this->priceBeforeOffer),
            // "priceId" => $this->priceId,
            "price" => $this->currencyPriceResource($this->price),
            "unPublishedAt" => $this->unPublishedAt, //when offer expires
            "isPreOrder" => $this->isPreOrder,
            "note" => $this->note,

        ];




        // return $response;
    }
}