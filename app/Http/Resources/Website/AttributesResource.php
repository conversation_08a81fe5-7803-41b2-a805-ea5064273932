<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class AttributesResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
        $response['options'] = AttributesOptionResource::generate($this->whenLoaded('options'));
        return $response;

    }
}