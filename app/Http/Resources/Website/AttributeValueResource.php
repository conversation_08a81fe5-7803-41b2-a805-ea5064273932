<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;


class AttributeValueResource extends ApiJsonResource
{

    public function toArray($request)
    {


        return [
            'text' => $this['option_name'] ?? '',
            'value' => $this['attributeOptionId'] ?? '',
            'meta' => [
                'slug' => $this['option_slug'] ?? '',
            ],
        ];


    }
}
