<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class AttributeOptionsAvailableResource extends ApiJsonResource
{
    public function toArray($request)
    {

        return [
            'attributeOptionId' => $this->resource['attributeOptionId'] ?? null,
            'attributeId' => $this->resource['attributeId'] ?? null,
            'type' => $this->resource['attribute']['type'] ?? null,
            'key' => $this->resource['attribute']['key'] ?? null,
        ];
    }
}