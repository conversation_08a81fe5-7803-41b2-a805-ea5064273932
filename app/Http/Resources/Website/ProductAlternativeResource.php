<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class ProductAlternativeResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        return [
            "alternativeId" => $this->alternativeId,
            "varianceId" => $this->varianceId,
            "auctionId" => $this->auctionId,
            "name" => $this->variance->name,
            "slug" => $this->variance->slug,
            "brandId" => $this->variance->brandId,
            "SKU" => $this->variance->SKU,
            "type" => $this->variance->type,
            "stock" => StockResource::generate($this->whenLoaded('activeStock')),
        ];



    }
}
