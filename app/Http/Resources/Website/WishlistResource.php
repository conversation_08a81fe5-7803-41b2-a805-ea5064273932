<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class WishlistResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        //parent::toArray($request);

        $response = [
            "wishlistId" => $this->wishlistId,
            "productId" => $this->product->productId,
            "name" => $this->product->name,
            "brandId" => $this->brandId,
            "productGroupId" => $this->product->productGroupId,
            "description" => $this->product->description,
            "type" => $this->product->type,
            "slug" => $this->product->slug,
            "isListed" => $this->product->isListed,
            "SKU" => $this->product->SKU,
            "variationAttributes" => $this->product->variationAttributes,
            "avgRate" => $this->product->avgRate ?? 0,


        ];


        if ($this->relationLoaded('product')) {
            if ($this->product->relationLoaded('brand')) {
                $response['brand'] = new BrandResource($this->product->brand);
            }

            if ($this->product->relationLoaded('categories')) {
                $response['categories'] = CategoryResource::generate($this->product->categories);
            }
            if ($this->product->type == 'bundle' && $this->product->relationLoaded('bundle')) {
                $response['bundle'] = new ProductBundleResource($this->product->bundle);
            } else {
                $response['variance'] = new ProductDefaultResource($this->product->variance);
            }


            $response['media'] = MediaResource::generate($this->product);


        }


        return $response;


    }
}