<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class OrderRateResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        return [
            'orderRatingId' => $this->orderRatingId,
            'review' => $this->review,
            'userId' => $this->userId,
            'status' => $this->status,
            'orderId' => $this->orderId,
            'user' => SimpleUserResource::generate($this->whenLoaded('user')),

        ];



    }

}