<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaCollectionResource;


class VariancesCompareResource extends ApiJsonResource
{
  /**
   * @param $request
   * @return array
   */
  public function toArray($request): array
  {

    return [
      'varianceId' => $this['varianceId'],
      'productId' => $this['productId'],
      'name' => $this['name'],
      'slug' => $this['slug'],
      'varianceName' => $this['varianceName'],
      'varianceSlug' => $this['varianceSlug'],
      "media" => [
        "gallery" => MediaCollectionResource::generate($this['gallery']),
      ],
      "stock" => StockResource::generate($this['stock']),


    ];
  }
}