<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class BrandResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        // $response = parent::toArray($request);
        return [
            "brandId" => $this->brandId,
            "name" => $this->name,
            "slug" => $this->slug,
            "metaTitle" => $this->metaTitle,
            "metaDescription" => $this->metaDescription,
            "media" => $this->whenLoaded('media', function () {
                return $this->media->isNotEmpty()
                    ? MediaResource::generate($this)
                    : [];
            }),

        ];




    }
}