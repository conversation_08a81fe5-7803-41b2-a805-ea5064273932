<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class AlternativeResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
      //  $response['variance'] = new VarianceResource($this->whenLoaded('variance'));
        return $response;

    }
}