<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaCollectionResource;

class OptionsResource extends ApiJsonResource
{

    public function toArray($request)
    {



        return [
            'attributeOptionId' => $this['attributeOptionId'],
            'hexCode' => $this['option_hexCode'],
            'number' => $this['option_number'],
            'name' => $this['option_name'],
            'slug' => $this['option_slug'],
            'value' => $this['value'],
            'variance' => isset($this['variance'])
                ? new AttributeOptionsStockResource($this['variance'])
                : null,
            'stock' => isset($this['activeStock'])
                ? StockResource::generate($this['activeStock'] ?? null)
                : null,
            "media" => [
                "cover" => MediaCollectionResource::generate($this['cover'] ?? null),
                "gallery" => MediaCollectionResource::generate($this['gallery'] ?? null),
            ],
        ];
    }
}