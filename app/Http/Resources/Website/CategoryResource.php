<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\General\MediaResource;
use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaCollectionResource;

class CategoryResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        // $response = parent::toArray($request);

        return [
            "categoryId" => $this->categoryId,
            "name" => $this->name,
            "slug" => $this->slug,
            "parentId" => $this->parentId,
            "parent" => self::generate($this->whenLoaded(relationship: 'parent')),
            "publishedAt" => $this->publishedAt,
            "metaTitle" => $this->metaTitle,
            "metaDescription" => $this->metaDescription,
            "isShowBrandIngListing" => $this->isShowBrandIngListing,
            "brands" => BrandResource::generate($this->whenLoaded('brands')),
            "attributes" => AttributesResource::generate($this->whenLoaded('attributes')),
            "children" => self::generate($this->whenLoaded('children')),
            "media" => $this->whenLoaded('media', function () {
                return $this->media->isNotEmpty()
                    ? MediaResource::generate($this)
                    : [];
            }),
            "cover" => MediaCollectionResource::generate($this->whenLoaded('cover')),

        ];

    }
}