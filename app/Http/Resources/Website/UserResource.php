<?php

namespace App\Http\Resources\Website;

use App\Helper\Currency;
use App\Http\Resources\ApiJsonResource;

class UserResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {


        return [
            'userId' => $this->userId,
            'firstName' => $this->firstName,
            'lastName' => $this->lastName,
            'email' => $this->email,
            'emailVerifiedAt' => $this->emailVerifiedAt,
            'birthday' => $this->birthday,
            'status' => $this->status,
            'createdAt' => $this->createdAt,
            'updatedAt' => $this->updatedAt,
            'phone' => $this->phone,
            'gender' => $this->gender,
            'fullName' => $this->fullName,
            'wallet' => $this->priceArray($this->wallet),
            'media' => MediaResource::generate($this),


        ];



    }


    protected function priceArray($price)
    {
        $userCurrency = getCurrency();
        $currency = Currency::convert($price)->fromBase()->to($userCurrency);
        return [
            "value" => $currency->get(),
            "currency" => $userCurrency,
            "symbol" => $currency->symbol()
        ];

    }

}
