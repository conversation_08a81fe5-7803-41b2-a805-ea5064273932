<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;


class ValueAttributeResource extends ApiJsonResource
{

    public function toArray($request)
    {

        return [
            // 'attributeValuesId' => $this->attributeValuesId,

            'attributeOptionId' => $this->attributeOptionId,
            'hexCode' => $this->hexCode,
            'number' => $this->number,
            'name' => $this->option_name,
            'slug' => $this->option_slug,
            'value' => $this->value,
            'extra' => $this->extra


        ];
    }
}
