<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\ApiJsonResource;

class ProductBundleResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {
        $response = parent::toArray($request);
        $response["stock"] = StockResource::generate($this->whenLoaded('stocks')->first());
        return $response;
    }
}