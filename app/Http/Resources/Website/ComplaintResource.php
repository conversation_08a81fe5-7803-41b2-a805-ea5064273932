<?php

namespace App\Http\Resources\Website;

use App\Http\Resources\Auth\RolesResource;
use App\Http\Resources\General\MediaResource;

use App\Http\Resources\ApiJsonResource;

class ComplaintResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);

        return $response;

    }
}