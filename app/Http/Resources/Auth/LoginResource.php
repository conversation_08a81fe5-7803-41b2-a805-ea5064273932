<?php

namespace App\Http\Resources\Auth;

use App\Helper\Currency;
use App\Http\Resources\ApiJsonResource;


class LoginResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $response = parent::toArray($request);
        $response["user"] = UserResource::generate($this['user']);
        // dd($response["user"]);
        // $response["user"]->roles = $this['user']->roles->pluck('name')->toArray();

        // // $response["user"]->roles = RolesResource::generate($this['user']->whenLoaded('roles'));

        // $response["user"]['roles'] = ["sss"];

        // $response['user']->wallet = $this->getWallet($this['user']->wallet);
        return $response;

    }


    // protected function getWallet($wallet)
    // {

    //     $userCurrency = getCurrency();
    //     $currency = Currency::convert($wallet)->fromBase()->to($userCurrency);
    //     return [
    //         "value" => $currency->get(),
    //         "currency" => $userCurrency,
    //         "symbol" => $currency->symbol()
    //     ];

    // }

}
