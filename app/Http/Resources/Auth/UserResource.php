<?php

namespace App\Http\Resources\Auth;

use App\Helper\Currency;
use App\Http\Resources\ApiJsonResource;


class UserResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        $response['wallet'] = $this->getWallet($this->wallet);
        $response["roles"] = RolesResource::generate($this->roles);
        return $response;

    }



    protected function getWallet($wallet)
    {

        $userCurrency = getCurrency();
        $currency = Currency::convert($wallet)->fromBase()->to($userCurrency);
        return [
            "value" => $currency->get(),
            "currency" => $userCurrency,
            "symbol" => $currency->symbol()
        ];

    }



}
