<?php

namespace App\Http\Resources\Lookup;


use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class BrandResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'value' => $this->brandId,
            'text' => $this->name,
            'meta' => [
                'media' => $this->whenLoaded('media') ? MediaResource::generate($this) : [],
                'slug' => $this->slug
            ]

        ];
    }
}