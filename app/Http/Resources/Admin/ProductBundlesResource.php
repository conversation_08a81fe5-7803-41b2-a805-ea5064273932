<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class ProductBundlesResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {
        // $response = parent::toArray($request);


        return [
            'bundleId' => $this->bundleId,
            'varianceId' => $this->varianceId,
            // 'brandId' => $this->variance->brandId,
            // 'SKU' => $this->variance->SKU,
            // 'slug' => $this->variance->slug,
            'name' => $this->variance?->name ?? [],
            'description' => $this->variance->description ?? [],
            'isDefault' => $this->variance?->isDefault ?? 0,
            'productId' => $this->variance?->productId ?? null,
            'media' => $this->whenLoaded('variance.media') ? MediaResource::generate($this->variance) : [],
        ];

    }
}