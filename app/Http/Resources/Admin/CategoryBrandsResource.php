<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
class  CategoryBrandsResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
        $response['brand'] = $this->brand;
        return $response;
    }
}
