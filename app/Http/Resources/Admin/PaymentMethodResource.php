<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\General\MediaResource;
use App\Http\Resources\ApiJsonResource;

class PaymentMethodResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        return $this->paymentMethodId;
        // $response = parent::toArray($request);
        // if ($this->whenLoaded('media')) {
        //     $response["media"] = MediaResource::generate($this);
        // }

        //return $response;

    }
}