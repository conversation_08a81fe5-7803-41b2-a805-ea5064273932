<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;

class PriceResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $response = parent::toArray($request);
        $response["value"] = $this->price;
        $response["priceId"] = $this->priceId;
        $response["currencyId"] = $this->currencyId;

        return $response;
    }
}
