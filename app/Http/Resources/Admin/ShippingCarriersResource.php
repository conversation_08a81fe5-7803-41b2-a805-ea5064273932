<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\Admin\AddressResource;


class ShippingCarriersResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);

        $response['prices'] = ShippingCarrierPriceCity::generate($this->whenLoaded('priceCities'));

        return $response;
    }

}