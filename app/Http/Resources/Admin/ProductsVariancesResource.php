<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;
use App\Http\Resources\Admin\AttributeVarianceResource;

class ProductsVariancesResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        $response['isPublished'] = (bool) $response['isPublished'];
        unset($response['attributesWithValue']);
        $response['attributes'] = AttributeVarianceResource::generate($this->whenLoaded('attributesWithValue'));
        $response['stocks'] = StockResource::generate($this->whenLoaded('stocks'));
        $response["media"] = $this->whenLoaded('media') ? MediaResource::generate($this) : [];
        return $response;
    }
}