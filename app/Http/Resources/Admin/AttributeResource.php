<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;

class AttributeResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        $response['attributesValuesUsed'] = AttributeValuesResource::generate($this->whenLoaded('attributesValuesUsed'));
  
        return $response;
    }
}
