<?php

namespace App\Http\Resources\Admin;


use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\Website\UserResource;

class ActivityLogResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $response = parent::toArray($request);
        /* example
        {
            "id": 2,
            "log_name": "default",
            "description": "created",
            "subject_type": "App\\Models\\Supplier",
            "event": "created",
            "subject_id": 4,
            "causer_type": null,
            "causer_id": null,
            "properties": [],
            "batch_uuid": null,
            "created_at": "2023-10-21T06:28:47.000000Z",
            "updated_at": "2023-10-21T06:28:47.000000Z",
            "causer": null,
            "subject": {
                "supplierId": 4,
                "name": "ارامكس",
                "createdAt": "2023-10-21T06:28:47.000000Z",
                "updatedAt": "2023-10-21T07:01:29.000000Z"
            }*/

            //camelCase response

        return [
            'id' => $response['id'],
            'logName' => $response['log_name'],
            'description' => $response['description'],
            'subjectType' => $response['subject_type'],
            'event' => $response['event'],
            'subjectId' => $response['subject_id'],
            'causerType' => $response['causer_type'],
            'causerId' => $response['causer_id'],
            'properties' => $response['properties'],
            'batchUuid' => $response['batch_uuid'],
            'createdAt' => $response['created_at'],
            'updatedAt' => $response['updated_at'],
            'causer' => UserResource::generate($this->whenLoaded('causer')),
            'subject' => $response['subject'],
        ];
    }
}
