<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\PriceResource;

class OrderItemResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response['orderItemsId'] = $this->orderItemsId;
        $response['orderId'] = $this->orderId;
        $response['model_type'] = $this->model_type;
        $response['model_id'] = $this->model_id;
        $response['snapshot'] = $this->model?->snapshot ?? [];
        $response['productOrderItemId'] = $this->model?->productOrderItemId ?? null;
        $response['productId'] = $this->model?->productId ?? null;
        $response['varianceId'] = $this->model?->varianceId ?? null;
        $response['quantity'] = $this->model?->quantity ?? null;
        $response['status'] = $this->model?->status ?? null;
        $response['cost'] = $this->currencyPriceResource($this->cost);
        $response['price'] = $this->currencyPriceResource($this->model?->price ?? 0);
        $response['product'] = ProductResource::generate($this->model?->product ?? null);
        $response['variance'] = VarianceResource::generate($this->model?->variance ?? null);

        return $response;
    }
}