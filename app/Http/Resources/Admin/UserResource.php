<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class UserResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
        if (isset($this->birthday)) {
            $response['birthday'] = $this->birthday->format('Y-m-d');
        }
        if ($this->whenLoaded('media')) {
            $response["media"] = MediaResource::generate($this);
        }
        return $response;
    }
}