<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;

class RatingResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
        $response['product'] = ProductResource::generate($this->whenLoaded('product'));
        return $response;
    }
}