<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaCollectionResource;

class VarianceResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        unset($response['gallery']);
        $response['attributes'] = AttributeResource::generate($this->whenLoaded('attributes'));
        $response['media']['gallery'] = MediaCollectionResource::generate($this->whenLoaded('gallery'));
        $response['stocks'] = StockResource::generate($this->whenLoaded('stocks'));
        return $response;
    }
}