<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;


class ShippingResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        $response['isPublished'] = (bool) $this->isPublished;
        $response['shippingCarriersMethod'] = ShippingCarrierMethodResource::generate($this->whenLoaded('shippingCarriersMethod'));
        return $response;
    }
}