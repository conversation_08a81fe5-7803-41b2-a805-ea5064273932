<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;


class AttributeProductResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        return [
            'attributeValuesId' => $this->attributeValuesId,
            'attributeId' => $this->attributeId,
            'attributeOptionId' => $this->attributeOptionId ?? $this->value,
            'value' => $this->value,
            'extra' => $this->extra
        ];


    }
}