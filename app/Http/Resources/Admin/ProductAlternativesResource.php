<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;

class ProductAlternativesResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request)
    {
  
        $response = parent::toArray($request);
        $response =array_merge($response, $this->variance->toArray()); ; 
        unset($response['variance']);
        return  $response   ;
    }
}