<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\General\PriceResource;
use App\Http\Resources\ApiJsonResource;


class UsedProductDetailsResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        $response = parent::toArray($request);
        $response['isPublished'] = (bool) $this->isPublished;
        unset($response['attributesWithValue']);


        $response['attributes'] = AttributeProductResource::generate($this->whenLoaded('attributesWithValue'));
        $response['city'] = CityResource::generate($this->whenLoaded('city'));
        $response['brand'] = BrandResource::generate($this->whenLoaded('brand'));
        $response['price'] = $this->currencyPriceResource($this->price);
        $response['user'] = UserResource::generate($this->whenLoaded('user'));
        // if ($this->whenLoaded('media')) {
        //     $response["media"] = MediaResource::generate($this);
        // }


        return $response;
    }
}