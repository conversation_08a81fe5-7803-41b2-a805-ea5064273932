<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\General\MediaResource;
use App\Http\Resources\ApiJsonResource;

class PaymentMethodDetailsResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        if (isset($response['media'])) {

            $response['media'] = MediaResource::generate($this);
        }
        return $response;

    }
}