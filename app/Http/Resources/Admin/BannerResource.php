<?php

namespace App\Http\Resources\Admin;



use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;
class BannerResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        if ($this->whenLoaded('media')) {
            $response["media"] = MediaResource::generate($this);
        }


        return $response;
    }
}