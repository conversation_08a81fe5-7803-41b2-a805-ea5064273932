<?php

namespace App\Http\Resources\Admin;


use App\Http\Resources\General\MediaModelResource;

use App\Http\Resources\ApiJsonResource;

class BrandResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        $response['isPublished'] = (bool) $this->isPublished;

        unset($response['logo']);
        unset($response['logoName']);

        $response['media']['logo'] = MediaModelResource::generate($this->whenLoaded('logo'));
        $response['media']['logoName'] = MediaModelResource::generate($this->whenLoaded('logoName'));

        $response['labels'] = LabelIdResource::generate($this->whenLoaded('brandLabels'));
        return $response;
    }
}