<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;


class AttributeVarianceResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {

        return [
            'attributeId' => $this->attributeId,
            'attributeOptionId' => $this->attributeOptionId,
            'extra' => $this->extra


        ];


    }
}