<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;


class StockResource extends ApiJsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $response = parent::toArray($request);
        $response['price'] = $this->currencyPriceResource($this->price);
        $response["cost"] = $this->currencyPriceResource($this->cost);
        $response["priceBeforeOffer"] = $this->currencyPriceResource($this->priceBeforeOffer);
        return $response;
    }
}
