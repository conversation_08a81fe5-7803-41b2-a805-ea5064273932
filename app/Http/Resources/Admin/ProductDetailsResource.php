<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;
use App\Models\Label;

class ProductDetailsResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $response = parent::toArray($request);
        //dd($this->variances);

        if ($this->type == 'bundle') {
            $response['bundles'] = ProductBundlesResource::generate($this->whenLoaded('bundles'));
            $response['stocks'] = StockResource::generate($this->whenLoaded('stocks'));

        } else {
            $response['variances'] = ProductsVariancesResource::generate($this->whenLoaded('variances'));
        }
        if ($this->type == 'simple') {
            $response['SKU'] = $this->variances[0]->SKU ?? 0;
            $response['modelNumber'] = $this->variances[0]->modelNumber ?? 0;
            $response['stocks'] = $this->whenLoaded('variances') && isset($this->variances[0]->stocks) ? StockResource::generate($this->variances[0]->stocks) : [];

            $this->variances[0]->translatable = [];

            $response['metaTitle'] = $this->variances[0]->metaTitle ?? ["ar" => "", "en" => ""];
            $response['metaDescription'] = $this->variances[0]->metaDescription ?? ["ar" => "", "en" => ""];
        }

        $response['variationAttributes'] = !is_null($this->variationAttributes) && $this->variationAttributes != "" ? array_map(function ($variationAttribute) {
            return (int) $variationAttribute;
        }, explode(',', $response['variationAttributes'])) : [];

        $response["media"] = $this->whenLoaded('media') ? MediaResource::generate($this) : [];



        $response['categories'] = $this->whenLoaded('categories', $this->categories->pluck('categoryId')->toArray());
        $response['related'] = ProductRelatedResource::generate($this->whenLoaded('related'));
        $response['suggested'] = ProductSuggestedResource::generate($this->whenLoaded('suggested'));
        $response['ratings'] = ProductRatingsResource::generate($this->whenLoaded('ratings'));

        unset($response['attributesWithValue']);
        $response['attributes'] = AttributeProductResource::generate($this->whenLoaded('attributesWithValue'));

        $response['disabledPaymentMethods'] = PaymentMethodResource::generate($this->whenLoaded('disabledPaymentMethods'));
        $response['shippingCarriers'] = ShippingCarrierResource::generate($this->whenLoaded('shippingCarriers'));

        $response['labels'] = LabelIdResource::generate($this->whenLoaded('productLabels'));



        return $response;

    }
}