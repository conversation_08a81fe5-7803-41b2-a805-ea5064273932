<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class AttributeAdminResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $response = parent::toArray($request);

        $result['attributeId'] = $response['attributeId'];
        
        $attributeValue = $this->whenLoaded('varianceAttributesValue');

        if ($attributeValue && !$attributeValue instanceof \Illuminate\Http\Resources\MissingValue) {

            $attributeValue = $attributeValue->toArray();
            //dd($attributeValue);
            $result['attributeOptionId'] = ($attributeValue['attributeOptionId']) ? $attributeValue['attributeOptionId'] : $attributeValue['value'];
        }

        $attributeValue = $this->whenLoaded('productAttributesValue');

        if ($attributeValue && !$attributeValue instanceof \Illuminate\Http\Resources\MissingValue) {

            $attributeValue = $attributeValue->toArray();
            //dd($attributeValue);
            $result['attributeOptionId'] = ($attributeValue['attributeOptionId']) ? $attributeValue['attributeOptionId'] : $attributeValue['value'];
        }



        return $result;

    }
}
