<?php


namespace App\Http\Resources\Admin;

use App\Http\Resources\ApiJsonResource;
use App\Http\Resources\General\MediaResource;

class UsedCategoryResource extends ApiJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $response = parent::toArray($request);

        $response['usedCategoriesBrands'] = UsedCategoriesBrandResource::generate($this->whenLoaded('usedCategoriesBrands'));

        $response['usedCategoriesAttributes'] = UsedCategoriesAttributeResource::generate($this->whenLoaded('usedCategoriesAttributes'));

        if ($this->whenLoaded('media')) {
            $response["media"] = MediaResource::generate($this);
        }

        return $response;
    }
}