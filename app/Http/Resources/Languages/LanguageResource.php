<?php

namespace App\Http\Resources\Languages;

use App\Http\Resources\ApiJsonResource;

class LanguageResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'abbreviation' => $this->abbreviation,
            'direction' => $this->direction,
            'updatedAt' => $this->updated_at,
            'createdAt' => $this->created_at,

        ];
    }
}