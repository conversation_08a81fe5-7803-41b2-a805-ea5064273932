<?php

namespace App\Http\Resources\TranslationTag;

use App\Http\Resources\ApiJsonResource;

class TranslationTagsResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'updatedAt' => $this->updatedAt,
            'createdAt' => $this->createdAt,
            
        ];
    }
}
