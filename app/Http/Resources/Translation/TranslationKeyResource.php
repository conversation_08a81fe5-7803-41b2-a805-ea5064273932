<?php

namespace App\Http\Resources\Translation;


use App\Http\Resources\ApiJsonResource;
use Illuminate\Support\Arr;

class TranslationKeyResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'group' => $this->group,
            'key' => $this->key,
            'values' => TranslationValueResource::generate($this->whenLoaded('values')),
            'languages' => LanguageHasTranslationResource::generate($this->whenLoaded('languages')),
            'tags' => TranslationTagResource::generate($this->whenLoaded('tags')),
            'placeholders' => array_key_exists('text', $this->placeholders) ? Arr::wrap([$this->placeholders]
            ) : $this->placeholders,
            'isPublished' => (bool)$this->publishedAt,
            'publishedAt' => $this->publishedAt,
        ];
    }
}
