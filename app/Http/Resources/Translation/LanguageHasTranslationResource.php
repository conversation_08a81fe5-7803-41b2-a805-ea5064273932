<?php

namespace App\Http\Resources\Translation;

use App\Http\Resources\ApiJsonResource;
use App\Models\Translations\LanguageHasTranslation;

class LanguageHasTranslationResource extends ApiJsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'language_id' => $this->whenPivotLoaded(new LanguageHasTranslation, fn() => $this->pivot->language_id),
            'abbreviation' => $this->abbreviation,
            'direction' => $this->direction,
            'value' => $this->whenPivotLoaded(new LanguageHasTranslation, fn() => $this->pivot->value?->value),
        ];
    }
}