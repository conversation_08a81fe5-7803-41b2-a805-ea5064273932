<?php

namespace App\Http\Resources;

use App\Helper\Currency;
use App\Http\Resources\PaginationCollection;
use Illuminate\Contracts\Auth\Access\Gate;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ApiJsonResource extends JsonResource
{
    public static $wrap = '';

    /**
     * @var bool
     */
    protected bool $withDetails = false;

    /**
     * @param bool $withDetails
     * @return ApiJsonResource
     */
    public function withDetails(bool $withDetails = true): static
    {
        $this->withDetails = $withDetails;

        return $this;
    }

    public static function generate($resource, $filtersModel = null)
    {

        if (is_array($resource) && array_filter($resource, 'is_integer')) {
            return $resource;
        }

        if (is_int($resource)) {
            return $resource;
        }

        if ($resource instanceof EloquentCollection || $resource instanceof Collection) {
            return parent::collection($resource);
        }

        if ($resource instanceof LengthAwarePaginator) {
            return new PaginationCollection($resource, static::class, $filtersModel);
        }


        return parent::make($resource);
    }

    protected function whenLoaded($relationship, $value = null, $default = null)
    {
        if (request()->has('ids')) {
            $onlyIdsRelationships = request()->string('ids')->explode(',')->toArray();

            if (!$this->resource->relationLoaded($relationship)) {
                return value($default);
            }

            $relationship = str($relationship)->snake()->toString();

            if ($relationship == 'websites_relation') {
                $relationship = 'websites';
            } elseif ($relationship == 'countries_relation') {
                $relationship = 'countries';
            }

            if (!in_array($relationship, $onlyIdsRelationships)) {
                $relationship = str($relationship)->camel()->toString();
                $modelName = str($relationship)->ucfirst()->toString();
                try {
                    $model = resolve("App\Models\\$modelName");
                    if (property_exists($model, 'allowPluckPrimaryKey')) {
                        return parent::whenLoaded($relationship)->pluck($model->getKeyName())->toArray();
                    }
                } catch (\Exception $exception) {
                    return parent::whenLoaded(...func_get_args());
                }

                return parent::whenLoaded(...func_get_args());
            }

            if (!in_array($relationship, $onlyIdsRelationships)) {
                $relationship = str($relationship)->camel()->toString();


            }


            // if ($relationship == ('websites')) {
            //     $relationship = 'websites_relation';
            // } elseif ($relationship == 'countries') {
            //     $relationship = 'countries_relation';
            // }

            $relationship = str($relationship)->camel()->toString();

            if ($this->resource->{$relationship} instanceof EloquentCollection) {
                if ($this->resource->{$relationship}?->isEmpty()) {
                    return $this->resource->{$relationship};
                }

                return $this->resource->{$relationship}?->modelKeys();
            }

            return $this->resource->{$relationship}?->id;
        }


        return parent::whenLoaded(...func_get_args());
    }



    public function whenWithIds(string $column)
    {
        return $this->when(request()->has('ids'), $this->whenHas($column));
    }

    // public function __get($key)
    // {
    //     if (in_array($key, ['id', 'created_at', 'updated_at'])) {
    //         return parent::__get($key);
    //     }
    //
    //     if (is_local_env()) {
    //         $value = parent::__get($key);
    //
    //         if (is_array($value)) {
    //             return $this->when(
    //                 $this->checkPermission("list.$key"),
    //                 fn() => parent::__get($key),
    //                 fn() => collect($value)->map(fn($item) => Str::mask($item, '*', (Str::length($item) > 2 ? 2 : 0))
    //                 )->toArray()
    //             );
    //         }
    //
    //         return $this->when(
    //             $this->checkPermission("list.$key"),
    //             fn() => parent::__get($key),
    //             fn() => Str::mask($value, '*', (Str::length($value) > 2 ? 2 : 0))
    //         );
    //     }
    //
    //     return $this->when(
    //         $this->checkPermission("list.$key"),
    //         fn() => parent::__get($key)
    //     );
    // }

    private function checkPermission(string $permission): bool
    {
        return (bool) resolve(Gate::class)->raw("{$this->permissionsResource}.{$permission}");
    }


    public function currencyPriceResource($column)
    {

        if (is_null($column)) {
            return null;
        }
        $userCurrency = getCurrency();

        $currency = Currency::convert($column)->fromBase()->to($userCurrency);


        return [
            "currencyId" => $currency->getCurrencyId(),
            "value" => $currency->get(),
            "currency" => $userCurrency,
            // "price" => $this->price,
            // "basePrice" => $this->basePrice,
            // "currency" => $this->currency->name,
            "symbol" => $currency->symbol()
        ];


    }
}