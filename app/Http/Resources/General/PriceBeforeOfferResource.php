<?php

namespace App\Http\Resources\General;

use App\Http\Resources\ApiJsonResource;

class PriceBeforeOfferResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        //! TODO no need to a new resource, just use PriceResource
        // $response = parent::toArray($request);
        return [
            "value" => $this->price,
            "price" => $this->price,
            "basePrice" => $this->basePrice,
            "currency" => $this->currency->symbol
        ];




        // return $response;
    }
}
