<?php

namespace App\Http\Resources\General;

use App\Helper\Currency;
use App\Http\Resources\ApiJsonResource;
use Illuminate\Http\Request;

class PriceResource extends ApiJsonResource
{
    /**
     * @param $request
     * @return array
     */
    public function toArray($request): array
    {
        // $response = parent::toArray($request);


        $userCurrency = getCurrency();

        $currency = Currency::convert($this->basePrice)->fromBase()->to($userCurrency);
        return [
            "value" => $currency->get(),
            "currency" => $userCurrency,
            // "price" => $this->price,
            // "basePrice" => $this->basePrice,
            // "currency" => $this->currency->name,
            "symbol" => $currency->symbol()
        ];





        // return $response;
    }
}
