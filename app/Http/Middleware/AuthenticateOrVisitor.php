<?php

namespace App\Http\Middleware;

use App\Models\Visitor;
use Closure;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AuthenticateOrVisitor
{
    use ApiResponser;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated via API guard
        $isAuthenticated = auth(GUARD_API)->check();

        // Check if valid visitor token exists
        $hasVisitorToken = $request->hasHeader('visitor-id')
            && !empty($request->header('visitor-id'));

        // If neither authentication method is valid
        if (!$isAuthenticated && !$hasVisitorToken) {
            return $this->sendError([], __('messages.unauthorized'), 401);
        }

        // If authenticated user exists, proceed
        if ($isAuthenticated) {
            return $next($request);
        }

        // Handle visitor token validation
        if ($hasVisitorToken) {

            // If no Authorization header, create auth client for visitor
            if (!$isAuthenticated) {
                Visitor::makeAuthClient();
            }
        }

        return $next($request);
    }

    /**
     * Validate visitor token
     * 
     * @param string $visitorId
     * @return bool
     */
    protected function isValidVisitorToken(string $visitorKey): bool
    {
        // Add your validation logic here
        // For example, check if token exists in database and isn't expired
        return Visitor::where('visitorKey', $visitorKey)->exists();
    }
}