<?php

namespace App\Http\Middleware;


use Closure;
use Illuminate\Http\Request;


class Localization
{

    public $language = ['en', 'ar'];
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  ...$guards
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, ...$guards)
    {

        $locale = ($request->hasHeader("language") && in_array($request->hasHeader("language"), $this->language)) ? $request->header("language") : "ar";
        // set laravel localization
        app()->setLocale($locale);

        return $next($request);
    }
}