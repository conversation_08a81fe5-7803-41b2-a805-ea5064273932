<?php

namespace App\Http\Controllers\General;

use App\Enums\PaymentMethodEnum;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\PaymentMethod\PaymentGatewayFactory;
use Illuminate\Http\Request;

class PaymentController extends ApiBaseController
{
    /**
     * Handle the upload media request.
     * @return JsonResponse
     */


    public function __invoke(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $paymentGateway = PaymentGatewayFactory::createPaymentGateway(PaymentMethodEnum::hyperpay->value);
            $paymentGateway->hookPayment($params);
            return $this->sendSuccess([]);
        } catch (\Exception $e) {

            throw new \Exception($e->getMessage());
        }

    }




}