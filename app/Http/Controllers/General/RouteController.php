<?php

namespace App\Http\Controllers\General;

use App\Helper\RouteHelper;
use Illuminate\Routing\Router;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\General\RouteResource;

class RouteController extends ApiBaseController
{
    /**
     * Handle the upload media request.
     * @return JsonResponse
     */

    protected $router;

    public function __construct(Router $router)
    {
        $this->router = $router;
    }
    public function index(): JsonResponse
    {

        $routes = $this->router->getRoutes();

        $routeList = filter_routes_app($routes);
        return $this->sendSuccess(RouteResource::generate($routeList));

    }



    public function routesAdmin()
    {
        return $this->sendSuccess(RouteResource::generate(RouteHelper::getRoutesAdmin()));
    }
}