<?php

namespace App\Http\Controllers\General;

use App\Helper\FileHelper;
use App\Models\Media;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\General\UploadMediaRequest;
use App\Http\Resources\General\UploadMediaResource;

class UploadMediaController extends ApiBaseController
{


    public function __construct()
    {
        $this->middleware(['auth:api'])->only(['deleteMediaById']);
    }
    /**
     * Handle the upload media request.
     *
     * @param UploadMediaRequest $request
     * @param FileHelper $fileHelper
     * @return JsonResponse
     */
    public function upload(UploadMediaRequest $request, FileHelper $fileHelper): JsonResponse
    {

        $data = $fileHelper->store($request, $fileHelper->getDefaultTempPath());
        return $this->sendSuccess(UploadMediaResource::make($data));
    }


    public function deleteMediaById(int $id): JsonResponse
    {

        Media::where('id', $id)->delete();

        return $this->sendSuccess();
    }



}