<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\ShippingRequest;
use App\Http\Requests\Admin\ShippingCarriersRequest;
use App\Http\Resources\Admin\ShippingCarriersResource;
use App\Repositories\ShippingCarrier\ShippingCarrierRepositoryInterface;

class ShippingCarriersController extends ApiBaseController
{
    /**
     * @param  ShippingCarrierRepositoryInterface $shippingCarriersRepository
     */
    public function __construct(protected ShippingCarrierRepositoryInterface $shippingCarriersRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Shippings .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->shippingCarriersRepository->getAllShippingCarriers();
        return $this->sendSuccess(ShippingCarriersResource::generate($data));
    }
    /**
     * Store a new shipping record .
     * @param ShippingRequest $request
     * @return JsonResponse
     */
    public function store(ShippingCarriersRequest $request)
    {
        $data = $this->shippingCarriersRepository->storeShippingCarriers($request->all());
        return $this->sendSuccess(ShippingCarriersResource::generate($data));
    }
    /**
     * Show Shipping by ID .
     * @param int|string $shippingCarriersId
     * @return JsonResponse
     */
    public function show(int|string $shippingCarriersId): JsonResponse
    {

        $data = $this->shippingCarriersRepository->findByIdShippingCarriers($shippingCarriersId);
        return $this->sendSuccess(data: ShippingCarriersResource::generate($data));
    }

    /**
     * Update  record .
     *
     * @group Shippings
     * @param ShippingRequest $request
     * @param int|string  $shippingCarriersId
     * @return JsonResponse
     */
    public function update(ShippingCarriersRequest $request, int|string $shippingCarriersId): JsonResponse
    {
        $data = $this->shippingCarriersRepository->updateShippingCarriers($shippingCarriersId, $request->all());
        return $this->sendSuccess(ShippingCarriersResource::generate($data));
    }

    /**
     * Delete Shipping record .
     * @param int|string $shippingCarriersId
     * @return JsonResponse
     */

    public function destroy(int|string $shippingCarriersId)
    {
        $this->shippingCarriersRepository->deleteShippingCarriers($shippingCarriersId);
        return $this->sendSuccess();
    }


    public function setShippingForAllProduct(int|string $shippingId)
    {
        $this->shippingCarriersRepository->setShippingForAllProduct($shippingId);
        return $this->sendSuccess();
    }
}