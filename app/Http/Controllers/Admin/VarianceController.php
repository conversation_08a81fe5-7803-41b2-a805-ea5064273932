<?php

namespace App\Http\Controllers\Admin;


use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\VarianceRequest;
use App\Repositories\Variance\VarianceRepositoryInterface;
use App\Http\Resources\Admin\VarianceResource;
use Illuminate\Http\JsonResponse;

class VarianceController extends ApiBaseController
{
    /**
     * @param  VarianceRepositoryInterface $varianceRepository
     */
    public function __construct(protected VarianceRepositoryInterface $varianceRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all variances .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->varianceRepository->getAllVariances();
        return $this->sendSuccess(VarianceResource::generate($data));
    }
    /**
     * Store a new Variance record .
     * @param VarianceRequest $request
     * @return JsonResponse
     */
    public function store(VarianceRequest $request): JsonResponse
    {
        $data = $this->varianceRepository->storeVariance($request->validated());
        return $this->sendSuccess(VarianceResource::generate($data));
    }

    /**
     * Show Variance by ID .
     *
     * @group Variances
     * @param int|string $varianceId
     * @return JsonResponse
     */
    public function show(int|string $varianceId): JsonResponse
    {
        $data = $this->varianceRepository->findByIdVariance($varianceId);

        return $this->sendSuccess(VarianceResource::generate($data));
    }
    /**
     * Update Variance record .
     *
     * @group Variances
     * @param VarianceRequest $request
     * @param int|string $varianceId
     * @return JsonResponse
     */
    public function update(VarianceRequest $request, int|string $varianceId): JsonResponse
    {
        $data = $this->varianceRepository->updateVariance($varianceId, $request->validated());

        return $this->sendSuccess(VarianceResource::generate($data));
    }
    /**
     * Delete Variance record .
     * @param int|string $varianceId
     * @return JsonResponse
     */
    public function destroy(int|string $varianceId)
    {
        $this->varianceRepository->deleteVariance($varianceId);
        return $this->sendSuccess();
    }

}