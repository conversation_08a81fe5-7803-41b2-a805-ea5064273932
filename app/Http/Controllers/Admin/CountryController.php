<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\CountryRequest;
use App\Http\Resources\Admin\CountryResource;
use App\Repositories\Country\CountryRepositoryInterface;

use Illuminate\Http\JsonResponse;

class CountryController extends ApiBaseController
{
    /**
     * @param  CountryRepositoryInterface $countryRepository
     */
    public function __construct(protected CountryRepositoryInterface $countryRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all Countries .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->countryRepository->getAllCountriesPaginated();
        return $this->sendSuccess(CountryResource::generate($data));
    }
    /**
     * Store a new Country record .
     * @param CountryRequest $request
     * @return JsonResponse
     */
    public function store(CountryRequest $request)
    {
        $data = $this->countryRepository->storeCountry($request->validated());
        return $this->sendSuccess(CountryResource::generate($data));
    }
    /**
     * Show Country by ID .
     * @param int|string $countryId
     * @return JsonResponse
     */
    public function show(int|string $countryId): JsonResponse
    {
        $data = $this->countryRepository->findByIdCountry($countryId);
        return $this->sendSuccess(CountryResource::generate($data));
    }

    /**
     * Update Country record .
     *
     * @group countries
     * @param CountryRequest $request
     * @param int|string  $countryId
     * @return JsonResponse
     */
    public function update(CountryRequest $request, int|string $countryId): JsonResponse
    {
        $data = $this->countryRepository->updateCountry($countryId, $request->validated());
        return $this->sendSuccess(CountryResource::generate($data));
    }

    /**
     * Delete Country record .
     * @param int|string $countryId
     * @return JsonResponse
     */

    public function destroy(int|string $countryId)
    {
        $this->countryRepository->deleteCountry($countryId);
        return $this->sendSuccess();
    }
}