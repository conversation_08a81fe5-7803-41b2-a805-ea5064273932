<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\JsonResponse;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\ComplaintRequest;
use App\Http\Resources\Admin\ComplaintResource;
use App\Repositories\Complaint\ComplaintRepositoryInterface;

class ComplaintController extends ApiBaseController
{
    /**
     * @param  ComplaintRepositoryInterface $complaintRepository
     */
    public function __construct(protected ComplaintRepositoryInterface $complaintRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Pages .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->complaintRepository->getAllLatestPaginatedFiltered();
        return $this->sendSuccess(ComplaintResource::generate($data));
    }
    /**
     * Store a new Page record .
     * @param ComplaintRequest $request
     * @return JsonResponse
     */
    public function store(ComplaintRequest $request)
    {
        $data = $this->complaintRepository->storeComplaint($request->validated());
        return $this->sendSuccess(ComplaintResource::generate($data));
    }
    /**
     * Show Page by ID .
     * @param int|string $complaintId
     * @return JsonResponse
     */
    public function show(int|string $complaintId): JsonResponse
    {
        $data = $this->complaintRepository->query()->findOrFail($complaintId);
        return $this->sendSuccess(ComplaintResource::generate($data));
    }

    /**
     * Update Page record .
     *
     * @param ComplaintRequest $request
     * @param int|string $complaintId
     * @return JsonResponse
     */
    public function update(ComplaintRequest $request, int|string $complaintId): JsonResponse
    {
        $data = $this->complaintRepository->updateComplaint($complaintId, $request->validated());
        return $this->sendSuccess(ComplaintResource::generate($data));
    }

    /**
     * Delete Page record .
     * @param int|string $complaintId
     * @return JsonResponse
     */

    public function destroy(int|string $complaintId)
    {
        $this->complaintRepository->destroy($complaintId);
        return $this->sendSuccess();
    }

}