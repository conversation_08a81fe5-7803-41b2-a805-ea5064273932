<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\BannerRequest;
use App\Http\Resources\Admin\BannerResource;
use App\Repositories\Banner\BannerRepositoryInterface;
use Illuminate\Http\JsonResponse;

class BannerController extends ApiBaseController
{
    /**
     * @param  BannerRepositoryInterface $bannerRepository
     */
    public function __construct(protected BannerRepositoryInterface $bannerRepository)
    {
        //   $this->applyPermissions();
        $this->middleware('role:admin');

    }
    /**
     * Get all Cities.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->bannerRepository->getAllLatestPaginated(with: ['media']);
        return $this->sendSuccess(BannerResource::generate($data));
    }
    /**
     * Store a new City record .
     * @param BannerRequest $request
     * @return JsonResponse
     */
    public function store(BannerRequest $request)
    {
        $data = $this->bannerRepository->storeBanner($request->all());
        return $this->sendSuccess(BannerResource::generate($data));
    }
    /**
     * Show City by ID .
     * @param int|string $bannerId
     * @return JsonResponse
     */
    public function show(int|string $bannerId): JsonResponse
    {
        $data = $this->bannerRepository->findById($bannerId, ['*'], ['media']);
        return $this->sendSuccess(BannerResource::generate($data));
    }

    /**
     * Update City record .
     *
     * @group Cities
     * @param BannerRequest $request
     * @param int|string  $bannerId
     * @return JsonResponse
     */
    public function update(BannerRequest $request, int|string $bannerId): JsonResponse
    {
        $data = $this->bannerRepository->updateBanner($bannerId, $request->all());
        return $this->sendSuccess(BannerResource::generate($data));
    }

    /**
     * Delete City record .
     * @param int|string $cityId
     * @return JsonResponse
     */


    public function destroy(int|string $bannerId)
    {
        $this->bannerRepository->destroy($bannerId);
        return $this->sendSuccess();
    }
}