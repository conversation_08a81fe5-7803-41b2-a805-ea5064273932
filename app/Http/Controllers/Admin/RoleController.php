<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\AssignRoleToUserRequest;
use App\Http\Resources\Admin\RoleUserResource;
use Illuminate\Http\JsonResponse;
use Spatie\Permission\Models\Role;
use App\Http\Requests\Admin\RoleRequest;
use App\Http\Resources\Admin\RoleResource;
use App\Http\Controllers\ApiBaseController;
use App\Repositories\Role\RoleRepositoryInterface;

class RoleController extends ApiBaseController
{
    /**
     * @param  RoleRepositoryInterface $roleRepository
     */
    public function __construct(protected RoleRepositoryInterface $roleRepository)
    {
        // $this->applyPermissions();
        $this->middleware('role:admin');
        // $this->middleware('permission:role-list|role-create|role-edit|role-delete', ['only' => ['index','store']]);
        // $this->middleware('permission:role-create', ['only' => ['create','store']]);
        // $this->middleware('permission:role-edit', ['only' => ['edit','update']]);
        // $this->middleware('permission:role-delete', ['only' => ['destroy']]);
    }
    /**
     * Get all Roles .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->roleRepository->getAllRoles();
        return $this->sendSuccess(RoleResource::generate($data));
    }
    /**
     * Delete Role record .
     * @param int|string $roleId
     * @return JsonResponse
     */

    public function destroy(int|string $roleId)
    {
        $this->roleRepository->deleteRole($roleId);
        return $this->sendSuccess();
    }


    /**
     * Store a new City record .
     * @param RoleRequest $request
     * @return JsonResponse
     */
    public function store(RoleRequest $request)
    {
        $data = $this->roleRepository->storeRole($request->all());
        return $this->sendSuccess(RoleResource::generate($data));
    }
    /**
     * Show City by ID .
     * @param int|string $roleId
     * @return JsonResponse
     */
    public function show(int|string $roleId): JsonResponse
    {
        $data = $this->roleRepository->findByIdRole($roleId);
        return $this->sendSuccess(RoleResource::generate($data));
    }



    /**
     * Store a new City record .
     * @param RoleRequest $request
     * @return JsonResponse
     */
    public function update(RoleRequest $request, int|string $roleId)
    {
        $data = $this->roleRepository->updateRole($roleId, $request->all());
        return $this->sendSuccess(RoleResource::generate($data));
    }


    public function assignRoleToUser(AssignRoleToUserRequest $request, int|string $roleId)
    {
        $data = $this->roleRepository->assignRoleToUser($roleId, $request->all());
        return $this->sendSuccess(RoleUserResource::generate($data));
    }
}