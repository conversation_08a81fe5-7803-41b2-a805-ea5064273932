<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\CurrencyRequest;
use App\Http\Resources\Admin\CurrencyResource;
use App\Repositories\Currency\CurrencyRepositoryInterface;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;


class CurrencyController extends ApiBaseController
{
    /**
     * @param  CurrencyRepositoryInterface $currencyRepository
     */
    public function __construct(protected CurrencyRepositoryInterface $currencyRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all Currencies .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->currencyRepository->getAllCurrencies();
        return $this->sendSuccess(CurrencyResource::generate($data));
    }
    /**
     * Store a new currency record .
     * @param CurrencyRequest $request
     * @return JsonResponse
     */
    public function store(CurrencyRequest $request)
    {
        $data = $this->currencyRepository->storeCurrency($request->validated());
        return $this->sendSuccess(CurrencyResource::generate($data));
    }
    /**
     * Show Currency by ID .
     * @param int|string $currencyId
     * @return JsonResponse
     */
    public function show(int|string $currencyId): JsonResponse
    {
        $data = $this->currencyRepository->findByIdCurrency($currencyId);
        return $this->sendSuccess(CurrencyResource::generate($data));
    }

    /**
     * Update  record .
     *
     * @group Currencies
     * @param CurrencyRequest $request
     * @param int|string $currencyId
     * @return JsonResponse
     */
    public function update(CurrencyRequest $request, int|string $currencyId): JsonResponse
    {
        $data = $this->currencyRepository->updateCurrency($currencyId, $request->validated());
        return $this->sendSuccess(CurrencyResource::generate($data));
    }

    /**
     * Delete Currency record .
     * @param int|string $currencyId
     * @return JsonResponse
     */

    public function destroy(int|string $currencyId)
    {
        $this->currencyRepository->deleteCurrency($currencyId);
        return $this->sendSuccess();
    }
}