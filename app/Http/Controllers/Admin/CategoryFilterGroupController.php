<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\JsonResponse;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\CategoryFilterGroupRequest;
use App\Http\Resources\Admin\CategoryFilterGroupResource;
use App\Repositories\CategoryFilterGroup\CategoryFilterGroupRepositoryInterface;




class CategoryFilterGroupController extends ApiBaseController
{
    /**
     * @param  CategoryFilterGroupRepositoryInterface $categoryFilterGroupRepository
     */
    public function __construct(protected CategoryFilterGroupRepositoryInterface $categoryFilterGroupRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all Prices .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->categoryFilterGroupRepository->getAllCategoryFilterGroup();
        return $this->sendSuccess(CategoryFilterGroupResource::generate($data));
    }
    /**
     * Store a new price record .
     * @param CategoryFilterGroupRequest $request
     * @return JsonResponse
     */
    public function store(CategoryFilterGroupRequest $request)
    {
        $data = $this->categoryFilterGroupRepository->storeCategoryFilterGroup($request->all());
        return $this->sendSuccess(CategoryFilterGroupResource::generate($data));
    }
    /**
     * Show Price by ID .
     * @param int|string $fieldFilterId
     * @return JsonResponse
     */
    public function show(int|string $fieldFilterId): JsonResponse
    {
        $data = $this->categoryFilterGroupRepository->findByIdCategoryFilterGroup($fieldFilterId);
        return $this->sendSuccess(CategoryFilterGroupResource::generate($data));
    }

    /**
     * Update  record .
     *
     * @group Prices
     * @param CategoryFilterGroupRequest $request
     * @param int|string  $fieldFilterId
     * @return JsonResponse
     */
    public function update(CategoryFilterGroupRequest $request, int|string $fieldFilterId): JsonResponse
    {
        $data = $this->categoryFilterGroupRepository->updateCategoryFilterGroup($fieldFilterId, $request->all());
        return $this->sendSuccess(CategoryFilterGroupResource::generate($data));
    }

    /**
     * Delete Price record .
     * @param int|string $fieldFilterId
     * @return JsonResponse
     */

    public function destroy(int|string $fieldFilterId)
    {
        $this->categoryFilterGroupRepository->deleteCategoryFilterGroup($fieldFilterId);
        return $this->sendSuccess();
    }
}