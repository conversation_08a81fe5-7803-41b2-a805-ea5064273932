<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\GroupRequest;
use App\Http\Resources\Admin\GroupResource;
use App\Repositories\ProductGroup\ProductGroupRepositoryInterface;
use Illuminate\Http\JsonResponse;


class ProductGroupController extends ApiBaseController
{
    /**
     * @param  ProductGroupRepositoryInterface $groupRepository
     */
    public function __construct(protected ProductGroupRepositoryInterface $productGroupRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Groups .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->productGroupRepository->getAllProductGroups();
        return $this->sendSuccess(GroupResource::generate($data));
    }
    /**
     * Store a new group record .
     * @param GroupRequest $request
     * @return JsonResponse
     */
    public function store(GroupRequest $request)
    {
        $data = $this->productGroupRepository->storeProductGroup($request->validated());
        return $this->sendSuccess(GroupResource::generate($data));
    }
    /**
     * Show Group by ID .
     * @param int|string $groupId
     * @return JsonResponse
     */
    public function show(int|string $groupId): JsonResponse
    {
        $data = $this->productGroupRepository->findByIdProductGroup($groupId);
        return $this->sendSuccess(GroupResource::generate($data));
    }

    /**
     * Update Group record .
     *
     * @group Groups
     * @param GroupRequest $request
     * @param int|string  $groupId
     * @return JsonResponse
     */
    public function update(GroupRequest $request, int|string $groupId): JsonResponse
    {
        $data = $this->productGroupRepository->updateProductGroup($groupId, $request->validated());
        return $this->sendSuccess(GroupResource::generate($data));
    }

    /**
     * Delete Group record .
     * @param int|string $groupId
     * @return JsonResponse
     */

    public function destroy(int|string $groupId)
    {
        $this->productGroupRepository->deleteProductGroup($groupId);
        return $this->sendSuccess();
    }


}