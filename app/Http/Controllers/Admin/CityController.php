<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\CityRequest;
use App\Http\Resources\Admin\CityResource;
use App\Repositories\City\CityRepositoryInterface;
use Illuminate\Http\JsonResponse;

class CityController extends ApiBaseController
{
    /**
     * @param  CityRepositoryInterface $cityRepository
     */
    public function __construct(protected CityRepositoryInterface $cityRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all Cities.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->cityRepository->getAllCities();
        return $this->sendSuccess(CityResource::generate($data));
    }
    /**
     * Store a new City record .
     * @param CityRequest $request
     * @return JsonResponse
     */
    public function store(CityRequest $request)
    {
        $data = $this->cityRepository->storeCity($request->validated());
        return $this->sendSuccess(CityResource::generate($data));
    }
    /**
     * Show City by ID .
     * @param int|string $cityId
     * @return JsonResponse
     */
    public function show(int|string $cityId): JsonResponse
    {
        $data = $this->cityRepository->findByIdCity($cityId);
        return $this->sendSuccess(CityResource::generate($data));
    }

    /**
     * Update City record .
     *
     * @group Cities
     * @param CityRequest $request
     * @param int|string  $cityId
     * @return JsonResponse
     */
    public function update(CityRequest $request, int|string $cityId): JsonResponse
    {
        $data = $this->cityRepository->updateCity($cityId, $request->validated());
        return $this->sendSuccess(CityResource::generate($data));
    }

    /**
     * Delete City record .
     * @param int|string $cityId
     * @return JsonResponse
     */

    public function destroy(int|string $cityId)
    {
        $this->cityRepository->deleteCity($cityId);
        return $this->sendSuccess();
    }
}