<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\BundleRequest;
use App\Http\Resources\Admin\BundleResource;
use App\Repositories\Bundle\BundleRepositoryInterface;
use Illuminate\Http\JsonResponse;



class BundleController extends ApiBaseController
{
    /**
     * @param  BundleRepositoryInterface $bundleRepository
     */
    public function __construct(protected BundleRepositoryInterface $bundleRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all Bundles.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->bundleRepository->getAllBundles();
        return $this->sendSuccess(BundleResource::generate($data));
    }
    /**
     * Store a new bundle record .
     * @param BundleRequest $request
     * @return JsonResponse
     */
    public function store(BundleRequest $request)
    {
        $data = $this->bundleRepository->storeBundle($request->validated());
        return $this->sendSuccess(BundleResource::generate($data));
    }
    /**
     * Show Bundle by ID .
     * @param int|string $bundleId
     * @return JsonResponse
     */
    public function show(int|string $bundleId): JsonResponse
    {
        $data = $this->bundleRepository->findByIdBundle($bundleId);
        return $this->sendSuccess(BundleResource::generate($data));
    }

    /**
     * Update Bundle record .
     *
     * @group Bundles
     * @param BundleRequest $request
     * @param int|string  $bundleId
     * @return JsonResponse
     */
    public function update(BundleRequest $request, int|string $bundleId): JsonResponse
    {
        $data = $this->bundleRepository->updateBundle($bundleId, $request->validated());
        return $this->sendSuccess(BundleResource::generate($data));
    }

    /**
     * Delete Bundle record .
     * @param int|string bundleId
     * @return JsonResponse
     */

    public function destroy(int|string $bundleId)
    {
        $this->bundleRepository->deleteBundle($bundleId);
        return $this->sendSuccess();
    }

}