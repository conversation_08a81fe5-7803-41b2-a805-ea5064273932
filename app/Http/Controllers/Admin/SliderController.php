<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\SliderResource;
use App\Http\Requests\Admin\SliderRequest;
use App\Repositories\Slider\SliderRepositoryInterface;
use Illuminate\Http\JsonResponse;

class SliderController extends ApiBaseController
{
    /**
     * @param  SliderRepositoryInterface $sliderRepository
     */
    public function __construct(protected SliderRepositoryInterface $sliderRepository)
    {
        // $this->applyPermissions();
        $this->middleware('role:admin');
    }
    /**
     * Get all Shippings Methods .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->sliderRepository->getAll();
        return $this->sendSuccess(SliderResource::generate($data));
    }
    /**
     * Store a new shipping  Method record .
     * @param SliderRequest $request
     * @return JsonResponse
     */
    public function store(SliderRequest $request)
    {
        $data = $this->sliderRepository->storeSlider($request->all());
        return $this->sendSuccess(SliderResource::generate($data));
    }
    /**
     * Show Shipping  Method by ID .
     * @param int|string $sliderId
     * @return JsonResponse
     */
    public function show(int|string $sliderId): JsonResponse
    {
        $data = $this->sliderRepository->findById($sliderId);
        return $this->sendSuccess(SliderResource::generate($data));
    }

    /**
     * Update  record .
     *
     * @group Shippings  Methods
     * @param SliderRequest $request
     * @param int|string  $sliderId
     * @return JsonResponse
     */
    public function update(SliderRequest $request, int|string $sliderId): JsonResponse
    {
        $data = $this->sliderRepository->updateSlider($sliderId, $request->validated());
        return $this->sendSuccess(SliderResource::generate($data));
    }

    /**
     * Delete Shipping  Method record .
     * @param int|string $sliderId
     * @return JsonResponse
     */

    public function destroy(int|string $sliderId)
    {
        $this->sliderRepository->destroy($sliderId);
        return $this->sendSuccess();
    }
}