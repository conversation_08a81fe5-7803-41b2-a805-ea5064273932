<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\PromotionRequest;
use App\Http\Resources\Admin\PromotionResource;
use App\Repositories\Promotion\PromotionRepositoryInterface;
use Illuminate\Http\JsonResponse;

class PromotionController extends ApiBaseController
{
    /**
     * @param  PromotionRepositoryInterface $promotionRepository
     */
    public function __construct(protected PromotionRepositoryInterface $promotionRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Cities.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->promotionRepository->getAllPromotions();
        return $this->sendSuccess(PromotionResource::generate($data));
    }
    /**
     * Store a new City record .
     * @param PromotionRequest $request
     * @return JsonResponse
     */
    public function store(PromotionRequest $request)
    {
        $data = $this->promotionRepository->storePromotion($request->all());
        return $this->sendSuccess(PromotionResource::generate($data));
    }
    /**
     * Show City by ID .
     * @param int|string $promotionId
     * @return JsonResponse
     */
    public function show(int|string $promotionId): JsonResponse
    {
        $data = $this->promotionRepository->findByIdPromotion($promotionId);
        return $this->sendSuccess(PromotionResource::generate($data));
    }

    /**
     * Update City record .
     *
     * @group Cities
     * @param PromotionRequest $request
     * @param int|string  $promotionId
     * @return JsonResponse
     */
    public function update(PromotionRequest $request, int|string $promotionId): JsonResponse
    {
        $data = $this->promotionRepository->updatePromotion($promotionId, $request->all());
        return $this->sendSuccess(PromotionResource::generate($data));
    }

    /**
     * Delete City record .
     * @param int|string $promotionId
     * @return JsonResponse
     */

    public function destroy(int|string $promotionId)
    {
        $this->promotionRepository->deletePromotion($promotionId);
        return $this->sendSuccess();
    }
}