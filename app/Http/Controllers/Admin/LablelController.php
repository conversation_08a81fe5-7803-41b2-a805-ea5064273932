<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\LabelRequest;
use App\Http\Resources\Admin\LabelResource;
use App\Repositories\Label\LabelRepositoryInterface;
use Illuminate\Http\JsonResponse;

class LablelController extends ApiBaseController
{
    /**
     * @param  LabelRepositoryInterface $labelRepository
     */
    public function __construct(protected LabelRepositoryInterface $labelRepository)
    {

    }
    /**
     * Get all Cities.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->labelRepository->getAllLabels();
        return $this->sendSuccess(LabelResource::generate($data));
    }
    /**
     * Store a new City record .
     * @param LabelRequest $request
     * @return JsonResponse
     */
    public function store(LabelRequest $request)
    {
        $data = $this->labelRepository->storeLabel($request->all());
        return $this->sendSuccess(LabelResource::generate($data));
    }
    /**
     * Show City by ID .
     * @param int|string $labelId
     * @return JsonResponse
     */
    public function show(int|string $labelId): JsonResponse
    {
        $data = $this->labelRepository->findByIdLabel($labelId);
        return $this->sendSuccess(LabelResource::generate($data));
    }

    /**
     * Update City record .
     *
     * @group Cities
     * @param LabelRequest $request
     * @param int|string  $labelId
     * @return JsonResponse
     */
    public function update(LabelRequest $request, int|string $labelId): JsonResponse
    {
        $data = $this->labelRepository->updateLabel($labelId, $request->all());
        return $this->sendSuccess(LabelResource::generate($data));
    }

    /**
     * Delete City record .
     * @param int|string $labelId
     * @return JsonResponse
     */

    public function destroy(int|string $labelId)
    {
        $this->labelRepository->deleteLabel($labelId);
        return $this->sendSuccess();
    }


    public function allowedFilters(): array
    {
        return [

        ];
    }

}