<?php

namespace App\Http\Controllers\Admin\General;

use App\Exceptions\GeneralException;
use App\Http\Controllers\ApiBaseController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class CollectionApiController extends ApiBaseController
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function __invoke(Request $request)
    {
        $resource = [];
        $endpoints = $request->query->all();

        if (blank($endpoints)) {
            return $this->sendSuccess(data: []);
        }

        foreach ($endpoints as $name => $endpoint) {
            try {
                $endpoint = base64_decode($endpoint);
                $endpointRequest = $request->create($endpoint);

                $endpointRequest->headers->set('Entity', $request->header('Entity'));
                $endpointRequest->headers->set('Language', $request->header('Language'));

                app()->bind('request', fn() => $endpointRequest);

                $response = Route::dispatch($endpointRequest);
                if ($response->isSuccessful()) {
                    $resource[$name] = $response->getData();
                } else {
                    throw new GeneralException(message: $response->exception->getMessage());
                }
            } catch (\Exception $exception) {
                $resource[$name] = $this->getExceptionMessage($exception);
            }
        }

        return $this->sendSuccess(data: $resource);
    }

    /**
     * @param \Exception $exception
     * @return array
     */
    protected function getExceptionMessage(\Exception $exception): array
    {
        $code = $exception->getCode();

        return [
            'error' => true,
            'code' => $code,
            'message' => $exception->getMessage(),
        ];
    }
}