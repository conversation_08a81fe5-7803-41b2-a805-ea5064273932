<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\ProductAttributeRequest;
use App\Http\Requests\Admin\ProductRequest;
use App\Http\Resources\Admin\ProductDetailsResource;
use App\Http\Resources\Admin\ProductResource;
use App\Http\Resources\Admin\AttributeResource;
use App\Http\Resources\Admin\ProductMediaResource;
use App\Repositories\Product\ProductRepositoryInterface;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Artisan;

class ProductController extends ApiBaseController
{
    /**
     * @param  ProductRepositoryInterface $productRepository
     */
    public function __construct(protected ProductRepositoryInterface $productRepository)
    {
        //  $this->applyPermissions();
        $this->middleware('role:admin');
    }
    /**
     * Get all Products .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->productRepository->getAllProducts();
        return $this->sendSuccess(ProductResource::generate($data));
    }

    public function flush()
    {
        Artisan::call('scout:import', [
            'model' => 'App\Models\Product',
        ]);
        return $this->sendSuccess();
    }

    /**
     * Get Category Attributes .
     * @param int $categoryId
     * @return JsonResponse
     */
    public function getCategoryAttributes($category)
    {
        $data = $this->productRepository->getCategoryAttributes($category);
        return $this->sendSuccess(AttributeResource::generate($data));
    }
    public function getAttributes(Request $request)
    {

        $data = $this->productRepository->getAttributesWithParents();
        return $this->sendSuccess(AttributeResource::generate($data));
    }
    /**
     * Store a new Product record .
     * @param ProductRequest $request
     * @return JsonResponse
     */
    public function store(ProductRequest $request)
    {
        $data = $this->productRepository->storeProduct($request->all());
        return $this->sendSuccess(ProductResource::generate($data));
    }



    /**
     * Store a new Product record .
     * @param ProductRequest $request
     * @return JsonResponse
     */
    public function storeDraftProduct(Request $request)
    {
        $data = $this->productRepository->storeDraftProduct($request->all());
        return $this->sendSuccess(ProductResource::generate($data));
    }

    /**
     * Show ProductId  by ID .
     *
     * @param int|string $productId
     * @return JsonResponse
     */
    public function show(int|string $productId): JsonResponse
    {
        $data = $this->productRepository->findByIdProduct($productId);
        return $this->sendSuccess(ProductDetailsResource::generate($data));
    }

    /**
     * Update ProductId record .
     *
     * @group Products
     * @param ProductRequest $request
     * @param int|string  $productId
     * @return JsonResponse
     */
    public function update(ProductRequest $request, int|string $productId): JsonResponse
    {
        $data = $this->productRepository->updateProduct($productId, $request->all());
        return $this->sendSuccess();
    }

    /**
     * Delete Product record .
     * @param int|string $productId
     * @return JsonResponse
     */

    public function destroy(int|string $productId)
    {
        $this->productRepository->deleteProduct($productId);
        return $this->sendSuccess();
    }
    /**
     * Store Product Attributes
     *
     * @param Request $request
     * @param object $product
     * @return JsonResponse
     */
    public function attribute(Product $product, ProductAttributeRequest $request): JsonResponse
    {
        $this->productRepository->storeProductAttributes($product, $request->validated());
        return $this->sendSuccess(ProductResource::generate($product));
    }
    /**
     * get Product Attributes
     *
     * @param Request $request
     * @param object $product
     * @return JsonResponse
     */
    public function getProductAttributes(Product $product): JsonResponse
    {
        $this->productRepository->getProductAttributes($product);
        return $this->sendSuccess(AttributeResource::generate($product->attributes));
    }
    /**
     * Store Product Variance
     *
     * @param Request $request
     * @param object $product
     * @return JsonResponse
     */
    public function variance(Product $product, Request $request): JsonResponse
    {
        $attributeId = ($request->attributeId != null) ? $request->attributeId : [];
        $this->productRepository->storeProductVariance($product, $attributeId);
        return $this->sendSuccess(ProductResource::generate($product));
    }
    /**
     * Store Product Media.
     *
     * @param Request $request
     * @param  Product $product
     * @return JsonResponse
     */
    public function storeProductMedia(Product $product, Request $request): JsonResponse
    {
        $mediaId = ($request->mediaId != null) ? $request->mediaId : [];
        $this->productRepository->storeProductMedia($mediaId, $product);
        return $this->sendSuccess(ProductMediaResource::generate($product->productsMedia));
    }
    /**
     * Get all Product Media
     * @param  Product $product
     * @return JsonResponse
     */
    public function getProductMedia(Product $product): JsonResponse
    {
        $this->productRepository->getProductMedia($product);
        return $this->sendSuccess(ProductMediaResource::generate($product->productsMedia));
    }


}