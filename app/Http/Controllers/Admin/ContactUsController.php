<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\ContactUsRequest;
use App\Http\Resources\Website\ContactUsResource;
use App\Repositories\ContactUs\ContactUsRepositoryInterface;
use Illuminate\Http\JsonResponse;

class ContactUsController extends ApiBaseController
{
    /**
     * @param  ContactUsRepositoryInterface $categoryRepository
     */
    public function __construct(protected ContactUsRepositoryInterface $contactUsRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all Messages.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->contactUsRepository->getAllMessages();
        return $this->sendSuccess(ContactUsResource::generate($data));
    }

    /**
     * Show ContactUs by ID .
     * @param int|string $contactUsId
     * @return JsonResponse
     */
    public function show(int|string $contactUsId): JsonResponse
    {
        $data = $this->contactUsRepository->findByIdContactUs($contactUsId);
        return $this->sendSuccess(ContactUsResource::generate($data));
    }

    /**
     * Update ContactUs record .
     *
     * @param ContactUsRequest $request
     * @param int|string  $contactUsId 
     * @return JsonResponse
     */
    public function update(ContactUsRequest $request, int|string $contactUsId): JsonResponse
    {
        $data = $this->contactUsRepository->updateStatus($contactUsId, $request->validated());
        return $this->sendSuccess(ContactUsResource::generate($data));
    }


    /**
     * Delete ContactUs record .
     * @param int|string $addressId
     * @return JsonResponse
     */

    public function destroy(int|string $contactUsId)
    {
        $this->contactUsRepository->deleteMessage($contactUsId);
        return $this->sendSuccess();
    }

}