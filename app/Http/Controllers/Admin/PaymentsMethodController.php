<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\PaymentsMethodRequest;
use App\Http\Resources\Admin\PaymentMethodsResource;
use App\Repositories\PaymentMethod\PaymentMethodRepositoryInterface;
use Illuminate\Http\JsonResponse;


class PaymentsMethodController extends ApiBaseController
{
    /**
     * @param  PaymentMethodRepositoryInterface $paymentMethodRepository
     */
    public function __construct(protected PaymentMethodRepositoryInterface $paymentMethodRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Payments Methods .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {

        $data = $this->paymentMethodRepository->getAllPaymentsMethods();
        return $this->sendSuccess(PaymentMethodsResource::generate($data));
    }
    /**
     * Store a new Payment Method record .
     * @param PaymentsMethodRequest $request
     * @return JsonResponse
     */
    public function store(PaymentsMethodRequest $request)
    {
        $data = $this->paymentMethodRepository->storePaymentMethod($request->validated());
        return $this->sendSuccess(PaymentMethodsResource::generate($data));
    }
    /**
     * Show Payment Method by ID .
     * @param int|string $paymentMethodId
     * @return JsonResponse
     */
    public function show(int|string $paymentMethodId): JsonResponse
    {
        $data = $this->paymentMethodRepository->findByIdPaymentMethod($paymentMethodId);
        return $this->sendSuccess(PaymentMethodsResource::generate($data));
    }

    /**
     * Update Payment Method  record .
     *
     * @param PaymentsMethodRequest $request
     * @param int|string $paymentMethodId
     * @return JsonResponse
     */
    public function update(PaymentsMethodRequest $request, int|string $paymentMethodId): JsonResponse
    {
        $data = $this->paymentMethodRepository->updatePaymentMethod($paymentMethodId, $request->validated());
        return $this->sendSuccess(PaymentMethodsResource::generate($data));
    }

    /**
     * Delete Payment Method record .
     * @param int|string $paymentMethodId
     * @return JsonResponse
     */

    public function destroy(int|string $paymentMethodId)
    {
        $this->paymentMethodRepository->deletePaymentMethod($paymentMethodId);
        return $this->sendSuccess();
    }



    /**
     * active Payment Method by ID .
     * @param int|string $paymentMethodId
     * @return JsonResponse
     */
    public function activation(int|string $paymentMethodId)
    {
        $data = $this->paymentMethodRepository->activation($paymentMethodId);
        return $this->sendSuccess($data);
    }

    /**
     * active Payment Method by ID .
     * @param int|string $paymentMethodId
     * @return JsonResponse
     */
    public function activePaymentMethod(int|string $paymentMethodId)
    {
        $data = $this->paymentMethodRepository->activePaymentMethod($paymentMethodId);
        return $this->sendSuccess($data);
    }

    /**
     * Inactive Payment Method  by ID .
     * @param int|string $paymentMethodId
     * @return JsonResponse
     */
    public function inactivePaymentMethod(int|string $paymentMethodId)
    {
        $this->paymentMethodRepository->inactivePaymentMethod($paymentMethodId);
        return $this->sendSuccess();
    }
}