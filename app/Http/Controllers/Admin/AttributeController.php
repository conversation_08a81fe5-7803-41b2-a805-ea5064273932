<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\AttributeOptionsRequest;
use App\Http\Requests\Admin\AttributeRequest;
use App\Http\Requests\Admin\AttributeValuesRequest;
use App\Http\Resources\Admin\AttributeResource;
use App\Repositories\Attribute\AttributeRepositoryInterface;
use App\Models\Attribute;
use Illuminate\Http\JsonResponse;


class AttributeController extends ApiBaseController
{
    /**
     * @param  AttributeRepositoryInterface $attributeRepository
     */
    public function __construct(protected AttributeRepositoryInterface $attributeRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Attributes .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->attributeRepository->getAllAttributes();
        return $this->sendSuccess(AttributeResource::generate($data));
    }
    /**
     * Store a new Attribute record .
     * @param AttributeRequest $request
     * @return JsonResponse
     */
    public function store(AttributeRequest $request): JsonResponse
    {

        $data = $this->attributeRepository->storeAttribute($request->validated());
        return $this->sendSuccess(AttributeResource::generate($data));
    }

    /**
     * Show Attribute by ID .
     *
     * @group Attributes
     * @param int|string $attributeId
     * @return JsonResponse
     */
    public function show(int|string $attributeId): JsonResponse
    {
        $data = $this->attributeRepository->findByIdAttribute($attributeId);
        return $this->sendSuccess(AttributeResource::generate($data));
    }
    /**
     * Update Attribute record .
     *
     * @group Attribute
     * @param AttributeRequest $request
     * @param int|string $attributeId
     * @return JsonResponse
     */
    public function update(AttributeRequest $request, int|string $attributeId): JsonResponse
    {
        $data = $this->attributeRepository->updateAttribute($attributeId, $request->validated());
        return $this->sendSuccess(AttributeResource::generate($data));
    }
    /**
     * Delete Attribute record .
     * @param int|string $attributeId
     * @return JsonResponse
     */
    public function destroy(int|string $attributeId)
    {
        $this->attributeRepository->deleteAttribute($attributeId);
        return $this->sendSuccess();
    }
    /**
     * get Attribute Options
     *
     * @return JsonResponse
     */
    public function getOptions($resource, $name)
    {
        $data = $this->attributeRepository->getOptions($resource, $name);
        return $this->sendSuccess(AttributeResource::generate($data));
    }
}