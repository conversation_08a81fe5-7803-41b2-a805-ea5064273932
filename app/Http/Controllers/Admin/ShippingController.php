<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\ShippingResource;
use App\Http\Requests\Admin\ShippingRequest;
use App\Repositories\Shipping\ShippingRepositoryInterface;
use Illuminate\Http\JsonResponse;

class ShippingController extends ApiBaseController
{
    /**
     * @param  ShippingRepositoryInterface $shippingRepository
     */
    public function __construct(protected ShippingRepositoryInterface $shippingRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Shippings Methods .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->shippingRepository->getAllShippingPaginated();
        return $this->sendSuccess(ShippingResource::generate($data));
    }
    /**
     * Store a new shipping  Method record .
     * @param ShippingRequest $request
     * @return JsonResponse
     */
    public function store(ShippingRequest $request)
    {
        $data = $this->shippingRepository->storeShipping($request->all());
        return $this->sendSuccess(ShippingResource::generate($data));
    }
    /**
     * Show Shipping  Method by ID .
     * @param int|string $shippingId
     * @return JsonResponse
     */
    public function show(int|string $shippingId): JsonResponse
    {
        $data = $this->shippingRepository->findByIdShipping($shippingId);
        return $this->sendSuccess(ShippingResource::generate($data));
    }

    /**
     * Update  record .
     *
     * @group Shippings  Methods
     * @param ShippingRequest $request
     * @param int|string  $ShippingId
     * @return JsonResponse
     */
    public function update(ShippingRequest $request, int|string $shippingId): JsonResponse
    {
        $data = $this->shippingRepository->updateShipping($shippingId, $request->validated());
        return $this->sendSuccess(ShippingResource::generate($data));
    }

    /**
     * Delete Shipping  Method record .
     * @param int|string $shippingId
     * @return JsonResponse
     */

    public function destroy(int|string $shippingId)
    {
        $this->shippingRepository->deleteShipping($shippingId);
        return $this->sendSuccess();
    }
}