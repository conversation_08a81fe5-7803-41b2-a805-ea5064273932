<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\AiRequest;
use App\Http\Resources\Admin\ActivityLogResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use App\Repositories\ActivityLog\ActivityLogRepositoryInterface;

class ActivityLogController extends ApiBaseController
{
    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */


    public function __construct(protected ActivityLogRepositoryInterface $activityLogRepository)
    {
        $this->middleware('role:admin');
    }
    public function get(Request $request, $key)
    {

        $inputs = $request->post();

        if (!$inputs || !$key || !is_array($inputs) || !count($inputs)) {
            return $this->sendError('Inputs and key are required');
        }

        $result = $this->activityLogRepository->get($key, $inputs);

        return $this->sendSuccess($result);


    }
    public function index(): JsonResponse
    {
        $data = $this->activityLogRepository->getAllLatestPaginated(with: ['causer', 'subject']);
        return $this->sendSuccess(ActivityLogResource::generate($data));
    }



    public function show(int|string $id): JsonResponse
    {

        $data = $this->activityLogRepository->findById($id);
        return $this->sendSuccess(ActivityLogResource::generate($data));
    }
    /**
     * Update Ai record .
     *
     * @group Ais
     * @param AiRequest $request
     * @param int|string $aiId
     * @return JsonResponse
     */
    public function findByModel(string $model, int $modelId = 0): JsonResponse
    {

        if ($model == 'null' || is_null($model))
            throw new \Exception(__('model is required'), Response::HTTP_LOCKED);


        $data = $this->activityLogRepository->findByModel($model, $modelId);
        return $this->sendSuccess(ActivityLogResource::generate($data));
    }

    /**
     * Delete Ai record .
     * @param int|string $aiId
     * @return JsonResponse
     */

    public function destroy(int|string $aiId)
    {
        $this->aiRepository->destroy($aiId);
        return $this->sendSuccess();
    }
}