<?php

namespace App\Http\Controllers\Admin;


use Illuminate\Http\JsonResponse;
use App\Http\Requests\Admin\PriceRequest;
use App\Http\Requests\Admin\FilterRequest;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\FilterResource;
use App\Repositories\Filters\FiltersRepositoryInterface;

class FiltersController extends ApiBaseController
{
    /**
     * @param  FiltersRepositoryInterface $filtersRepository
     */
    public function __construct(protected FiltersRepositoryInterface $filtersRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all Filters .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->filtersRepository->getAllFilters();
        return $this->sendSuccess(FilterResource::generate($data));
    }
    /**
     * Store a new Filter record .
     * @param PriceRequest $request
     * @return JsonResponse
     */
    public function store(FilterRequest $request)
    {
        $data = $this->filtersRepository->storeFilter($request->all());
        return $this->sendSuccess(FilterResource::generate($data));
    }
    /**
     * Show Filter by ID .
     * @param int|string $filterId
     * @return JsonResponse
     */
    public function show(int|string $filterId): JsonResponse
    {
        $data = $this->filtersRepository->findByIdFilter($filterId);
        return $this->sendSuccess(FilterResource::generate($data));
    }

    /**
     * Update Filter record .
     *
     * @group 
     * @param FilterRequest $request
     * @param int|string  $filterId
     * @return JsonResponse
     */
    public function update(FilterRequest $request, int|string $filterId): JsonResponse
    {
        $data = $this->filtersRepository->updateFilter($filterId, $request->all());
        return $this->sendSuccess(FilterResource::generate($data));
    }

    /**
     * Delete Filter record .
     * @param int|string $filterId
     * @return JsonResponse
     */

    public function destroy(int|string $filterId)
    {
        $this->filtersRepository->deleteFilter($filterId);
        return $this->sendSuccess();
    }
}