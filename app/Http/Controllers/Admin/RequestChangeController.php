<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\RequestChangeRequest;
use App\Http\Resources\Admin\RequestChangeResource;
use App\Http\Resources\Admin\UserResource;
use App\Http\Requests\Admin\UserRequest;
use Illuminate\Http\JsonResponse;
use App\Repositories\RequestChange\RequestChangeRepositoryInterface;
use Illuminate\Http\Request;


class RequestChangeController extends ApiBaseController
{
    /**
     * @param   RequestChangeRepositoryInterface $requestChangeRepository
     */
    public function __construct(protected RequestChangeRepositoryInterface $requestChangeRepository)
    {
        $this->middleware('role:admin');
    }

    /**
     * Get all Users.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->requestChangeRepository->getAllPaginated();
        return $this->sendSuccess(RequestChangeResource::generate($data));
    }

    /**
     * Store a new User record .
     * @param UserRequest $request
     * @return JsonResponse
     */
    public function store(RequestChangeRequest $request): JsonResponse
    {
        $data = $this->requestChangeRepository->storeRequestChange($request->all());
        return $this->sendSuccess(RequestChangeResource::generate($data));

    }

    /**
     * Show User by ID .
     *
     * @group Users
     * @param int|string $requestChangeId
     * @return JsonResponse
     */
    public function show(int|string $requestChangeId): JsonResponse
    {
        $data = $this->requestChangeRepository->findById($requestChangeId);
        return $this->sendSuccess(RequestChangeResource::generate($data));
    }

    /**
     * Update User record .
     *
     * @group Users
     * @param UserRequest $request
     * @param int|string $userId
     * @return JsonResponse
     */
    public function update(RequestChangeRequest $request, int|string $requestChangeId): JsonResponse
    {
        $data = $this->requestChangeRepository->updateRequestChange($requestChangeId, $request->all());
        return $this->sendSuccess(RequestChangeResource::generate($data));
    }

    /**
     * Update User record .
     *
     * @group Users
     * @param UserRequest $request
     * @param int|string $userId
     * @return JsonResponse
     */
    public function activationRequestChangeName(Request $request, int|string $requestChangeId): JsonResponse
    {
        $data = $this->requestChangeRepository->activationRequestChangeName($requestChangeId);
        return $this->sendSuccess(RequestChangeResource::generate($data));
    }

    /**
     * Delete User record .
     * @param int|string $requestChangeId
     */
    public function destroy(int|string $requestChangeId)
    {
        $this->requestChangeRepository->destroy($requestChangeId);
        return $this->sendSuccess();
    }




}