<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\AuctionRequest;
use App\Http\Resources\Admin\AuctionResource;
use App\Repositories\Auction\AuctionRepositoryInterface;
use App\Models\Attribute;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AuctionController extends ApiBaseController
{
    /**
     * @param  AuctionRepositoryInterface $auctionRepository
     */
    public function __construct(protected AuctionRepositoryInterface $auctionRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Auctions .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->auctionRepository->getAllAuctions();
        return $this->sendSuccess(AuctionResource::generate($data));
    }
    /**
     * Store a new Auction record .
     * @param  AuctionRequest $request
     * @return JsonResponse
     */
    public function store(AuctionRequest $request)
    {
        $data = $this->auctionRepository->storeAuction($request->validated());
        return $this->sendSuccess(AuctionResource::generate($data));
    }
    /**
     * Show Auction by ID .
     * @param int|string $auctionId
     * @return JsonResponse
     */
    public function show(int|string $auctionId): JsonResponse
    {
        $data = $this->auctionRepository->findByIdAuction($auctionId);
        return $this->sendSuccess(AuctionResource::generate($data));
    }

    /**
     * Update Auction record .
     *
     * @group  Auctions
     * @param  AuctionRequest $request
     * @param int|string  $auctionId
     * @return JsonResponse
     */
    public function update(AuctionRequest $request, int|string $auctionId): JsonResponse
    {
        $data = $this->auctionRepository->updateAuction($auctionId, $request->validated());
        return $this->sendSuccess(AuctionResource::generate($data));
    }

    /**
     * Delete Auction record .
     * @param int|string $auctionId
     * @return JsonResponse
     */

    public function destroy(int|string $auctionId)
    {
        $this->auctionRepository->deleteAuction($auctionId);
        return $this->sendSuccess();
    }
}