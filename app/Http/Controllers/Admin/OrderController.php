<?php

namespace App\Http\Controllers\Admin;


use App\Http\Resources\Admin\OrderDetailsResource;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\OrderCostRequest;
use App\Http\Requests\Admin\OrderRequest;
use App\Http\Resources\Admin\OrderResource;
use App\Repositories\Order\OrderRepositoryInterface;
use Illuminate\Http\Request;

class OrderController extends ApiBaseController
{
    /**
     * @param  OrderRepositoryInterface $orderRepository
     */
    public function __construct(protected OrderRepositoryInterface $orderRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Orders .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->orderRepository->getAllOrders();
        //dd($data->toArray());
        return $this->sendSuccess(OrderResource::generate($data));
    }
    /**
     * Store a new Order record .
     * @param OrderRequest $request
     * @return JsonResponse
     */
    public function store(OrderRequest $request)
    {
        $data = $this->orderRepository->storeOrder($request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }
    /**
     * Show Order by ID .
     * @param int|string $order
     * @return JsonResponse
     */
    public function show(int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->findByIdOrder($orderId);
        return $this->sendSuccess(OrderDetailsResource::generate($data));
    }
    /**
     * refund Order  by ID .
     * @param int|string $order
     * @return JsonResponse
     */
    public function refundOrder(int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->refundOrder($orderId);
        return $this->sendSuccess(OrderResource::generate($data));
    }

    /**
     * Update Order record .
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function update(OrderRequest $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->updateOrderAdmin($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }

    /**
     * Delete Order record .
     * @param int|string $orderId
     * @return JsonResponse
     */

    public function destroy(int|string $orderId)
    {
        $this->orderRepository->deleteOrder($orderId);
        return $this->sendSuccess();
    }



    /**
     * @return JsonResponse
     */
    public function completed(int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->completed($orderId);
        return $this->sendSuccess(OrderResource::generate($data));
    }




    /**
     * Update Order record .
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function processing(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->processing($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    /**
     * Add Product.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function addProduct(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->addProduct($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }

    /**
     * Add Product.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function deleteProduct(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->deleteProduct($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }




    /**
     * change Delivery Status.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function changeDeliveryStatus(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->changeDeliveryStatus($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }




    /**
     * Add Note.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function addNote(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->addNote($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }



    /**
     * cancel product.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function cancelOrderItem(Request $request, int|string $orderId, int|string $orderItemId): JsonResponse
    {
        $data = $this->orderRepository->cancelOrderItem($orderId, $orderItemId);
        return $this->sendSuccess(OrderResource::generate($data));
    }


    /**
     * change shipping carrier.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function changeShippingCarrier(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->changeShippingCarrier($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }

    /**
     * change shipping carrier.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function quantityProduct(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->quantityProduct($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }



    /**
     * change shipping carrier.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function deliverMoney(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->deliverMoney($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }



    /**
     * change shipping carrier.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function startEditing(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->startEditing($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    /**
     * change shipping carrier.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function endEditing(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->endEditing($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }



    /**
     * change shipping carrier.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @param string $status
     * @return JsonResponse
     */
    public function changeStatus(Request $request, int|string $orderId, string $status): JsonResponse
    {
        $data = $this->orderRepository->changeStatus($orderId, $status, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    public function updateCost(int|string $orderId, OrderCostRequest $request): JsonResponse
    {
        $data = $this->orderRepository->updateCost($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }

    public function updateCostItem(int|string $orderId, OrderCostRequest $request): JsonResponse
    {
        $data = $this->orderRepository->updateCostItem($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }
}