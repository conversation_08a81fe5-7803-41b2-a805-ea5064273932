<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\CategoryRequest;
use App\Http\Resources\Admin\CategoryAttributesResource;
use App\Http\Resources\Admin\CategoryResource;
use App\Repositories\Category\CategoryRepositoryInterface;
use App\Models\Category;
use Illuminate\Http\JsonResponse;


class CategoryController extends ApiBaseController
{
    /**
     * @param  CategoryRepositoryInterface $categoryRepository
     */
    public function __construct(protected CategoryRepositoryInterface $categoryRepository)
    {
        $this->middleware('role:admin');


        // dd(auth(GUARD_API)->user()->hasPermissionTo('admin.categories.index'));
        // $this->middleware(function ($request, $next) {
        //     // Check if the authenticated user has the 'admin.categories.index' permission
        //     if (auth(GUARD_API)->check()) {
        //         return $next($request);
        //     }

        //     // If the user does not have the permission, you can handle it accordingly
        //     abort(403, 'Unauthorized');


        // });


        //  $this->middleware('permission:admin.categories.index')->only(['index']);
        //$this->middleware('can:admin.categories.index')->only(['index']);
        // $this->middleware('permission:admin.categories.index', ['only' => ['index', 'show']]);


        //$this->middleware('can:admin.categories.index');
        // $this->middleware('permission:role.create', ['only' => ['create', 'store']]);
        // $this->middleware('permission:role.edit', ['only' => ['edit', 'update']]);
        // $this->middleware('permission:role.delete', ['only' => ['destroy']]);


        //$this->middleware('can:admin.categories.index');
        // $this->middleware('permission:admin.categories.sds', ['only' => ['index', 'show']]);

        // $this->middleware('permission:admin.categories.index', ['only' => ['index', 'show']]);

        // $roles = auth(GUARD_API)->user()->getRoleNames();

        // $permissions = auth(GUARD_API)->user()->getAllPermissions()->toArray();
        // // dd($permissions);
        // $this->middleware('can:admin.categories.index');
        //->only(['index']);

        // $this->middleware('can:admin.categories.store')->only(['store']);
        // $this->middleware('can:admin.categories.update')->only(['update']);
        // $this->middleware('can:admin.categories.destroy')->only(['destroy']);
        // $this->middleware('can:admin.categories.show')->only(['show']);
        // $this->middleware('can:admin.categories.getCategoryAttributes')->only(['getCategoryAttributes']);

    }

    /**
     * Get all Categories .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {

        $data = $this->categoryRepository->getAllCategories();
        return $this->sendSuccess(CategoryResource::generate($data));
    }
    /**
     * Store a new category record .
     * @param CategoryRequest $request
     * @return JsonResponse
     */
    public function store(CategoryRequest $request)
    {
        $data = $this->categoryRepository->storeCategory($request->all());
        return $this->sendSuccess(CategoryResource::generate($data));
    }

    /**
     * Show Category by ID .
     *
     * @param int|string $categoryId
     * @return JsonResponse
     */
    public function show(int|string $categoryId): JsonResponse
    {
        $data = $this->categoryRepository->findByIdCategory($categoryId);
        return $this->sendSuccess(CategoryResource::generate($data));
    }
    /**
     * Update Category record .
     *
     * @group Brands
     * @param CategoryRequest $request
     * @param int|string  $categoryId
     * @return JsonResponse
     */
    public function update(CategoryRequest $request, int|string $categoryId): JsonResponse
    {
        $data = $this->categoryRepository->updateCategory($categoryId, $request->validated());
        return $this->sendSuccess(CategoryResource::generate($data));
    }

    /**
     * Get all Category Attributes.
     *
     * @param  Category $category
     * @return JsonResponse
     */
    public function getCategoryAttributes(Category $category): JsonResponse
    {
        $attributes = $this->categoryRepository->getCategoryAttributes($category);
        return $this->sendSuccess(CategoryAttributesResource::collection($attributes));
    }


    /**
     * Delete Category record .
     * @param int|string $categoryId
     * @return JsonResponse
     */

    public function destroy(int|string $categoryId)
    {
        $this->categoryRepository->deleteCategory($categoryId);
        return $this->sendSuccess();
    }
}