<?php

namespace App\Http\Controllers\Admin;


use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\UsedCategoryRequest;
use App\Http\Resources\Admin\UsedCategoryResource;
use App\Repositories\UsedCategory\UsedCategoryRepositoryInterface;
use Illuminate\Http\JsonResponse;

class UsedCategoryController extends ApiBaseController
{
    /**
     * @param  UsedCategoryRepositoryInterface $usedCategoryRepository
     */
    public function __construct(protected UsedCategoryRepositoryInterface $usedCategoryRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Used Categories .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->usedCategoryRepository->getAllUsedCategories();
        return $this->sendSuccess(UsedCategoryResource::generate($data));
    }
    /**
     * Store a new used category record .
     * @param UsedCategoryRequest $request
     * @return JsonResponse
     */
    public function store(UsedCategoryRequest $request)
    {
        $data = $this->usedCategoryRepository->storeUsedCategory($request->all());
        return $this->sendSuccess(UsedCategoryResource::generate($data));
    }

    /**
     * Show UsedCategory by ID .
     *
     * @param int|string $usedCategoryId
     * @return JsonResponse
     */
    public function show(int|string $usedCategoryId): JsonResponse
    {
        $data = $this->usedCategoryRepository->findByIdUsedCategory($usedCategoryId);
        return $this->sendSuccess(UsedCategoryResource::generate($data));
    }
    /**
     * Update UsedCategory record .
   
     * @param UsedCategoryRequest $request
     * @param int|string  $usedCategoryId
     * @return JsonResponse
     */
    public function update(UsedCategoryRequest $request, int|string $usedCategoryId): JsonResponse
    {
        $data = $this->usedCategoryRepository->updateUsedCategory($usedCategoryId, $request->validated());
        return $this->sendSuccess(UsedCategoryResource::generate($data));
    }

    /**
     * Delete Used Category record .
     * @param int|string $categoryId
     * @return JsonResponse
     */

    public function destroy(int|string $usedCategoryId)
    {
        $this->usedCategoryRepository->deleteUsedCategory($usedCategoryId);
        return $this->sendSuccess();
    }
}