<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\UsedProductRequest;
use App\Http\Resources\Admin\UsedProductDetailsResource;
use App\Http\Resources\Admin\UsedProductResource;
use App\Http\Resources\Admin\AttributeResource;
use Illuminate\Http\Request;
use App\Repositories\UsedProduct\UsedProductRepositoryInterface;
use Illuminate\Http\JsonResponse;

class UsedProductController extends ApiBaseController
{

    /**
     * @param  UsedProductRepositoryInterface $usedProductRepository
     */
    public function __construct(protected UsedProductRepositoryInterface $usedProductRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all UsedProducts .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->usedProductRepository->getAllUsedProducts();
        return $this->sendSuccess(UsedProductResource::generate($data));
    }
    /**
     * Store a new UsedProductRequest record .
     * @param UsedProductRequest $request
     * @return JsonResponse
     */
    public function store(UsedProductRequest $request)
    {
        $data = $this->usedProductRepository->storeUsedProduct($request->validated());
        return $this->sendSuccess(UsedProductResource::generate($data));
    }
    /**
     * Show UsedProductRequest by ID .
     * @param int|string $usedProductId
     * @return JsonResponse
     */
    public function show(int|string $usedProductId): JsonResponse
    {
        $data = $this->usedProductRepository->findByIdUsedProduct($usedProductId);
        return $this->sendSuccess(UsedProductDetailsResource::generate($data));
    }

    /**
     * Update UsedProductRequest record .
     *
     * @UsedProducts
     * @param UsedProductRequest $request
     * @param int|string $supplierId
     * @return JsonResponse
     */
    public function update(UsedProductRequest $request, int|string $usedProductId): JsonResponse
    {
        $data = $this->usedProductRepository->updateUsedProduct($usedProductId, $request->validated());
        return $this->sendSuccess(UsedProductResource::generate($data));
    }

    /**
     * Delete UsedProduct record .
     * @param int|string $usedProductId
     * @return JsonResponse
     */

    public function destroy(int|string $usedProductId)
    {
        $this->usedProductRepository->deleteUsedProduct($usedProductId);
        return $this->sendSuccess();
    }

    /**
     * get Attributes
     * @return JsonResponse
     */
    public function getAttributes(Request $request)
    {
        $data = $this->usedProductRepository->getAttributesWithParents();
        return $this->sendSuccess(AttributeResource::generate($data));
    }

    /**
     * active UsedProduct  by ID .
     * @param int|string $usedProductId
     * @return JsonResponse
     */

    public function activeUsedProduct(int|string $usedProductId)
    {
        $data = $this->usedProductRepository->activeUsedProduct($usedProductId);
        return $this->sendSuccess($data);
    }


}