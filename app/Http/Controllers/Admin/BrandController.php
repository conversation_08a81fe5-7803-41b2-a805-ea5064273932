<?php

namespace App\Http\Controllers\Admin;


use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\BrandRequest;
use App\Http\Resources\Admin\BrandResource;
use App\Repositories\Brand\BrandRepositoryInterface;
use Illuminate\Http\JsonResponse;

class BrandController extends ApiBaseController
{
    /**
     * @param  BrandRepositoryInterface $brandRepository
     */
    public function __construct(protected BrandRepositoryInterface $brandRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all brands .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->brandRepository->getAllBrands();
        return $this->sendSuccess(BrandResource::generate($data));
    }
    /**
     * Store a new brand record .
     * @param BrandRequest $request
     * @return JsonResponse
     */
    public function store(BrandRequest $request): JsonResponse
    {
        $data = $this->brandRepository->storeBrand($request->validated());
        return $this->sendSuccess(BrandResource::generate($data));
    }

    /**
     * Show Brand by ID .
     *
     * @group Brands
     * @param int|string $brandId
     * @return JsonResponse
     */
    public function show(int|string $brandId): JsonResponse
    {
        $data = $this->brandRepository->findByIdBrand($brandId);

        return $this->sendSuccess(BrandResource::generate($data));
    }
    /**
     * Update Brand record .
     *
     * @group Brands
     * @param BrandRequest $request
     * @param int|string $brandId
     * @return JsonResponse
     */
    public function update(BrandRequest $request, int|string $brandId): JsonResponse
    {
        $data = $this->brandRepository->updateBrand($brandId, $request->validated());

        return $this->sendSuccess(BrandResource::generate($data));
    }
    /**
     * Delete Brand record .
     * @param int|string $brandId
     * @return JsonResponse
     */

    public function destroy(int|string $brandId)
    {
        $this->brandRepository->deleteBrand($brandId);
        return $this->sendSuccess();
    }

}