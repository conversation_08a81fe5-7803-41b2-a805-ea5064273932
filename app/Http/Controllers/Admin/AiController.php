<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\AiRequest;
use App\Http\Resources\Admin\AiResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Repositories\Ai\AiRepositoryInterface;

class AiController extends ApiBaseController
{
    /**
     * Display a listing of the resource.
     *
     * @return JsonResponse
     */


    public function __construct(protected AiRepositoryInterface $aiRepository)
    {
        $this->middleware('role:admin');
    }
    public function get(Request $request, $key)
    {

        $inputs = $request->post();

        if (!$inputs || !$key || !is_array($inputs) || !count($inputs)) {
            return $this->sendError('Inputs and key are required');
        }

        $result = $this->aiRepository->get($key, $inputs);

        return $this->sendSuccess($result);


    }
    public function index(): JsonResponse
    {
        $data = $this->aiRepository->getAllLatestPaginatedFiltered();
        return $this->sendSuccess(AiResource::generate($data));
    }
    /**
     * Store a new Ai record .
     * @param AiRequest $request
     * @return JsonResponse
     */
    public function store(AiRequest $request): JsonResponse
    {
        $data = $this->aiRepository->store($request->validated());
        return $this->sendSuccess(AiResource::generate($data));
    }

    /**
     * Show Ai by ID .
     *
     * @group Ais
     * @param int|string $aiId
     * @return JsonResponse
     */
    public function show(int|string $aiId): JsonResponse
    {
        $data = $this->aiRepository->findById($aiId);

        return $this->sendSuccess(AiResource::generate($data));
    }
    /**
     * Update Ai record .
     *
     * @group Ais
     * @param AiRequest $request
     * @param int|string $aiId
     * @return JsonResponse
     */
    public function update(AiRequest $request, int|string $aiId): JsonResponse
    {
        $data = $this->aiRepository->update($aiId, $request->validated());

        return $this->sendSuccess(AiResource::generate($data));
    }
    /**
     * Delete Ai record .
     * @param int|string $aiId
     * @return JsonResponse
     */

    public function destroy(int|string $aiId)
    {
        $this->aiRepository->destroy($aiId);
        return $this->sendSuccess();
    }
}