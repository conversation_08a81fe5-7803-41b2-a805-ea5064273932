<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\UserResource;
use App\Http\Requests\Admin\UserRequest;
use App\Repositories\User\UserRepositoryInterface;
use Illuminate\Http\JsonResponse;

class UserController extends ApiBaseController
{
    /**
     * @param   UserRepositoryInterface $userRepository
     */
    public function __construct(protected UserRepositoryInterface $userRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get Deleted Users.
     * @return JsonResponse
     */
    public function getDeletedUsers(): JsonResponse
    {
        $data = $this->userRepository->getDeletedUsers();
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Get all Users.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->userRepository->getAllUsers();
        return $this->sendSuccess(UserResource::generate($data));
    }

    /**
     * Store a new User record .
     * @param UserRequest $request
     * @return JsonResponse
     */
    public function store(UserRequest $request): JsonResponse
    {
        $data = $this->userRepository->storeUser($request->validated());
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Restore User
     * @param int|string $id
     * @return JsonResponse
     */
    public function restoreUser(int|string $id): JsonResponse
    {
        $data = $this->userRepository->restoreUser($id);
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Show User by ID .
     *
     * @group Users
     * @param int|string $userId
     * @return JsonResponse
     */
    public function show(int|string $userId): JsonResponse
    {
        $data = $this->userRepository->findByIdUser($userId);
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Show Deleted User by ID .
     *
     * @group Users
     * @param int|string $userId
     * @return JsonResponse
     */
    public function getDeletedUser(int|string $userId): JsonResponse
    {
        $data = $this->userRepository->getDeletedUser($userId);
        return $this->sendSuccess($data);
    }
    /**
     * Update User record .
     *
     * @group Users
     * @param UserRequest $request
     * @param int|string $userId
     * @return JsonResponse
     */
    public function update(UserRequest $request, int|string $userId): JsonResponse
    {
        $data = $this->userRepository->updateUser($userId, $request->validated());
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Update Delete User record .
     *
     * @group Users
     * @param UserRequest $request
     * @param int|string $userId
     * @return JsonResponse
     */
    public function updateDeletedUser(UserRequest $request, int|string $userId): JsonResponse
    {
        $data = $this->userRepository->updateDeletedUser($userId, $request->validated());
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Delete User record .
     * @param int|string $userId
     */
    public function destroy(int|string $userId)
    {
        $this->userRepository->deleteUser($userId);
        return $this->sendSuccess();
    }

    /**
     * Get all getRequestsChangeName.
     * @return JsonResponse
     */
    public function getRequestsChangeName(): JsonResponse
    {
        $data = $this->userRepository->getRequestsChangeName();
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Update Requests ChangeName .
     * @param int|string  $requestId
     * @return JsonResponse
     */
    public function activationRequestChangeName(int|string $requestId): JsonResponse
    {
        $data = $this->userRepository->activationRequestChangeName($requestId);
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Update Requests ChangeName .
     * @param int|string  $requestId
     * @return JsonResponse
     */
    public function pendingRequestChangeName(int|string $requestId): JsonResponse
    {
        $data = $this->userRepository->pendingRequestChangeName($requestId);
        return $this->sendSuccess(UserResource::generate($data));
    }
    /**
     * Delete Request ChangeName.
     * @param int|string $requestId
     */
    public function deleteRequestChangeName(int|string $requestId): JsonResponse
    {
        $this->userRepository->deleteRequestChangeName($requestId);
        return $this->sendSuccess();
    }


}