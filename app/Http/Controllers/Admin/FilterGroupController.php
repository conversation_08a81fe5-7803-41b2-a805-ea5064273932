<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\JsonResponse;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\FilterGroupRequest;
use App\Http\Requests\Admin\ReorderFilterRequest;
use App\Http\Resources\Admin\FilterGroupResource;
use App\Repositories\FilterGroup\FilterGroupRepositoryInterface;



class FilterGroupController extends ApiBaseController
{
    /**
     * @param  FilterGroupRepositoryInterface $filterGroupRepository
     */
    public function __construct(protected FilterGroupRepositoryInterface $filterGroupRepository)
    {
        $this->middleware('role:admin');

    }
    /**
     * Get all FilterGroup.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->filterGroupRepository->getAllFilterGroup();
        return $this->sendSuccess(FilterGroupResource::generate($data));
    }
    /**
     * Store a new Filter Group record .
     * @param FilterGroupRequest $request
     * @return JsonResponse
     */
    public function store(FilterGroupRequest $request)
    {
        $data = $this->filterGroupRepository->storeFilterGroup($request->validated());
        return $this->sendSuccess(FilterGroupResource::generate($data));
    }
    /**
     * Show Filter Group by ID .
     * @param int|string $filterGroupId
     * @return JsonResponse
     */
    public function show(int|string $filterGroupId): JsonResponse
    {
        $data = $this->filterGroupRepository->findByIdFilterGroup($filterGroupId);
        return $this->sendSuccess(FilterGroupResource::generate($data));
    }

    /**
     * Update  record .
     *
     * @group Filter Groups 
     * @param FilterGroupRequest $request
     * @param int|string  $filterGroupId
     * @return JsonResponse
     */
    public function update(FilterGroupRequest $request, int|string $filterGroupId): JsonResponse
    {
        $data = $this->filterGroupRepository->updateFilterGroup($filterGroupId, $request->all());
        return $this->sendSuccess(FilterGroupResource::generate($data));
    }

    /**
     * Delete Filter Group record .
     * @param int|string $filterGroupId
     * @return JsonResponse
     */

    public function destroy(int|string $filterGroupId)
    {
        $this->filterGroupRepository->deleteFilterGroup($filterGroupId);
        return $this->sendSuccess();
    }
    /**
     * Delete Filters .
     * @param int|string $filterGroupId
     * @return JsonResponse
     */

    public function reorderFilter(ReorderFilterRequest $request)
    {
        $this->filterGroupRepository->reorderFilter($request->validated());
        return $this->sendSuccess();

    }
}