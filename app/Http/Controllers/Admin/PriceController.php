<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;

use App\Http\Requests\Admin\PriceRequest;
use App\Http\Resources\Admin\PriceResource;
use App\Repositories\Price\PriceRepositoryInterface;
use Illuminate\Http\JsonResponse;


class PriceController extends ApiBaseController
{
    /**
     * @param  PriceRepositoryInterface $priceRepository
     */
    public function __construct(protected PriceRepositoryInterface $priceRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Prices .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->priceRepository->getAllPrices();
        return $this->sendSuccess(PriceResource::generate($data));
    }
    /**
     * Store a new price record .
     * @param PriceRequest $request
     * @return JsonResponse
     */
    public function store(PriceRequest $request)
    {
        $data = $this->priceRepository->storePrice($request->validated());
        return $this->sendSuccess(PriceResource::generate($data));
    }
    /**
     * Show Price by ID .
     * @param int|string $priceId
     * @return JsonResponse
     */
    public function show(int|string $priceId): JsonResponse
    {
        $data = $this->priceRepository->findByIdPrice($priceId);
        return $this->sendSuccess(PriceResource::generate($data));
    }

    /**
     * Update  record .
     *
     * @group Prices
     * @param PriceRequest $request
     * @param int|string  $priceId
     * @return JsonResponse
     */
    public function update(PriceRequest $request, int|string $priceId): JsonResponse
    {
        $data = $this->priceRepository->updatePrice($priceId, $request->validated());
        return $this->sendSuccess(PriceResource::generate($data));
    }

    /**
     * Delete Price record .
     * @param int|string $priceId
     * @return JsonResponse
     */

    public function destroy(int|string $priceId)
    {
        $this->priceRepository->deletePrice($priceId);
        return $this->sendSuccess();
    }
}