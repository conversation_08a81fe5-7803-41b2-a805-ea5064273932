<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\JsonResponse;
use App\Http\Requests\Admin\PermissionRequest;
use App\Http\Resources\Admin\PermissionResource;
use App\Http\Controllers\ApiBaseController;
use App\Repositories\Permission\PermissionRepositoryInterface;

class PermissionController extends ApiBaseController
{
    /**
     * @param  PermissionRepositoryInterface $permissionRepository
     */
    public function __construct(protected PermissionRepositoryInterface $permissionRepository)
    {
        $this->middleware('role:admin');
        // $this->middleware('permission:permission-list|permission-create|permission-edit|permission-delete', ['only' => ['index','store']]);
        // $this->middleware('permission:permission-create', ['only' => ['create','store']]);
        // $this->middleware('permission:permission-edit', ['only' => ['edit','update']]);
        // $this->middleware('permission:permission-delete', ['only' => ['destroy']]);
    }
    /**
     * Get all Permissions .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->permissionRepository->getAllPermissions();
        return $this->sendSuccess(PermissionResource::generate($data));
    }
    /**
     * Delete Permission record .
     * @param int|string $permissionId
     * @return JsonResponse
     */

    public function destroy(int|string $permissionId)
    {
        $this->permissionRepository->deletePermission($permissionId);
        return $this->sendSuccess();
    }


    /**
     * Store a new City record .
     * @param PermissionRequest $request
     * @return JsonResponse
     */
    public function store(PermissionRequest $request)
    {
        $data = $this->permissionRepository->storePermission($request->all());
        return $this->sendSuccess(PermissionResource::generate($data));
    }
    /**
     * Show City by ID .
     * @param int|string $permissionId
     * @return JsonResponse
     */
    public function show(int|string $permissionId): JsonResponse
    {
        $data = $this->permissionRepository->findByIdPermission($permissionId);
        return $this->sendSuccess(PermissionResource::generate($data));
    }


    /**
     * Store a new City record .
     * @param $permissionId
     * @param PermissionRequest $request
     * @return JsonResponse
     */
    public function update(PermissionRequest $request, int|string $permissionId)
    {
        $data = $this->permissionRepository->updatePermission($permissionId, $request->all());
        return $this->sendSuccess(PermissionResource::generate($data));
    }

}