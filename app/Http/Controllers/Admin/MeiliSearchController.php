<?php


namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\MailsearchFilterableResource;
use App\Http\Resources\Admin\MailsearchSortableResource;
use App\Models\Product;
use App\Repositories\Attribute\AttributeRepositoryInterface;
use Meilisearch\Client;

class MeiliSearchController extends ApiBaseController
{



    public function __construct()
    {
        $this->middleware('role:admin');
    }

    /**
     * Retrieve the filterable attributes for products from the MeiliSearch index.
     *
     * This function connects to the MeiliSearch instance using the configured host and key,
     * accesses the 'products' index, and retrieves the filterable attributes.
     *
     * @return \Illuminate\Http\JsonResponse A successful response containing the filterable attributes.
     */
    public function getFilterableAttributesProducts()
    {
        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));
        $index = $client->index('products');
        return $this->sendSuccess(MailsearchFilterableResource::generate($index->getFilterableAttributes()), 200);

    }

    /**
     * Update the filterable attributes for products in the MeiliSearch index.
     *
     * This function connects to the MeiliSearch instance using the configured host and key,
     * accesses the 'products' index, and updates the filterable attributes based on
     * predefined attributes and those retrieved from the attribute repository.
     *
     * @return \Illuminate\Http\JsonResponse A successful response containing the updated filterable attributes.
     */
    public function updateFilterableAttributesProducts()
    {
        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));
        $index = $client->index('products');
        $keys = resolve(AttributeRepositoryInterface::class)->getKeysFilterableAttributes();
        $filterableAttributes = array_merge(Product::$filterableAttributes, $keys);
        $index->updateFilterableAttributes($filterableAttributes);
        return $this->sendSuccess(MailsearchFilterableResource::generate($index->getFilterableAttributes()), 200);

    }


    /**
     * Retrieve the sortable attributes for products from the MeiliSearch index.
     *
     * This function connects to the MeiliSearch instance using the configured host and key,
     * accesses the 'products' index, and retrieves the sortable attributes.
     *
     * @return \Illuminate\Http\JsonResponse A successful response containing the sortable attributes.
     */
    public function getSortableAttributesProducts()
    {
        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));
        $index = $client->index('products');
        return $this->sendSuccess(MailsearchSortableResource::generate($index->getSortableAttributes()), 200);

    }

    public function updateSortableAttributesProducts()
    {
        $client = new Client(config('scout.meilisearch.host'), config('scout.meilisearch.key'));
        $index = $client->index('products');
        $keys = resolve(AttributeRepositoryInterface::class)->getKeysFilterableAttributes();
        $sortableAttributes = array_merge(Product::$sortableAttributes, $keys);
        $index->updateSortableAttributes($sortableAttributes);
        return $this->sendSuccess(MailsearchSortableResource::generate($index->getSortableAttributes()), 200);

    }


}