<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\JsonResponse;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\PageRequest;
use App\Http\Resources\Admin\PageResource;
use App\Repositories\Page\PageRepositoryInterface;

class PageController extends ApiBaseController
{
    /**
     * @param  PageRepositoryInterface $pageRepository
     */
    public function __construct(protected PageRepositoryInterface $pageRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Pages .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->pageRepository->getAllPages();
        return $this->sendSuccess(PageResource::generate($data));
    }
    /**
     * Store a new Page record .
     * @param PageRequest $request
     * @return JsonResponse
     */
    public function store(PageRequest $request)
    {
        $data = $this->pageRepository->storePage($request->validated());
        return $this->sendSuccess(PageResource::generate($data));
    }
    /**
     * Show Page by ID .
     * @param int|string $page
     * @return JsonResponse
     */
    public function show(int|string $pageId): JsonResponse
    {
        $data = $this->pageRepository->findByIdPage($pageId);
        return $this->sendSuccess(PageResource::generate($data));
    }

    /**
     * Update Page record .
     *
     * @param PageRequest $request
     * @param int|string $pageId
     * @return JsonResponse
     */
    public function update(PageRequest $request, int|string $pageId): JsonResponse
    {
        $data = $this->pageRepository->updatePage($pageId, $request->validated());
        return $this->sendSuccess(PageResource::generate($data));
    }

    /**
     * Delete Page record .
     * @param int|string $pageId
     * @return JsonResponse
     */

    public function destroy(int|string $pageId)
    {
        $this->pageRepository->deletePage($pageId);
        return $this->sendSuccess();
    }

}