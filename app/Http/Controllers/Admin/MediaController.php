<?php

namespace App\Http\Controllers\Admin;


use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Models\Media;
use Illuminate\Http\Request;

class MediaController extends ApiBaseController
{


    public function destroy(int|string $id)
    {

        Media::destroy($id);

        return $this->sendSuccess();
    }

    public function deleteMediaByIdes(Request $request)
    {

        Media::whereIn('id', $request->ides)->delete();

        return $this->sendSuccess();
    }




}