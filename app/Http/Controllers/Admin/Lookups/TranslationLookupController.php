<?php

namespace App\Http\Controllers\Admin\Lookups;

use App\Http\Controllers\ApiBaseController;
use App\Repositories\TranslationKey\TranslationKeyRepositoryInterface;
use Illuminate\Http\JsonResponse;

class TranslationLookupController extends ApiBaseController
{
    /**
     * @param TranslationKeyRepositoryInterface $translationRepository
     */
    public function __construct(
        protected TranslationKeyRepositoryInterface $translationRepository,
    ) {
    }

    /**
     * Get all translations based key .
     *
     * @group Translations
     * @param string $languageAbbreviation
     * @param string|null $groups
     * @return JsonResponse
     */
    public function __invoke(string $languageAbbreviation, string $groups = null): JsonResponse
    {
        $groups = array_filter(explode('|', $groups));

        $data = $this->translationRepository->getAllBasedKeys($languageAbbreviation, $groups);

        return $this->sendSuccess($data);
    }
}