<?php

namespace App\Http\Controllers\Admin\Lookups;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Languages\LanguageResource;
use App\Repositories\Language\LanguageRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LanguageLookupController extends ApiBaseController
{
    /**
     * @param Request $request
     * @param LanguageRepositoryInterface $languageRepository
     * @return JsonResponse
     */
    public function __invoke(Request $request, LanguageRepositoryInterface $languageRepository): JsonResponse
    {
        $data = $languageRepository->getAllLanguages();

        return $this->sendSuccess(LanguageResource::generate($data));
    }
}