<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\SupplierRequest;
use App\Http\Resources\Admin\SupplierResource;
use App\Models\User;
use App\Repositories\Supplier\SupplierRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Spatie\Activitylog\Facades\CauserResolver;


class SupplierController extends ApiBaseController
{
    /**
     * @param  SupplierRepositoryInterface $supplierRepository
     */
    public function __construct(protected SupplierRepositoryInterface $supplierRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Suppliers.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->supplierRepository->getAllSuppliers();
        return $this->sendSuccess(SupplierResource::generate($data));
    }
    /**
     * Store a new Supplier record .
     * @param SupplierRequest $request
     * @return JsonResponse
     */
    public function store(SupplierRequest $request)
    {
        $data = $this->supplierRepository->storeSupplier($request->all());
        return $this->sendSuccess(SupplierResource::generate($data));
    }
    /**
     * Show Supplier by ID .
     * @param int|string $supplierId
     * @return JsonResponse
     */
    public function show(int|string $supplierId): JsonResponse
    {
        $data = $this->supplierRepository->findByIdSupplier($supplierId);
        return $this->sendSuccess(SupplierResource::generate($data));
    }

    /**
     * Update Supplier record .
     *
     * @group Suppliers
     * @param SupplierRequest $request
     * @param int|string $supplierId
     * @return JsonResponse
     */
    public function update(SupplierRequest $request, int|string $supplierId): JsonResponse
    {


        $data = $this->supplierRepository->updateSupplier($supplierId, $request->validated());
        return $this->sendSuccess(SupplierResource::generate($data));
    }

    /**
     * Delete Supplier record .
     * @param int|string $supplierId
     * @return JsonResponse
     */

    public function destroy(int|string $supplierId)
    {
        $this->supplierRepository->deleteSupplier($supplierId);
        return $this->sendSuccess();
    }

}