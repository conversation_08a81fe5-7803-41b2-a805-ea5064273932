<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\RatingResource;
use App\Repositories\Rating\RatingRepositoryInterface;
use Illuminate\Http\JsonResponse;

class RatingController extends ApiBaseController
{
    /**
     * @param  RatingRepositoryInterface $ratingRepository
     */
    public function __construct(protected RatingRepositoryInterface $ratingRepository)
    {
        $this->middleware('role:admin');
    }
    /**
     * Get all Ratings .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->ratingRepository->getAllRatings();
        return $this->sendSuccess(RatingResource::generate($data));
    }
    /**
     * Delete Rating record .
     * @param int|string $ratingId
     * @return JsonResponse
     */

    public function destroy(int|string $ratingId)
    {
        $this->ratingRepository->deleteRating($ratingId);
        return $this->sendSuccess();
    }

    /**
     * published Rating by ID .
     * @param int|string $ratingId
     * @return JsonResponse
     */

    public function publishingRating(int|string $ratingId)
    {
        $data = $this->ratingRepository->publishingRating($ratingId);
        return $this->sendSuccess($data);
    }

}