<?php

namespace App\Http\Controllers\Admin\Portal;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Portal\Translation\StoreTranslationRequest;
use App\Http\Requests\Portal\Translation\UpdateTranslationRequest;
use App\Http\Requests\Portal\Translation\ExportTranslationsRequest;
use App\Http\Requests\Portal\Translation\ImportTranslationsRequest;
use App\Http\Resources\Translation\TranslationKeyResource;
use App\Repositories\TranslationKey\TranslationKeyRepositoryInterface;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TranslationController extends ApiBaseController
{
    /**
     * @param TranslationKeyRepositoryInterface $translationKeyRepository
     */
    public function __construct(protected TranslationKeyRepositoryInterface $translationKeyRepository)
    {
    }

    /**
     * Get all static translations .
     *
     * @group Static Translations
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->translationKeyRepository->getAllPaginatedWithLanguages();

        return $this->sendSuccess(TranslationKeyResource::generate($data));
    }

    /**
     * Store a new static translation record .
     *
     * @group Static Translations
     * @param StoreTranslationRequest $request
     * @return JsonResponse
     */
    public function store(StoreTranslationRequest $request): JsonResponse
    {
        $data = $this->translationKeyRepository->storeFromMultiLanguages($request->validated());

        return $this->sendSuccess(translationKeyResource::generate($data));
    }

    /**
     * Update static translation record .
     *
     * @group Static Translations
     * @param UpdateTranslationRequest $request
     * @param int|string $keyId
     * @return JsonResponse
     */
    public function update(UpdateTranslationRequest $request, int|string $keyId): JsonResponse
    {
        $data = $this->translationKeyRepository->updateFromMultiLanguages($keyId, $request->validated());

        return $this->sendSuccess(TranslationKeyResource::generate($data));
    }

    /**
     * Find static translation by ID .
     *
     * @group Static Translations
     * @param int|string $staticTranslationId
     * @return JsonResponse
     */
    public function show(string|int $staticTranslationId): JsonResponse
    {
        $data = $this->translationKeyRepository->findByIdWithLanguages(id: $staticTranslationId);

        return $this->sendSuccess(TranslationKeyResource::generate($data));
    }

    /**
     * Delete static translation record .
     *
     * @group Static Translations
     * @param int $staticTranslationId
     * @return JsonResponse
     */
    public function destroy(int $staticTranslationId): JsonResponse
    {
        $this->translationKeyRepository->destroy($staticTranslationId);

        return $this->sendSuccess(message: __('response_messages.successfully_deleted'));
    }

    /**
     * @param Request $request
     * @param string $language
     * @return JsonResponse
     */
    public function patchUpdate(Request $request, string $language): JsonResponse
    {
        $data = $this->translationKeyRepository->storeFromMultiKeys($request->all(), $language);

        return $this->sendSuccess($data);
    }

    /**
     * @param \App\Http\Requests\Translation\ImportTranslationsRequest $request
     * @return JsonResponse
     */
    public function import(ImportTranslationsRequest $request): JsonResponse
    {
        $this->translationKeyRepository->importFromFile($request->file('file'));

        return $this->sendSuccess();
    }

    /**
     * @param ExportTranslationsRequest $request
     * @return mixed
     */
    public function export(ExportTranslationsRequest $request): mixed
    {
        return $this->translationKeyRepository->exportToFile(
            source: $request->input('source_language'),
            target: $request->input('target_language')
        );
    }
}