<?php

namespace App\Http\Controllers\Admin\Portal;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\TranslationTag\TranslationTagsResource;
use App\Repositories\TranslationTag\TranslationTagRepositoryInterface;
use App\Http\Requests\Portal\Translation\TranslationTagRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
class TranslationTagController extends ApiBaseController
{
    public function __construct(protected TranslationTagRepositoryInterface $translationTagRepository)
    {
    }

    public function index()
    {
        $data = $this->translationTagRepository->getAllTags();

        return $this->sendSuccess(TranslationTagsResource::generate($data));
    }
    /**
     * Store a new tag record .
     * @param TranslationTagRequest  $request
     * @return JsonResponse
     */
    public function store(TranslationTagRequest $request)
    {
        $data = $this->translationTagRepository->storeTag($request->validated());
        return $this->sendSuccess(TranslationTagsResource::generate($data));
    }

    /**
     * Update  record .
     *
     * @group Tags
     * @param TranslationTagRequest; $request
     * @param int|string  $ShippingId
     * @return JsonResponse
     */
    public function update(TranslationTagRequest $request, int|string $tagId): JsonResponse
    {
        $data = $this->translationTagRepository->updateTag($tagId, $request->validated());
        return $this->sendSuccess(TranslationTagsResource::generate($data));
    }

    /**
     * Delete Tag record .
     * @param int|string $shippingId
     * @return JsonResponse
     */

    public function destroy(int|string $tagId)
    {
        $this->translationTagRepository->deleteTag($tagId);
        return $this->sendSuccess();
    } 

    public function show(int|string $id)
    {
        $data = $this->translationTagRepository->findByIdTag($id);

        return $this->sendSuccess(TranslationTagsResource::generate($data));
    }
}