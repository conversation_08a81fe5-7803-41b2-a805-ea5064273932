<?php

namespace App\Http\Controllers\Admin\Portal;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Languages\LanguageResource;
use App\Http\Resources\Languages\LanguageWithDefaultResource;
use App\Models\Language;

use App\Repositories\Language\LanguageRepositoryInterface;
use Illuminate\Http\JsonResponse;

class LanguageController extends ApiBaseController
{
    /**
     * @param LanguageRepositoryInterface $languageRepository
     */
    public function __construct(protected LanguageRepositoryInterface $languageRepository)
    {
    }
    /**
     * Get all supported languages .
     *
     * @group Supported Languages
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->languageRepository->getAllWithDefaultLanguage();

        return $this->sendSuccess(LanguageResource::generate($data));
    }

    /**
     * Show supported language by abbreviation .
     *
     * @group Supported Languages
     *
     * @param string $languageAbbreviation
     * @return JsonResponse
     */
    public function show(string $languageAbbreviation): JsonResponse
    {
        $data = $this->languageRepository->findByAbbreviation($languageAbbreviation);

        return $this->sendSuccess(LanguageResource::generate($data));
    }
}