<?php

namespace App\Http\Controllers\Website;
use App\Http\Resources\Website\BranchResource;
use App\Repositories\Branch\BranchRepositoryInterface;
use App\Http\Controllers\ApiBaseController;
use Illuminate\Http\JsonResponse;

class BranchController extends ApiBaseController
{
    /**
     * @param  BranchRepositoryInterface $branchRepository
     */
    public function __construct(protected BranchRepositoryInterface $branchRepository)
    {
    }
    /**
     * Get all Branches
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->branchRepository->getAllBranches();
        return $this->sendSuccess(BranchResource::generate($data));
    }

}
