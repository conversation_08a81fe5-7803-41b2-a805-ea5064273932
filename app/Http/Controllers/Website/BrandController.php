<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\BrandResource;
use App\Repositories\Brand\BrandRepositoryInterface;
use Illuminate\Http\JsonResponse;

class BrandController extends ApiBaseController
{
    /**
     * @param  BrandRepositoryInterface $brandRepository
     */
    public function __construct(protected BrandRepositoryInterface $brandRepository)
    {
    }
    /**
     * Get Brands
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->brandRepository->getAllBrands();
        return $this->sendSuccess(BrandResource::generate($data));
    }

    /**
     * Show Brand by brand.
     *
     * @param int|string $brand
     * @return JsonResponse
     */
    public function show(int|string $brand): mixed
    {
        $data = $this->brandRepository->findBySlugBrand($brand);
        return $this->sendSuccess(BrandResource::generate($data));

    }




}