<?php

namespace App\Http\Controllers\Website;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\UsedProductRequest;
use App\Http\Resources\Website\UsedProductResource;
use App\Http\Resources\Website\UsedProductDetailsResource;
use App\Repositories\UsedProduct\UsedProductRepositoryInterface;


class UsedProductController extends ApiBaseController
{
    /**
     * @param  UsedProductRepositoryInterface $usedProductRepository
     */
    public function __construct(protected UsedProductRepositoryInterface $usedProductRepository)
    {
    }

    /**
     * Show Used Product 
     *
     * @param int|string $product
     * @return JsonResponse
     */
    public function  show(int|string $usedProduct): JsonResponse
    {
        $data = $this->usedProductRepository->getUsedProductDetails($usedProduct);
        return $this->sendSuccess(UsedProductDetailsResource::generate($data));
    }


    /**
     * Get getProductsByCategory
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $data = $this->usedProductRepository->getUsedProductsByUsedCategory($request->usedCategory);
        return $this->sendSuccess(UsedProductResource::generate($data));
    }

    /**
     * Show User Used Product 
     *
     * @param int|string $product
     * @return JsonResponse
     */

   public function  showUserUsedProduct(int|string $usedProduct): JsonResponse
    {
        $data = $this->usedProductRepository->findByIdUserUsedProduct($usedProduct);
        return $this->sendSuccess(UsedProductResource::generate($data));
    }

    /**
     * Show User Used Product 
     *
     * @param int|string $product
     * @return JsonResponse
     */

    public function showUserUsedProductDetails(int|string $usedProduct): JsonResponse
    {
        $data = $this->usedProductRepository->getUsedProductDetails($usedProduct);
        return $this->sendSuccess(UsedProductDetailsResource::generate($data));
    }

    /**
     * Get  get User Products 
     * @return JsonResponse
     */
    public function getUserUsedProducts(): JsonResponse
    {
        $data = $this->usedProductRepository->getUserUsedProducts();
        return $this->sendSuccess(UsedProductResource::generate($data));
    }

    /**
     * Store Used Product By User.
     * @param UsedProductRequest; $request
     * @return JsonResponse
     */
    public function addUsedProductByUser(UsedProductRequest $request): JsonResponse
    {
        $data = $this->usedProductRepository->addUsedProductByUser($request->validated());
        return $this->sendSuccess(UsedProductResource::generate($data));
    }

    /**
     * Update Used Product By User.
     * @param UsedProductRequest; $request
     * @return JsonResponse
     */
    public function updateUserUsedProduct(UsedProductRequest $request, int|string $usedProduct): JsonResponse
    {   $data = $this->usedProductRepository->updateUserUsedProduct($request->validated(),$usedProduct);
        return $this->sendSuccess(UsedProductResource::generate($data));
    }

    /**
     * Delete User UsedProduct record .
     * @param int|string $usedProductId
     * @return JsonResponse
     */

    public function destroy(int|string $usedProductId)
    {
        $this->usedProductRepository->deleteUserUsedProduct($usedProductId);
        return $this->sendSuccess();
    }

}
