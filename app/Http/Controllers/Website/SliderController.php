<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\SliderResource;
use App\Http\Requests\Admin\SliderRequest;
use App\Repositories\Slider\SliderRepositoryInterface;
use Illuminate\Http\JsonResponse;

class SliderController extends ApiBaseController
{
    /**
     * @param  SliderRepositoryInterface $sliderRepository
     */
    public function __construct(protected SliderRepositoryInterface $sliderRepository)
    {

    }
    /**
     * Get all Shippings Methods .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->sliderRepository->getPublished();
        return $this->sendSuccess(SliderResource::generate($data));
    }

}