<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Website\UsedCategoryResource;
use App\Repositories\UsedCategory\UsedCategoryRepositoryInterface;
use Illuminate\Http\JsonResponse;


class UsedCategoryController extends ApiBaseController
{
 /**
     * @param  UsedCategoryRepositoryInterface $usedCategoryRepository
     */
    public function __construct(protected UsedCategoryRepositoryInterface $usedCategoryRepository)
    {
    }
    /**
     * Get all Used Categories .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->usedCategoryRepository->getAllUsedCategories();
        return $this->sendSuccess(UsedCategoryResource::generate($data));
    }

    /**
     * Show Used Category by slug .
     *
     * @param int|string $category
     * @return JsonResponse
     */
    public function show($category): JsonResponse
    {
        $data = $this->usedCategoryRepository->findBySlugUsedCategory($category);
        return $this->sendSuccess(UsedCategoryResource::generate($data));
    }

}
