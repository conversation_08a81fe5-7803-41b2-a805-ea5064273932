<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\WishlistRequest;
use App\Http\Resources\Website\ProductResource;
use App\Http\Resources\Website\WishlistResource;
use App\Repositories\Wishlist\WishlistRepositoryInterface;

use Illuminate\Http\JsonResponse;

class WishlistController extends ApiBaseController
{
    /**
     * @param  WishlistRepositoryInterface $wishlistRepository
     */
    public function __construct(protected WishlistRepositoryInterface $wishlistRepository)
    {
    }
    /**
     * Get User Wishlist
     * @return JsonResponse
     */

    public function getUserWishlist(): JsonResponse
    {
        $data = $this->wishlistRepository->getUserWishlist();
        return $this->sendSuccess(WishlistResource::generate($data));
    }
    /**
     * remove From Wishlist .
     * @param string|int $productId
     */
    public function removeFromWishlist($productId)
    {

        $data = $this->wishlistRepository->removeFromWishlist($productId);
        return $this->sendSuccess(ProductResource::generate($data));

    }
    /**
     *  add To Wishlist .
     * @param WishlistRequest $request
     * @return JsonResponse
     */
    public function addToWishlist(WishlistRequest $request): JsonResponse
    {

        $data = $this->wishlistRepository->addToWishlist($request->all());
        return $this->sendSuccess(WishlistResource::generate($data));
    }


}