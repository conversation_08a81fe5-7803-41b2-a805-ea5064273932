<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\OptionsResource;
use App\Http\Resources\Website\RawSearchResource;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use MeiliSearch\Endpoints\Indexes;
use App\Models\Variance;

class AutocompleteController extends ApiBaseController
{
    /* DEPRECATED */

    public function __invoke(Request $request): JsonResponse
    {
        $q = trim($request->get('q'));
        $products = [];
        $categories = [];


        if ($q) {
            $products = Variance::search(
                $q,
                function (Indexes $meiliSearch, string $query, array $options) {
                    $options['sort'] = ['score:desc']; //!TODO later: add sort by popularity
                    $options['limit'] = 6;
                    return $meiliSearch->search($query, $options);
                }
            )->query(function ($query) {
                $query->with(
                    [
                        'product',
                        'product.media',
                        'media'
                    ]
                );
            })->get();

            // $categories = Category::search(
            //     $q,
            //     function (Indexes $meiliSearch, string $query, array $options) {
            //         // $options['sort'] = ['createdAt:desc'];
            //         $options['limit'] = 6;
            //         return $meiliSearch->search($query, $options);
            //     }
            // )->query(function ($query) {
            //     $query->with(
            //         [
            //             'media'
            //         ]
            //     );
            // })->get();
        }

        return $this->sendSuccess(
            //'p' => $categories,
            // 'products' => new OptionsResource(
            //     resource: $products,
            //     textColumn: ['product.name', 'name'],
            //     valueColumn: ['productSlug' => 'product.slug', 'varianceSlug' => 'slug'],
            //     meta: ['media', 'product'],
            // ),
            // 'categories' => new OptionsResource(
            //     resource: $categories,
            //     textColumn: 'name',
            //     valueColumn: 'slug',
            //     meta: ['media'],
            // ),
            RawSearchResource::generate($products, Variance::class)
        );
    }


}
