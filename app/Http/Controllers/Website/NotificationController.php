<?php

namespace App\Http\Controllers\Website;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;

use App\Http\Resources\Website\NotificationResource;
use App\Repositories\Notification\NotificationRepositoryInterface;

class NotificationController extends ApiBaseController
{ /**
  * @param  NotificationRepositoryInterface $notificationRepository
  */
    public function __construct(protected NotificationRepositoryInterface $notificationRepository)
    {
    }


       /**
     * Get User Orders.
     * @return JsonResponse
     */
    public function index(): mixed
    {
        $data = $this->notificationRepository->getNotifications();
        return $this->sendSuccess(NotificationResource::generate($data));
    }

    /**
     * Get User Orders.
     * @return JsonResponse
     */
    public function readIt(int|string $notificationId): mixed
    {
        $data = $this->notificationRepository->readIt($notificationId);
        return $this->sendSuccess(NotificationResource::generate($data));
    }


    


}