<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Website\SearchResource;
use App\Http\Resources\Website\SearchVarianceResource;
use App\Models\Variance;
use Illuminate\Http\Request;
use App\Repositories\SearchVariance\SearchVarianceRepositoryInterface;


class VarianceSearchController extends ApiBaseController
{


    public function __construct(protected SearchVarianceRepositoryInterface $searchRepository)
    {
    }



    public function index(Request $request)
    {
        $variances = $this->searchRepository->search(
            searchTerm: $request->q ?? '',
            page: $request->page ?? 1,
            limit: $request->perPage ?? 26,
        );

        return $this->sendSuccess(SearchVarianceResource::generate($variances));

    }


}