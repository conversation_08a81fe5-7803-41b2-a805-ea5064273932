<?php

namespace App\Http\Controllers\website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\RequestChangeNameAccountRequest;
use App\Http\Requests\Website\UserProfileImageRequest;
use App\Http\Requests\Website\AccountRequest;
use App\Http\Requests\Website\UpdatePasswordRequest;
use App\Http\Requests\Website\RequestChangeRequest;
use App\Http\Resources\Website\AccountResource;
use App\Repositories\Account\AccountRepositoryInterface;
use Illuminate\Http\JsonResponse;

class AccountController extends ApiBaseController
{
    /**
     * @param  AccountRepositoryInterface $accountRepository
     */
    public function __construct(protected AccountRepositoryInterface $accountRepository)
    {
    }


    /**
     * Get User Information 
     * @return User Auth
     */
    public function getUserInformation(): JsonResponse
    {
        $data = $this->accountRepository->getUserInformation();
        return $this->sendSuccess(AccountResource::generate($data));

    }


    /**
     * Get User Information 
     * @return User Auth
     */
    public function updateUserName(RequestChangeNameAccountRequest $request): JsonResponse
    {
        $data = $this->accountRepository->updateUserName($request->all());
        return $this->sendSuccess(AccountResource::generate($data));

    }



    /**
     * Get User General Information
     * @return User 
     */
    public function getUserGeneralInformation(): JsonResponse
    {
        $data = $this->accountRepository->getUserGeneralInformation();
        return $this->sendSuccess(AccountResource::generate($data));

    }

    /**
     *update User Information .
     * @param AccountRequest $request
     * @return JsonResponse
     */
    public function updateUserInformation(AccountRequest $request): JsonResponse
    {
        $data = $this->accountRepository->updateUserInformation($request->all());
        return $this->sendSuccess(AccountResource::generate($data));
    }


    /**
     *update User Information .
     * @param AccountRequest $request
     * @return JsonResponse
     */
    public function updateUserPhoneNumber(AccountRequest $request): JsonResponse
    {
        $data = $this->accountRepository->updateUserPhoneNumber($request->all());
        return $this->sendSuccess(AccountResource::generate($data));
    }



    /**
     *update User Information .
     * @param AccountRequest $request
     * @return JsonResponse
     */
    public function verifyUserOtp(AccountRequest $request): JsonResponse
    {
        $data = $this->accountRepository->verifyUserOtp($request->all());
        return $this->sendSuccess(AccountResource::generate($data));
    }


    /**
     *update User Information .
     * @param AccountRequest $request
     * @return JsonResponse
     */
    public function updateUserEmail(AccountRequest $request): JsonResponse
    {
        $data = $this->accountRepository->updateUserEmail($request->all());
        return $this->sendSuccess(AccountResource::generate($data));
    }


    /**
     *update User Information .
     * @param AccountRequest $request
     * @return JsonResponse
     */
    public function updateUserImage(UserProfileImageRequest $request): JsonResponse
    {
        $data = $this->accountRepository->updateUserImage($request->validated());
        return $this->sendSuccess(AccountResource::generate($data));
    }
    /**
     *update Update Password .
     * @param UpdatePasswordRequest $request
     * @return JsonResponse
     */
    public function updatePassword(UpdatePasswordRequest $request): JsonResponse
    {
        $data = $this->accountRepository->updatePassword($request->validated());
        return $this->sendSuccess(($data));
    }
    /**
     *create request change  .
     * @param UpdatePasswordRequest $request
     * @return JsonResponse
     */
    public function requestChange(RequestChangeRequest $request): JsonResponse
    {
        $data = $this->accountRepository->requestChange($request->all());
        return $this->sendSuccess(($data));
    }


    public function getTransactions()
    {
        $data = $this->accountRepository->getTransactions();
        return $this->sendSuccess(($data));
    }


}