<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Repositories\Auth\AuthRepositoryInterface;
use App\Helper\ProviderSMS\SMSInterfaceHelper;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use Auth;
use App\Models\MobileCode;
use App\Http\Resources\Auth\LoginResource;
use App\Http\Resources\Auth\RegisterResource;
use App\Http\Resources\Auth\AuthResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Mail\ResetPassword;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class AuthController extends ApiBaseController
{
    /**
     * @param  AuthRepositoryInterface $authRepository
     */

    public function __construct(
        protected AuthRepositoryInterface $authRepository,
        //   protected SMSInterfaceHelper $ArabiacellHelper,
        //    protected SMSInterfaceHelper $JosmsServiceHelper,
        //protected SMSInterfaceHelper $ReleansHelperHelper,
    )
    {
    }
    /**
     * login
     */

    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->only('phone', 'password');
        if (!Auth::attempt($credentials)) {
            return $this->sendError([], 'login fails');
        }
        $data = $this->authRepository->login($request->validated());
        return $this->sendSuccess(LoginResource::generate($data));
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        $data = $this->authRepository->register($request->validated());
        return $this->sendSuccess(RegisterResource::generate($data));
    }



    public function forgotPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => ['nullable'],
            'phone' => ['nullable','integer','exists:users'],

        ]);

        $verify = User::where('email', $request->all()['email'])->exists();

        if ($verify) {
            $verify2 = DB::table('password_resets')->where([
                ['email', $request->all()['email']]
            ]);

            if ($verify2->exists()) {
                $verify2->delete();
            }

            $token = random_int(100000, 999999);
            $password_reset = DB::table('password_resets')->insert([
                'email' => $request->all()['email'],
                'token' => $token,
                'createdAt' => Carbon::now()
            ]);

            if ($password_reset) {
                Mail::to($request->all()['email'])->send(new ResetPassword($token));

                return new JsonResponse(
                    [
                        'success' => true,
                        'message' => "Please check your email for a 6 digit pin"
                    ],
                    200
                );
            }
        } else {
            return new JsonResponse(
                [
                    'success' => false,
                    'message' => "This email does not exist"
                ],
                400
            );
        }
    }

    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        $data = $this->authRepository->resetPassword($request->validated());

        return $this->sendSuccess([
            'message' => "Your password has been reset",
            'token' => $data
        ], );


    }

    public function logout()
    {

        $this->authRepository->logout();

        return $this->sendSuccess([
            'success' => true,
            'message' => 'Logged Out Successfully'
        ], );


    }
    public function sendOtpVerificationCode()
    {
        $mobileCode = MobileCode::where('userId', Auth::id())
            ->where('createdAt', '>=', Carbon::now()->subMinutes(2))->first();


        if (!$mobileCode && Auth::user()->otpVerifiedAt == null) {
            $phoneNumber = substr(Auth::user()->mobile, 1);
            $code = mt_rand(100000, 999999);

            $mobileCode = MobileCode::create([
                'verificationCode' => $code,
            ]);

            $message = $code;

            // $this->ArabiacellHelper->send($message, $phoneNumber);
            // $this->JosmsServiceHelper->send($message, $phoneNumber);
            //  $this->ReleansHelperHelper->send($message, $phoneNumber);


            return response()->json([
                'sentAt' => $mobileCode->createdAt
            ], 200);
        }

        return response()->json([
            'sentAt' => $mobileCode ? $mobileCode->createdAt : Carbon::now()
        ], 200);
    }


}