<?php

namespace App\Http\Controllers\Website;

use App\Http\Resources\Admin\RatingResource;
use App\Repositories\Rating\RatingRepositoryInterface;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\RatingModelRequest;
use App\Http\Resources\Admin\RatingModelResource;

class RatingController extends ApiBaseController
{

    /**
     * @param  RatingRepositoryInterface $ratingRepository
     */
    public function __construct(protected RatingRepositoryInterface $ratingRepository)
    {
    }
    /**
     * Get User Ratings .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->ratingRepository->getUserRatings();
        return $this->sendSuccess(RatingResource::generate($data));
    }


    /**
     * Store a new Rating record .
     * @param RatingModelRequest $request
     * @return JsonResponse
     */
    public function store(RatingModelRequest $request): JsonResponse
    {
        $data = $this->ratingRepository->storeRatingModel($request->all());
        return $this->sendSuccess(RatingModelResource::generate($data));
    }

    /**
     * Update Rating record .
     * @param RatingModelRequest $request
     * @param int|string $ratingModelId
     * @return JsonResponse
     */
    public function update(RatingModelRequest $request, int|string $ratingModelId): JsonResponse
    {
        $data = $this->ratingRepository->updateRatingModel($ratingModelId, $request->all());
        return $this->sendSuccess(RatingResource::generate($data));
    }
}