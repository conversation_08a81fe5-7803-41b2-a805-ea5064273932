<?php

namespace App\Http\Controllers\Website;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\SubscriptionStockRequest;
use App\Http\Resources\Website\SubscriptionStockResource;
use App\Repositories\SubscriptionStock\SubscriptionStockRepositoryInterface;

class SubscribeController extends ApiBaseController
{

    /**
     * @param  SubscriptionStockRepositoryInterface $subscriptionStockRepository
     */
    public function __construct(protected SubscriptionStockRepositoryInterface $subscriptionStockRepository)
    {
    }


    /**
     * Store a new Country record .
     * @param SubscriptionStockRequest $request
     * @return JsonResponse
     */
    public function subscribe(SubscriptionStockRequest $request)
    {
        $data = $this->subscriptionStockRepository->store($request->all());

        return $this->sendSuccess(SubscriptionStockResource::generate($data));
    }



}