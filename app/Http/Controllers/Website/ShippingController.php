<?php

namespace App\Http\Controllers\Website;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\ShippingResource;
use App\Repositories\Shipping\ShippingRepositoryInterface;



class ShippingController extends ApiBaseController
{
    /**
     * @param  ShippingRepositoryInterface $shippingRepository
     */
    public function __construct(protected ShippingRepositoryInterface $shippingRepository)
    {
    }
    /**
     * Get all Pages .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->shippingRepository->getAllShipping();
        return $this->sendSuccess(ShippingResource::generate($data));
    }

}