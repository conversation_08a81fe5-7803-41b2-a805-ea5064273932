<?php

namespace App\Http\Controllers\Website;


use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\WalletRequest;
use App\Http\Resources\Website\WalletResource;
use App\Repositories\Wallet\WalletRepositoryInterface;
use Illuminate\Http\JsonResponse;


class WalletController extends ApiBaseController
{
    /**
     * @param  WalletRepositoryInterface $WalletRepository
     */
    public function __construct(protected WalletRepositoryInterface $walletRepository)
    {
    }
    /**
     * Get all Pages .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {

        $data = $this->walletRepository->getAllPaymentMethod();
        return $this->sendSuccess(WalletResource::generate($data));
    }

    /**
     * Show Page by slug .
     *
     * @param int|string $page
     * @return JsonResponse
     */
    public function set(WalletRequest $walletRequest): JsonResponse
    {
        $data = $this->walletRepository->set($walletRequest);
        return $this->sendSuccess($data);
    }

}