<?php

namespace App\Http\Controllers\Website;



use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Admin\RatingModelRequest;
use App\Http\Requests\Website\RatingComplaintRequest;
use App\Http\Resources\Admin\ComplaintResource;
use App\Repositories\Complaint\ComplaintRepositoryInterface;

class ComplaintController extends ApiBaseController
{

    /**
     * @param  ComplaintRepositoryInterface $complaintRepository
     */
    public function __construct(protected ComplaintRepositoryInterface $complaintRepository)
    {
    }
    /**
     * Get User Ratings .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->complaintRepository->getAll();
        return $this->sendSuccess(ComplaintResource::generate($data));
    }


    /**
     * Store a new Rating record .
     * @param RatingComplaintRequest $request
     * @return JsonResponse
     */
    public function store(RatingComplaintRequest $request): JsonResponse
    {
        $data = $this->complaintRepository->storeRating($request->all());
        return $this->sendSuccess(ComplaintResource::generate($data));
    }

    /**
     * Update Rating record .
     * @param RatingComplaintRequest $request
     * @param int|string $ratingComplaintId
     * @return JsonResponse
     */
    public function update(RatingModelRequest $request, int|string $ratingComplaintId): JsonResponse
    {
        $data = $this->complaintRepository->updateRating($ratingComplaintId, $request->all());
        return $this->sendSuccess(ComplaintResource::generate($data));
    }
}