<?php

namespace App\Http\Controllers\Website;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\PromotionRequest;
use App\Http\Resources\Website\PromotionResource;
use App\Repositories\Promotion\PromotionRepositoryInterface;


class PromotionController extends ApiBaseController
{
    /**
     * @param  PromotionRepositoryInterface $promotionRepository
     */
    public function __construct(protected PromotionRepositoryInterface $promotionRepository)
    {
    }
        /**
     * Get all Pages .
     * @return JsonResponse
     */
    public function checked(PromotionRequest $request): JsonResponse
    {
        $data = $this->promotionRepository->checkedPromotion($request->all());
        return $this->sendSuccess(PromotionResource::generate($data));
    }

}