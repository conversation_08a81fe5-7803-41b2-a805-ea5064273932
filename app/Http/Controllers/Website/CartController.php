<?php

namespace App\Http\Controllers\Website;

use App\Helper\Calculator;
use App\Http\Resources\Website\CartResource;
use App\Http\Requests\Website\CartRequest;
use App\Models\Cart;
use App\Repositories\Cart\CartRepositoryInterface;
use App\Http\Controllers\ApiBaseController;
use Illuminate\Http\JsonResponse;

class CartController extends ApiBaseController
{
    /**
     * @param  CartRepositoryInterface $cartRepository
     */
    public function __construct(protected CartRepositoryInterface $cartRepository)
    {
    }
    /**
     * Get Cart
     * @return Cart
     */
    public function getCart(): JsonResponse
    {
        $data = $this->cartRepository->getCart();
        return $this->sendSuccess(CartResource::generate($data));
    }
    /**
     * Delete Cart record .
     * @param int|string $cartId
     * @return JsonResponse
     */

    public function deleteCart(int|string $cartId)
    {
        $data = $this->cartRepository->deleteCart($cartId);
        return $this->sendSuccess(CartResource::generate($data));
    }
    /**
     * Store Cart On Product .
     * @param CartRequest $request
     * @param int|string $productId
     * @return JsonResponse
     */
    public function storeProductToCart(CartRequest $request): JsonResponse
    {
        $data = $this->cartRepository->storeProductToCart($request->all());
        return $this->sendSuccess(CartResource::generate($data));
    }

    /**
     * update Cart .
     * @param CartRequest $request
     * @param int|string $productId
     * @return JsonResponse
     */
    public function updateCart(CartRequest $request): JsonResponse
    {
        $data = $this->cartRepository->updateCart($request->all());
        return $this->sendSuccess(CartResource::generate($data));
    }

}