<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\ProductsRequest;
use App\Http\Resources\Website\AutocompleteResource;
use App\Http\Resources\Website\SearchResource;
use App\Models\Brand;
use App\Models\Variance;
use App\Repositories\Brand\BrandRepositoryInterface;
use Illuminate\Support\Carbon;
use MeiliSearch\Endpoints\Indexes;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Product;
use App\Repositories\Search\SearchRepositoryInterface;
use App\Repositories\Category\CategoryRepositoryInterface;

class SearchController extends ApiBaseController
{

    public function __construct(protected SearchRepositoryInterface $searchRepository)
    {
    }
    public function index(ProductsRequest $request): JsonResponse
    {

        if ($request->has('slug') && $request->get('slug') != "") {
            // use getCategoryBySlug from category repository
            $categoryRepository = app(CategoryRepositoryInterface::class);

            if ($request->has(key: 'slugs') && count($request->slugs) > 1) {

                $categories = $categoryRepository->query()->whereIn('slug', $request->slugs)->get();

                if ($categories->count() != count($request->slugs)) {

                    $categoryModel = $categoryRepository->query()->whereJsonContains('oldSlug->' . current_locale(), $request->slug)->first();
                    if ($categoryModel) {
                        // If found by oldSlug, construct the redirect URL
                        $redirectUrl = $categoryModel->slug;
                        $parent = $categoryModel->parent;
                        while ($parent) {
                            $redirectUrl = $parent->slug . '/' . $redirectUrl;
                            $parent = $parent?->parent;
                        }
                        throw new \App\Exceptions\ModelNotFoundException('Category found by oldSlug, redirecting...', 301, $redirectUrl);
                    }

                    throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category not found');
                }

                $categoryMap = $categories->keyBy('categoryId');

                $categoryMap->each(function ($category) use ($categoryMap) {
                    if ($category->parentId) {
                        if (!$categoryMap->has($category->parentId)) {
                            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category not found');
                        }
                    }
                });

                $category = $categoryRepository->query()->where('slug', '=', $request->slug)->first();

                if (!$category) {
                    $categoryModel = $categoryRepository->query()->whereJsonContains('oldSlug->' . current_locale(), $request->slug)->first();
                    if ($categoryModel) {
                        // If found by oldSlug, construct the redirect URL
                        $redirectUrl = $categoryModel->slug;
                        $parent = $categoryModel->parent;
                        while ($parent) {
                            $redirectUrl = $parent->slug . '/' . $redirectUrl;
                            $parent = $parent?->parent;
                        }
                        throw new \App\Exceptions\ModelNotFoundException('Category found by oldSlug, redirecting...', 301, $redirectUrl);
                    } else {
                        // If not found by either slug or oldSlug, throw the original exception
                        throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category not found');
                    }
                }



            } else {

                $category = $categoryRepository->query()->where('slug', '=', $request->slug)->whereNull('parentId')->first();
                if (!$category) {
                    $categoryModel = $categoryRepository->query()->whereJsonContains('oldSlug->' . current_locale(), $request->slug)->first();
                    if ($categoryModel) {
                        // If found by oldSlug, construct the redirect URL
                        $redirectUrl = $categoryModel->slug;
                        $parent = $categoryModel->parent;
                        while ($parent) {
                            $redirectUrl = $parent->slug . '/' . $redirectUrl;
                            $parent = $parent?->parent;
                        }
                        throw new \App\Exceptions\ModelNotFoundException('Category found by oldSlug, redirecting...', 301, $redirectUrl);
                    } else {
                        // If not found by either slug or oldSlug, throw the original exception
                        throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Category not found');
                    }
                }
            }


            if ($category) {
                $categoryId = $category->categoryId;
                // dd($category);
                $filterId = $category->filtersGroupsId;
            }
        } else {
            $categoryId = $request->categoryId;
            $filterId = 2; //TODO change 2 to 1 when search filter is done in seeder
        }
        $data = $this->searchRepository->getProducts(
            categoryId: $categoryId ?? '',
            page: $request->page ?? 1,
            limit: $request->perPage ?? 26,
            orderBy: $request->orderBy ?? 'createdAt,desc',
            query: $request->q ?? '',
            filterId: isset($filterId) ? $filterId : null,
        );
        return $this->sendSuccess(SearchResource::generate($data, Product::class));
    }


    public function searchOffers(Request $request)
    {

        $filterId = 3;
        $data = $this->searchRepository->getProductsOffers(
            page: $request->page ?? 1,
            limit: $request->perPage ?? 26,
            orderBy: $request->orderBy ?? 'score,DESC',
            query: $request->q ?? '',
            filterId: $filterId,
        );

        return $this->sendSuccess(SearchResource::generate($data, Product::class));
    }




    public function searchByBrand($slug, Request $request)
    {

        $filterId = 2;

        $brandRepository = app(BrandRepositoryInterface::class);
        $brand = $brandRepository->getBySlug(slug: $slug);
        $data = $this->searchRepository->getProductsByBrand(
            page: $request->page ?? 1,
            limit: $request->perPage ?? 26,
            orderBy: $request->orderBy ?? 'score,desc',
            query: $request->q ?? '',
            brandId: $brand->brandId ?? '',
            filterId: $filterId,
        );

        return $this->sendSuccess(SearchResource::generate($data, Product::class));

    }

    public function autocomplete(Request $request)
    {
        $categoryId = $request->has('category') ? $request->get('category') : '';

        $q = $request->has('q') ? trim($request->get('q')) : '';

        if ($q) {
            $products = $this->searchRepository->getAutocompleteProducts(
                categoryId: $categoryId,
                query: $q,
            );
            return $this->sendSuccess(AutocompleteResource::generate($products), Variance::class);
        } else {
            return $this->sendError('No search query provided');
        }
    }




    public function mostPopular()
    {

        $data = $this->searchRepository->mostPopular();
        return $this->sendSuccess(SearchResource::generate($data), Product::class);
    }
    public function newArrival()
    {
        $data = $this->searchRepository->newArrival();
        return $this->sendSuccess(SearchResource::generate($data), Product::class);
    }


}