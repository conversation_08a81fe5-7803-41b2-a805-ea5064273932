<?php

namespace App\Http\Controllers\Website;

use App\Http\Requests\Website\Order\AddressRequest;
use App\Http\Requests\Website\Order\BuyNowRequest;
use App\Http\Requests\Website\Order\OrderPaymentFormRequest;
use App\Http\Requests\Website\Order\OrderPayRequest;
use App\Http\Resources\Website\PaymentMethodResource;
use App\Http\Resources\Website\ShippingResource;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Website\PayResource;
use App\Http\Resources\Website\OrderResource;
use App\Http\Resources\Website\VerifyResource;
use App\Http\Requests\Website\Order\PayRequest;
use App\Http\Requests\Website\Order\OrderRequest;
use App\Http\Requests\Website\Order\VerifyRequest;
use App\Repositories\Order\OrderRepositoryInterface;
use App\Http\Requests\Website\Order\OrderPaymentRequest;
use App\Http\Requests\Website\Order\OrderCompleteRequest;
use App\Http\Requests\Website\Order\OrderShippingRequest;
use App\Http\Requests\Website\Order\OrderPromotionRequest;
use App\Http\Requests\Website\OrderRatingRequest;
use App\Http\Resources\Admin\OrderDetailsResource;
use App\Http\Resources\Website\OrderRateResource;
use Illuminate\Http\Request;

class OrderController extends ApiBaseController
{ /**
  * @param  OrderRepositoryInterface $orderRepository
  */
    public function __construct(protected OrderRepositoryInterface $orderRepository)
    {
    }
    /**
     * Get User Orders.
     * @return JsonResponse
     */
    public function index()
    {
        $data = $this->orderRepository->getUserOrders();
        return $this->sendSuccess(OrderResource::generate($data));
    }

    /**
     * Show Order by order.
     *
     * @param int|string $order
     * @return JsonResponse
     */
    public function show(int|string $order): mixed
    {
        $data = $this->orderRepository->findByIdUserOrder($order);
        return $this->sendSuccess(OrderDetailsResource::generate($data));

    }


    /**
     * Show Order by order.
     *
     * @param int|string $order
     * @return JsonResponse
     */
    public function orderDetails(int|string $order): mixed
    {
        $data = $this->orderRepository->findOrderById($order);
        return $this->sendSuccess(OrderResource::generate($data));

    }




    public function store(OrderRequest $request)
    {
        $data = $this->orderRepository->storeOrderFormCart();
        if (isset($data["status"]) && $data["status"] === false) {
            return $this->sendError($data, $data['message'], 400);
        }
        return $this->sendSuccess(OrderResource::generate($data));
    }


    public function update(OrderRequest $request, $orderId)
    {
        $data = $this->orderRepository->updateOrder($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }

    public function verifyOrderPaymentHyperpay(Request $request, $orderId)
    {
        $data = $this->orderRepository->verifyOrderPaymentHyperpay($orderId, $request->all());
        return $this->sendSuccess($data);

    }



    public function setShippingCarrier(OrderShippingRequest $request, $orderId)
    {

        $data = $this->orderRepository->setShippingCarrier($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    public function setPromotion(OrderPromotionRequest $request, $orderId)
    {
        $data = $this->orderRepository->setPromotion($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    public function setPaymentMethod(OrderPaymentRequest $request, $orderId)
    {
        $data = $this->orderRepository->setPaymentMethod($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    public function setPaymentParams(OrderPaymentFormRequest $request, $orderId)
    {
        $data = $this->orderRepository->setPaymentParams($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    public function orderComplete(OrderCompleteRequest $request, $orderId)
    {
        $data = $this->orderRepository->orderComplete($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    // public function renderPay(PayRequest $request, $orderId)
    // {
    //     $data = $this->orderRepository->renderPay($orderId, $request->all());
    //     return $this->sendSuccess(PayResource::generate($data));
    // }




    public function verifyIsPaid($orderId)
    {
        $data = $this->orderRepository->verifyIsPaid($orderId);
        return $this->sendSuccess($data);
    }
    public function verify(VerifyRequest $request, $orderId)
    {
        $data = $this->orderRepository->verify($orderId, $request->all());
        return $this->sendSuccess(VerifyResource::generate($data));
    }

    public function buyNow(BuyNowRequest $request)
    {

        $data = $this->orderRepository->buyNow($request->all());
        if (isset($data["status"]) && $data["status"] === false) {
            return $this->sendError($data, $data['message'], 400);
        }
        return $this->sendSuccess(OrderResource::generate($data));
    }



    public function getShippings($id)
    {
        $data = $this->orderRepository->getShippings($id);
        return $this->sendSuccess(ShippingResource::generate($data));
    }


    public function getShippingCarriers($id)
    {
        $data = $this->orderRepository->getShippingCarriers($id);
        return $this->sendSuccess(ShippingResource::generate($data));
    }


    public function getAllowPaymentMethods($id)
    {
        $data = $this->orderRepository->getAllowPaymentMethods($id);
        return $this->sendSuccess(PaymentMethodResource::generate($data));
    }



    public function canceled($id)
    {
        $data = $this->orderRepository->canceled($id);
        return $this->sendSuccess(OrderResource::generate($data));
    }

    public function cancel($id)
    {
        $data = $this->orderRepository->cancel($id);
        return $this->sendSuccess(OrderResource::generate($data));
    }

    public function finalize(OrderPayRequest $request, $orderId)
    {
        $data = $this->orderRepository->finalize($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }



    /**
     * Update Address record .
     *
     * @param AddressRequest $request
     * @param int|string  $addressId
     * @return JsonResponse
     */
    public function address(AddressRequest $request, int|string $orderId): JsonResponse
    {

        $data = $this->orderRepository->address($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }



    /**
     * Add Note.
     *
     * @param OrderRequest $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function addNote(Request $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->addNote($orderId, $request->all());
        return $this->sendSuccess(OrderResource::generate($data));
    }


    /**
     * Delete Order record .
     * @param int|string $orderId
     * @return JsonResponse
     */

    public function destroy(int|string $orderId)
    {
        $this->orderRepository->deleteOrder($orderId);
        return $this->sendSuccess();
    }


    /**
     * Store Rating On Product .
     * @param RateOrderResource $request
     * @param int|string $orderId
     * @return JsonResponse
     */
    public function storeRatingOnOrder(OrderRatingRequest $request, int|string $orderId): JsonResponse
    {
        $data = $this->orderRepository->storeRatingOnOrder($orderId, $request->all());
        return $this->sendSuccess(OrderRateResource::generate($data));
    }


    /**
     * Store Rating On Product .
     * @param RateOrderResource $request
     * @param int|string $orderId
     * @param int|string $orderRatingId
     * @return JsonResponse
     */
    public function updateRatingOnOrder(OrderRatingRequest $request, int|string $orderId, int|string $orderRatingId): JsonResponse
    {
        $data = $this->orderRepository->updateRatingOnOrder($orderId, $orderRatingId, $request->all());
        return $this->sendSuccess(OrderRateResource::generate($data));

    }

}