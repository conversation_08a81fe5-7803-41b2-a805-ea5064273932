<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\AddressRequest;
use App\Http\Requests\Website\DefaultAddressRequest;
use App\Http\Resources\Website\AddressResource;
use App\Repositories\Address\AddressRepositoryInterface;
use Illuminate\Http\JsonResponse;

class AddressController extends ApiBaseController
{
    /**
     * @param  AddressRepositoryInterface $addressRepository
     */
    public function __construct(protected AddressRepositoryInterface $addressRepository)
    {
    }
    /**
     * Get all Addresses.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->addressRepository->getUserAddresses();
        return $this->sendSuccess(AddressResource::generate($data));
    }
    /**
     * Store a new Address record .
     * @param AddressRequest $request
     * @return JsonResponse
     */
    public function store(AddressRequest $request)
    {
        $data = $this->addressRepository->storeAddress($request->validated());
        return $this->sendSuccess(AddressResource::generate($data));
    }

    /**
     * Show Address by ID .
     * @param int|string $addressId
     * @return JsonResponse
     */
    public function show(int|string $addressId): JsonResponse
    {
        $data = $this->addressRepository->findByIdAddress($addressId);
        return $this->sendSuccess(AddressResource::generate($data));
    }

    /**
     * Update Address record .
     *
     * @param AddressRequest $request
     * @param int|string  $addressId
     * @return JsonResponse
     */
    public function update(AddressRequest $request, int|string $addressId): JsonResponse
    {
        $data = $this->addressRepository->updateAddress($addressId, $request->validated());
        return $this->sendSuccess(AddressResource::generate($data));
    }

    public function setDefaultAddress(int|string $addressId): JsonResponse
    {
        $data = $this->addressRepository->setDefaultAddress($addressId);
        return $this->sendSuccess(AddressResource::generate($data));

    }

    /**
     * Delete Address record .
     * @param int|string $addressId
     * @return JsonResponse
     */

    public function destroy(int|string $addressId)
    {
        $this->addressRepository->deleteAddress($addressId);
        return $this->sendSuccess();
    }
}