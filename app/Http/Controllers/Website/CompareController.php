<?php

namespace App\Http\Controllers\Website;

use App\Enums\ProductTypeEnum;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Website\CompareResource;
use App\Repositories\Variance\VarianceRepositoryInterface;
use Illuminate\Http\Request;

class CompareController extends ApiBaseController
{
    public function __construct(protected VarianceRepositoryInterface $varianceRepositoryInterface)
    {
    }

    public function index(Request $request)
    {
        $variances = $this->varianceRepositoryInterface->getVariancesIds($request->get('varianceIds'));


        $data = [];

        $attributeGroups[0] = [
            'attribute' => __('global.title.brands'),
            'attributeId' => 0,
            'icon' => 'brand',
            'attributeOptions' => []
        ];

        // First, collect all variances data
        foreach ($variances as $variance) {
            $data["variances"][] = [
                'varianceId' => $variance->varianceId,
                'name' => $variance->product->name,
                'varianceName' => $variance->name,
                "gallery" => $variance->product->type == ProductTypeEnum::alternative->value ? $variance->gallery : $variance->product->covers,
                "stock" => $variance->activeStock,
                "productId" => $variance->productId,
                'slug' => $variance->product->slug,
                "varianceSlug" => $variance->slug
            ];


            $attributeGroups[0]['attributeOptions'][] = [
                'varianceId' => $variance->varianceId,
                'attributeOptionId' => 0,
                'name' => $variance->brand?->name ?? '',
                'value' => null,
                'attributeId' => 0
            ];

            // Collect all attributes and their values for each variance
            foreach ($variance->attributesWithValue as $attributeValue) {
                $attributeId = $attributeValue->attributeId;

                if (!isset($attributeGroups[$attributeId])) {
                    $attributeGroups[$attributeId] = [
                        'attribute' => $attributeValue->name ?? $attributeValue->key ?? 'Unknown',
                        'attributeId' => $attributeId,
                        'icon' => $attributeValue->icon,
                        'attributeOptions' => []
                    ];
                }

                $attributeGroups[$attributeId]['attributeOptions'][] = [
                    'varianceId' => $variance->varianceId,
                    'attributeOptionId' => $attributeValue->attributeOptionId,
                    'name' => $attributeValue->option_name,
                    'value' => $attributeValue->value,
                    'attributeId' => $attributeId
                ];
            }

            // Collect all attributes and their values for each variance
            foreach ($variance->product->attributesWithValue as $attributeValue) {
                $attributeId = $attributeValue->attributeId;

                if (!isset($attributeGroups[$attributeId])) {
                    $attributeGroups[$attributeId] = [
                        'attribute' => $attributeValue->name ?? $attributeValue->key ?? 'Unknown',
                        'attributeId' => $attributeId,
                        'icon' => $attributeValue->icon,
                        'attributeOptions' => []
                    ];
                }

                $attributeGroups[$attributeId]['attributeOptions'][] = [
                    'varianceId' => $variance->varianceId,
                    'attributeOptionId' => $attributeValue->attributeOptionId,
                    'name' => $attributeValue->option_name,
                    'value' => $attributeValue->value,
                    'attributeId' => $attributeId
                ];
            }





        }

        // Convert the associative array to indexed array
        $data["attributes"] = array_values($attributeGroups);

        return $this->sendSuccess(CompareResource::generate($data));
    }
}