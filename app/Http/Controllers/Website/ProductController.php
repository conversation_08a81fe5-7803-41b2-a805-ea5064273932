<?php

namespace App\Http\Controllers\Website;

use App\Http\Requests\Website\ProductVarianceDetailsRequest;
use App\Http\Resources\Website\RateResource;
use App\Http\Resources\Website\RatingsResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Website\RatingRequest;
use App\Http\Resources\Website\RatingResource;
use App\Http\Resources\Website\ProductResource;
use App\Http\Resources\Website\ProductDetailsResource;
use App\Repositories\Product\ProductRepositoryInterface;

class ProductController extends ApiBaseController
{
    /**
     * @param  ProductRepositoryInterface $productRepository
     */
    public function __construct(protected ProductRepositoryInterface $productRepository)
    {
    }

    /**
     * Show Product With Variance .
     *
     * @param int|string $product
     * @return JsonResponse
     */
    public function getProductWithVariance(int|string $product, int|string $variance = null): JsonResponse
    {
        $data = $this->productRepository->getProductWithVariance($product, $variance);
        return $this->sendSuccess(($data));
    }

    /**
     * Show Product With Variance .
     *
     * @param int|string $product
     * @param int|string $variance
     * @return JsonResponse
     */
    public function getProductDetails(int|string $product, int|string $variance = null): JsonResponse
    {
        $data = $this->productRepository->getProductDetails($product, $variance);
        return $this->sendSuccess(ProductDetailsResource::generate($data));
    }



    /**
     * Show Product With Variance .
     *
     * @param int|string $oldSlug
     * @return JsonResponse
     */
    public function getProductDetailsByOldSlug(int|string $oldSlug): JsonResponse
    {
        $data = $this->productRepository->getProductDetailsByOldSlug($oldSlug);
        return $this->sendSuccess(ProductDetailsResource::generate($data));
    }


    /**
     * Show Product With Variance .
     *
     * @param int|string $oldSlug
     * @return JsonResponse
     */
    public function getProductDetailsByOldId(int|string $oldSlug): JsonResponse
    {
        $data = $this->productRepository->getProductDetailsByOldId($oldSlug);
        return $this->sendSuccess(ProductDetailsResource::generate($data));
    }



    /**
     * Show Product With Variance .
     * @param array $data
     * @return JsonResponse
     */
    public function getProductVarianceDetails(ProductVarianceDetailsRequest $request): JsonResponse
    {
        $data = $this->productRepository->getProductVarianceDetails($request->all());
        return $this->sendSuccess(ProductDetailsResource::generate($data));

    }


    /**
     * Store Rating On Product .
     * @param RatingRequest $request
     * @param int|string $productId
     * @return JsonResponse
     */
    public function storeRatingOnProduct(RatingRequest $request, int|string $product): JsonResponse
    {
        $data = $this->productRepository->storeRatingOnProduct($product, $request->validated());
        return $this->sendSuccess(RateResource::generate($data));
    }

    /**
     * get Ratings Product .
     * @param int|string $productId
     * @return JsonResponse
     */
    public function getRatingsProduct(int|string $product): JsonResponse
    {
        $data['ratings'] = $this->productRepository->getRatings($product);
        return $this->sendSuccess(RatingResource::generate($data));
    }



    /**
     * Get getRecentlyAddedProducts
     * @return JsonResponse
     */
    public function getRecentlyAddedProducts(): JsonResponse
    {
        $data = $this->productRepository->getRecentlyAddedProducts();
        return $this->sendSuccess(ProductResource::generate($data));
    }

    /**
     * Get getProductsByCategory
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $data = $this->productRepository->getProductsByCategory($request->category, $request->subcategory);
        return $this->sendSuccess(ProductResource::generate($data));
    }


    /**
     * Get getProductsByCategory
     * @return JsonResponse
     */
    public function mostPopular()
    {

        $data = $this->productRepository->mostPopular();
        return $this->sendSuccess(ProductResource::generate($data));
    }
    public function newArrival()
    {
        $data = $this->productRepository->newArrival();
        return $this->sendSuccess(ProductResource::generate($data));
    }



    public function suggestedProduct($id)
    {
        $data = $this->productRepository->suggestedProduct($id);
        return $this->sendSuccess(ProductResource::generate($data));
    }




    /**
     * Store Rating On Product .
     * @param RatingRequest $request
     * @param int|string $productId
     * @param int|string $ratingId
     * @return JsonResponse
     */
    public function updateRatingOnProduct(RatingRequest $request, int|string $product, int|string $ratingId): JsonResponse
    {
        $data = $this->productRepository->updateRatingOnProduct($product, $ratingId, $request->all());
        return $this->sendSuccess(RateResource::generate($data));
    }
}