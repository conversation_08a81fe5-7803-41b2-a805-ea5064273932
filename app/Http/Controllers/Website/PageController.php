<?php

namespace App\Http\Controllers\Website;

use App\Repositories\Page\PageRepositoryInterface;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Admin\PageResource;
use Illuminate\Http\JsonResponse;


class PageController extends ApiBaseController
{
    /**
     * @param  PageRepositoryInterface $pageRepository
     */
    public function __construct(protected PageRepositoryInterface $pageRepository)
    {
    }
        /**
     * Get all Pages .
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $data = $this->pageRepository->getPages();
        return $this->sendSuccess(PageResource::generate($data));
    }

    /**
     * Show Page by slug .
     *
     * @param int|string $page
     * @return JsonResponse
     */
    public function show($page): JsonResponse
    {
        $data = $this->pageRepository->findBySlugPage($page);
        return $this->sendSuccess(PageResource::generate($data));
    }

}
