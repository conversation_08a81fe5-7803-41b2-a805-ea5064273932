<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\Website\ContactUsResource;
use App\Http\Requests\Website\ContactUsRequest;
use App\Repositories\ContactUs\ContactUsRepositoryInterface;
use Illuminate\Http\JsonResponse;


class ContactUsController extends ApiBaseController
{
    /**
     * @param  ContactUsRepositoryInterface $categoryRepository
     */
    public function __construct(protected ContactUsRepositoryInterface $contactUsRepository)
    {
    }
    /**
     * Store a new ContactUs record .
     * @param ContactUsRequest $request
     * @return JsonResponse
     */
    public function store(ContactUsRequest $request)
    {
        $data = $this->contactUsRepository->storeMessage($request->validated());
        return $this->sendSuccess(ContactUsResource::generate($data));
    }

   
}
