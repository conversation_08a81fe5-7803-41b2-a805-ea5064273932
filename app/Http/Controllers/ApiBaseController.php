<?php

namespace App\Http\Controllers;

use App\Traits\ApiResponser;
use Illuminate\Support\Facades\Route;
use ReflectionClass;
use ReflectionMethod;

class ApiBaseController extends Controller
{
    use ApiResponser;

    protected $publicFunctions = [];

    protected $skipFunctionNames = [
        '__construct',
        '__call',
        'middleware',
        'getMiddleware',
        'callAction',
        'authorize',
        'authorizeForUser',
        'authorizeResource',
        'dispatchNow',
        'dispatchSync',
        'validateWith',
        'validate',
        'validateWithBag',
        'sendSuccess',
        'sendError'
    ];

    protected function getFunctions()
    {

        $className = static::class;
        $classReflection = new ReflectionClass($className);
        $adminRoute = is_admin_route($className);

        foreach ($classReflection->getMethods(ReflectionMethod::IS_PUBLIC) as $method) {
            // Exclude magic methods and constructor
            $functionName = $method->getName();
            $routeName = $adminRoute ? 'admin.' . $this->getRouteName($functionName) : $this->getRouteName($functionName);
            if (!$method->isConstructor() && !$method->isInternal() && !in_array($functionName, $this->skipFunctionNames)) {
                $this->publicFunctions[] = ['functionName' => $functionName, 'routeName' => $routeName];
            }
        }

    }


    protected function getRouteName($methodName)
    {
        $controllerAction = static::class . '@' . $methodName;

        // Get the route associated with the controller action
        $route = collect(Route::getRoutes())->first(function ($route) use ($controllerAction) {
            return $route->getActionName() === $controllerAction;
        });

        // Return the route name if found, or null if not found
        return $route ? $route->getName() : null;
    }

    protected function applyPermissions()
    {
        $this->getFunctions();

        foreach ($this->publicFunctions as $function) {
            $this->middleware('permission:' . $function['routeName'])->only([$function['functionName']]);
        }

    }


}
