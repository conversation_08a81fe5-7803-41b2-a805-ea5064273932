<?php

namespace App\Http\Controllers\Lookups;

use App\Exceptions\GeneralException;
use App\Helper\CacheHelper;
use App\Http\Controllers\ApiBaseController;
use App\Http\Resources\OptionsResource;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Lookable;
use Illuminate\Http\JsonResponse;

use Illuminate\Support\Facades\Cache;
use const _PHPStan_582a9cb8b\__;

class DynamicLookupController extends ApiBaseController
{



    public function __construct(
        protected CacheHelper $cacheHelper,
        public int $minutes = 100
    ) {

    }
    /**
     * Apply dynamic lookup functionality .
     *
     * @param string $resource
     * @return JsonResponse
     * @throws GeneralException
     */
    public function __invoke(string $resource): JsonResponse
    {
        $repository = get_repo_interface($resource);

        $keyCache = request()->path() . "/" . request()->getQueryString();

        $modelClass = $repository->modelClass;

        // abort_if_not_has_trait($modelClass, [Lookable::class, HasFilters::class]);

        $model = resolve($modelClass);

        $modelOptions = $model->getLookupResourceConfig();


        if (has_trait($model, HasFilters::class)) {

            if ($count = $model->lookupMaxRecords()) {
                $data = $this->cacheHelper->remember(
                    key: $keyCache,
                    callback: function () use ($repository, $count) {
                        return $repository->getAllFilteredTake($count);
                        ;
                    },
                    tags: [],
                    ttl: $this->minutes,
                );

            } else {
                $data = $this->cacheHelper->remember(
                    key: $keyCache,
                    callback: function () use ($repository) {
                        return $repository->getAllFiltered();
                    },
                    tags: [],
                    ttl: $this->minutes,
                );

            }
        } else {


            $data = $this->cacheHelper->remember(
                key: $keyCache,
                callback: function () use ($repository) {
                    return $repository->getAll();
                },
                tags: [],
                ttl: $this->minutes,
            );




        }

        return $this->sendSuccess(
            new OptionsResource(
                resource: $data,
                textColumn: $modelOptions['text_column'] ?? 'name',
                valueColumn: $modelOptions['value_column'] ?? 'id',
                meta: $modelOptions['meta'] ?? [],
                textSeparator: $modelOptions['text_separator'] ?? ''
            )
        );
    }
}