<?php

namespace App\Http\Controllers\Lookups;

use App\Http\Controllers\ApiBaseController;
use App\Repositories\TranslationKey\TranslationKeyRepositoryInterface;
use Illuminate\Http\JsonResponse;
class TranslationLookupController extends ApiBaseController
{
    /**
     * @param TranslationKeyRepositoryInterface $translationRepository
     */
    public function __construct(
        protected TranslationKeyRepositoryInterface $translationRepository,
    ) {
    }

    /**
     * Get all static translations based key .
     *
     * @group Static Translations
     * @param string $languageAbbreviation
     * @return JsonResponse
     */
    public function __invoke(string $languageAbbreviation): JsonResponse
    {

        $fallbackAbbreviation = null;
        $groups = [];
        $tags = [];

        if (request()->has('fallback') && request()->filled('fallback')) {
            $fallbackAbbreviation = request('fallback');
        }

        if (request()->has('groups') && request()->filled('groups')) {
            $groups = request()->string('groups')->explode(',')->toArray();
        }

        if (request()->has('tags') && request()->filled('tags')) {
            $groups = request()->string('tags')->explode(',')->toArray();
        }

        $data = $this->translationRepository->getAllBasedKeys(
            languageAbbreviation: $languageAbbreviation,
            fallbackAbbreviation: $fallbackAbbreviation,
            groups: $groups,
            tags: $tags,
        );

        return $this->sendSuccess($data);
    }
}