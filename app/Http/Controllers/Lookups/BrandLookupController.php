<?php

namespace App\Http\Controllers\Lookups;

use App\Http\Controllers\ApiBaseController;


use App\Traits\Models\HasFilters;
use App\Helper\CacheHelper;
use App\Http\Resources\Lookup\BrandResource;
use App\Http\Resources\OptionsResource;
use App\Models\Brand;
use App\Repositories\Brand\BrandRepositoryInterface;
use Illuminate\Http\JsonResponse;

class BrandLookupController extends ApiBaseController
{
    /**
     * @param  BrandRepositoryInterface $brandRepository
     * @param  Brand $model
     * @param  CacheHelper $cacheHelper
     */
    public function __construct(
        protected BrandRepositoryInterface $brandRepository,
        protected Brand $model,
        protected CacheHelper $cacheHelper,
        public int $minutes = 100
    ) {
    }
    /**
     * Get all Addresses.
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {


        $keyCache = request()->path() . "/" . request()->getQueryString();


        $modelOptions = $this->model->getLookupResourceConfig();

        if (has_trait($this->model, HasFilters::class)) {

            if ($count = $this->model->lookupMaxRecords()) {

                $data = $this->cacheHelper->remember(
                    key: $keyCache,
                    callback: function () use ($count) {
                        return $this->brandRepository->query()->with(['media'])->getAllFilteredTake($count);
                    },
                    tags: [],
                    ttl: $this->minutes,
                );

            } else {

                $data = $this->cacheHelper->remember(
                    key: $keyCache,
                    callback: function () use ($count) {
                        return $this->brandRepository->query()
                            ->with(['media'])
                            ->filters()
                            ->take($count)
                            ->get();
                    },
                    tags: [],
                    ttl: $this->minutes,
                );

            }


        } else {

            $data = $this->cacheHelper->remember(
                key: $keyCache,
                callback: function () {
                    return $this->brandRepository->query()
                        ->with(['media'])
                        ->filters()
                        ->get();
                },
                tags: [],
                ttl: $this->minutes,
            );




        }

        return $this->sendSuccess(BrandResource::generate($data));
    }



}