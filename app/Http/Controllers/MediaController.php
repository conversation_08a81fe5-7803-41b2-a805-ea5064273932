<?php

namespace App\Http\Controllers;

use App\Helper\FileHelper;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Media\MediaRequest;
use App\Http\Resources\Media\MediaResource;
use App\Models\Media;
use Database\Variants\Brand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class MediaController extends ApiController
{



    public function upload(MediaRequest $request)
    {
        $data = $request->except('file');
        $data['type'] = FileHelper::getTypeFile($request->file);
        $data['src'] = Storage::disk('s3')->put('temp', $request->file);
        $data['preview'] = Storage::disk('s3')->url($data['src']);
        $media = Media::create($data);
        return new MediaResource($media);
    }
}
