<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Notification;
use Illuminate\Http\Request;
use App\Enums\SMSProvidersEnum;
use App\Enums\PaymentMethodEnum;

use App\Mail\NotificationOrderMail;
use Illuminate\Support\Facades\Mail;
use App\Events\OrderNotificationEvent;
use App\SMSProviders\SMSProvidersFactory;
use App\Listeners\OrderNotificationListener;
use App\Models\Order;
use App\PaymentMethod\PaymentGatewayFactory;
use App\Repositories\Order\OrderRepositoryInterface;

class TestControllerApi extends ApiBaseController
{



    public function index(Request $request)
    {


        $data = [
            'title' => [
                "ar" => "asa",
                "en" => "sasa"
            ],
            "body" => [
                "ar" => "sdsd",
                "en" => "sas"
            ],
            "userId" => 1,
            'action' => null,
            'phone' => "+************",
            "email" => "<EMAIL>"
        ];


        event(new OrderNotificationEvent($data));

        return $data;

        $gatewayType = PaymentMethodEnum::arabbank->value;
        $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);
        $amount = 10;
        $paymentGateway->setAmount($amount);
        $transactionId = $paymentGateway->generateTransactionId(Order::class);
        return $transactionId;
    }


    public function setParamFillForm(Request $request)
    {
        $order = resolve(OrderRepositoryInterface::class)->findByIdOrder(1);
        $gatewayType = PaymentMethodEnum::arabbank->value;
        $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);
        $amount = 10;
        $paymentGateway->setAmount($amount);
        $paymentGateway->setOrderDetails($order);
        $paymentGateway->setParamFillForm();

        return $paymentGateway->sign();
        return $order;
    }

    public function render(Request $request)
    {
        $gatewayType = PaymentMethodEnum::arabbank->value;
        $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);
        $amount = 10;
        $paymentGateway->setAmount($amount);
    }



    public function setForm()
    {
        $gatewayType = PaymentMethodEnum::arabbank->value;
        $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);
        $amount = 10;
        $paymentGateway->setAmount($amount);

    }
}