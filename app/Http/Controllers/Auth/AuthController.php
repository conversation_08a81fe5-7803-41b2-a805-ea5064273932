<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiBaseController;
use App\Repositories\Auth\AuthRepositoryInterface;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\ForgotPasswordEmailRequest;
use App\Http\Requests\Auth\ForgotPasswordPhoneRequest;
use App\Http\Requests\Auth\OtpVerificationCodeRequest;
use App\Http\Requests\Auth\ResetPasswordTokenRequest;
use App\Http\Resources\Auth\LoginResource;
use App\Http\Resources\Auth\MobileCodeResource;
use App\Http\Resources\Auth\PasswordResetResource;
use App\Http\Resources\Auth\RegisterResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AuthController extends ApiBaseController
{
    /**
     * @param  AuthRepositoryInterface $authRepository
     */

    public function __construct(
        protected AuthRepositoryInterface $authRepository
    ) {
    }


    public function login(LoginRequest $request): JsonResponse
    {
        $data = $this->authRepository->login($request->all());
        if ($data['isUserLoggedIn']) {
            return $this->sendSuccess(LoginResource::generate($data));
        }
        return $this->sendError([], $data['message']);
    }


    public function register(RegisterRequest $request): JsonResponse
    {
        $data = $this->authRepository->register($request->all());
        return $this->sendSuccess(RegisterResource::generate($data));
    }

    public function logout()
    {
        $this->authRepository->logout();
        return $this->sendSuccess([
            'success' => true,
            'message' => 'Logged Out Successfully'
        ], );
    }


    public function forgotPasswordPhone(ForgotPasswordPhoneRequest $request): JsonResponse
    {

        $data = $this->authRepository->forgotPasswordPhone($request->all());
        return $this->sendSuccess(MobileCodeResource::generate($data));


    }


    public function forgotPasswordEmail(ForgotPasswordEmailRequest $request): JsonResponse
    {
        $data = $this->authRepository->forgotPasswordEmail($request->validated());
        return $this->sendSuccess(PasswordResetResource::generate($data));

    }


    public function verifyToken($token)
    {

        $data = $this->authRepository->verifyToken($token);
        return $this->sendSuccess((PasswordResetResource::generate($data)));

    }





    public function sendOTPVerifyCode(Request $request)
    {
        $data = $this->authRepository->sendOTPVerifyCode($request->all());
        return $this->sendSuccess(MobileCodeResource::generate($data));
    }

    public function verifyOTPCode(OtpVerificationCodeRequest $request)
    {
        $data = $this->authRepository->verifyOTPCode($request->all());
        return $this->sendSuccess(MobileCodeResource::generate($data));
    }


    public function verifyCode(OtpVerificationCodeRequest $request)
    {
        $data = $this->authRepository->verifyCode($request->all());
        return $this->sendSuccess(MobileCodeResource::generate($data));
    }

    public function resetPasswordCode(ResetPasswordTokenRequest $request)
    {
        $data = $this->authRepository->resetPasswordCode($request->all());

        return $this->sendSuccess([
            'message' => "Your password has been reset",
            'data' => $data
        ], );
    }


    public function resetPasswordToken(ResetPasswordTokenRequest $request, $token)
    {
        $data = $this->authRepository->resetPasswordToken($token, $request->all());

        return $this->sendSuccess([
            'message' => "Your password has been reset",
            'data' => $data
        ], );
    }

}