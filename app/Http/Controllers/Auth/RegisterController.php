<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\ApiBaseController;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Resources\Auth\RegisterResource;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class RegisterController extends ApiBaseController
{



    public function register(RegisterRequest $request)
    {
        $request = $request->all();
        $request["password"] = Hash::make($request['password']);
        $user = User::create($request);
        $user['token'] = $user->createToken(TOKEN_USER)->accessToken;

        return $this->sendSuccess(RegisterResource::generate($user));
    }
}
