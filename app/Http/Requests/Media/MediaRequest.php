<?php

namespace App\Http\Requests\Media;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class MediaRequest extends \App\Http\Requests\Api\FormRequest
{

    protected $allowFileExtention;

    /**
     * set allow extention  for files upload
     * @return void 
     */
    public function __construct()
    {
        parent::__construct();
        $this->allowFileExtention = env("Allow_File_Extension");
    }

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return  [
            'file' => "required|file|mimes:$this->allowFileExtention",
            //"alt" => "required",
            'startDate' =>
            [
                //'required',
                'date_format:Y-m-d', // format without hours, minutes and seconds.
                'before_or_equal:endDate', // not 'now' string
            ],
            'endDate' => [
                'date_format:Y-m-d',
                'after_or_equal:startDate'
            ],

        ];
    }


    /*
     * Returns validations errors.
     *
     * @param Validator $validator
     * @throws  HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            "message" => $this->validator->errors()->first(),
            'errors' => $validator->errors(),
            'status' => 422
        ], 422));

        parent::failedValidation($validator);
    }
}
