<?php

namespace App\Http\Requests\Portal\Translation;

use App\Http\Requests\BaseFormRequest;
use App\Models\Language;
use Illuminate\Validation\Rule;

class ExportTranslationsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'source_language' => [
                'required',
                'string',
                Rule::exists(Language::class, 'abbreviation')->withoutTrashed()
            ],
            'target_language' => [
                'required',
                'string',
                Rule::exists(Language::class, 'abbreviation')->withoutTrashed()
            ]
        ];
    }
}