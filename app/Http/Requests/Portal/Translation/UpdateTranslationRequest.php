<?php

namespace App\Http\Requests\Portal\Translation;

use App\Http\Requests\BaseFormRequest;

use function now;

class UpdateTranslationRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'group' => [
                'required',
            ],
            'key' => 'required',
            'languages' => 'required|array',
            'tags' => 'nullable|array',
            'languages.*' => [
                'required_array_keys:language_id,abbreviation,value',
            ],
            'languages.*.language_id' => 'exists:languages,id',
            'placeholders' => 'nullable|array',
            'placeholders.*' => 'required_array_keys:text,value',
            'isPublished' => 'nullable|boolean|exclude',
        ];
    }

    public function mergeToValidated(): array
    {
        return [
            'publishedAt' => $this->boolean('isPublished') ? now()->toDateTimeString() : null
        ];
    }

}