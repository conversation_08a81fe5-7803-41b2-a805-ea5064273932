<?php

namespace App\Http\Requests\Portal\Translation;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\File;

class ImportTranslationsRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'file' => [
                'required',
                File::types(['xlsx', 'csv', 'xls'])
                    ->max(12 * 1024)
            ]
        ];
    }
}