<?php

namespace App\Http\Requests\Portal\Translation;

use App\Enums\DatabaseConnectionsEnum;
use App\Http\Requests\BaseFormRequest;
use App\Rules\UniqueMultipleColumns;
use Illuminate\Validation\Rule;

class StoreTranslationRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'group' => [
                'required',

                Rule::unique('translation_keys')
                    ->where('key', $this->input('key'))
                    ->where('group', $this->input('group'))
                //    'lowercase',

                /*     new UniqueMultipleColumns(
                DatabaseConnectionsEnum::TRANSLATIONS->tableName('translation_keys'),
                ['group', 'key']
                )
                */

            ],
            'key' => 'required',
            'languages' => 'required|array',
            'tags' => 'nullable|array',
            'tags.*' => 'required_array_keys:value,text',
            'tags.*.value' => 'required|numeric',
            'languages.*' => 'required_array_keys:language_id,abbreviation,value',
            'placeholders' => 'nullable|array',
            'placeholders.*' => 'required_array_keys:text,value',
            'isPublished' => 'nullable|boolean|exclude',
        ];
    }

    public function mergeToValidated(): array
    {
        return [
            'publishedAt' => $this->boolean('isPublished') ? now()->toDateTimeString() : null
        ];
    }
}