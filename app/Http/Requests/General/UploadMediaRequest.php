<?php

namespace App\Http\Requests\General;

use App\Rules\AllowedMimeTypes;

class UploadMediaRequest extends \App\Http\Requests\Api\FormRequest
{


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            // 'file' => "required|file|mimes:$this->allowFileExtention",
            //"alt" => "required",
            'file' => [
                'required',
                'file',
                new AllowedMimeTypes([
                    'image/jpeg',
                    'image/png',
                    'image/jpg',
                    'image/gif',
                    'image/svg+xml',
                    'image/webp',
                    'image/webm',
                    'video/mp4',
                    'video/webm',
                ])
            ],

            'startDate' =>
                [
                    //'required',
                    'date_format:Y-m-d', // format without hours, minutes and seconds.
                    'before_or_equal:endDate', // not 'now' string
                ],
            'endDate' => [
                'date_format:Y-m-d',
                'after_or_equal:startDate'
            ],

        ];
    }



}