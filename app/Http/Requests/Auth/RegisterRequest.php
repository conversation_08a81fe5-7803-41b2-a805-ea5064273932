<?php

namespace App\Http\Requests\Auth;

use App\Rules\UniquePhone;
use Illuminate\Contracts\Validation\Validator;
use App\Http\Requests\BaseFormRequest;

use Illuminate\Http\Exceptions\HttpResponseException;

class RegisterRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'phone' => ['required', 'array', new UniquePhone()],
            'email' => 'nullable|unique:users,email',
            'password' => 'required',
            'firstName' => 'required|string',
            'lastName' => 'required|string',
            'gender' => 'nullable',
            'birthday' => 'nullable|date',
            'src' => 'nullable'

        ];
    }


}