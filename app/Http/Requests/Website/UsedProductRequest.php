<?php

namespace App\Http\Requests\Website;

use App\Http\Requests\BaseFormRequest;

class UsedProductRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        if ($this->isMethod('post')) {
            return [
                'name' => ['required'],
                'description' => 'required',
                'brandId' => 'required|exists:brands,brandId',
                'cityId' => 'required|exists:cities,cityId',
                'price' => 'required',
                'attributes' => 'nullable',
                'usedCategories' => 'nullable',
                'media' => 'nullable',


            ];
        }

        if ($this->isMethod('put')) {
            return [
                'name' => 'nullable',
                'description' => 'nullable',
                'brandId' => 'nullable',
                'cityId' => 'nullable|exists:cities,cityId',
                'price' => 'nullable',
                'attributes' => 'nullable',
                'usedCategories' => 'nullable',
                'media' => 'nullable',

            ];
        }
    }
}
