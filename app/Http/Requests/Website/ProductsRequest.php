<?php

namespace App\Http\Requests\Website;

use App\Http\Requests\BaseFormRequest;

class ProductsRequest extends BaseFormRequest
{



    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        // get scout sortable fields
        $sortableFields = config('scout.meilisearch.index-settings.App\Models\Variance.sortableAttributes');
        /*
        array:6 [
                    0 => "name"
                    1 => "createdAt"
                    2 => "productId"
                    3 => "basePrice"
                    4 => "avgRate"
                    5 => "score"
                ]
        */
        // dd('nullable|regex:/^(' . join('|', $sortableFields) . '),(asc|desc)$/i');
        return [
            'page' => 'numeric',
            'perPage' => 'numeric',
            'orderBy' => ['nullable', 'regex:/^(' . join('|', $sortableFields) . '),(asc|desc)$/i'],
        ];
    }


    /**
     * Get the validation data for the request.
     *
     * @return array
     */
    function validationData()
    {
        return $this->query();
    }
}
