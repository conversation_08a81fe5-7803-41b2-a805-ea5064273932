<?php

namespace App\Http\Requests\Website;

use App\Http\Requests\BaseFormRequest;

class FCMTokenRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            "token" => "required|string",
            "device" => "required", // web , mobile,
            "expiryDate" => "required|date"
        ];

    }


}