<?php

namespace App\Http\Requests\Website;

use App\Rules\PhoneNumberValidator;
use App\Http\Requests\BaseFormRequest;

class AddressRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'cityId' => 'required|integer|exists:cities,cityId',
            'district' => 'required|string',
            'phone' => ['required_if:isProfileDataShared,false', new PhoneNumberValidator],
            'street' => 'nullable|string',
            'isProfileDataShared' => 'nullable|boolean',
            'firstName' => 'required_if:isProfileDataShared,false|string',
            'lastName' => 'required_if:isProfileDataShared,false|string',
            'apartmentNumber' => 'nullable|string',
            'buildingNumber' => 'nullable|string',
            'default' => 'nullable',
            'buildingType' => 'nullable|string',
            'fullAddress' => 'nullable|string',
        ];
    }


}