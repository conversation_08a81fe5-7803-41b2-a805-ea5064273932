<?php

namespace App\Http\Requests\Website;

use App\Rules\VarianceHasStockValidator;
use App\Http\Requests\BaseFormRequest;

class CartRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            // 'quantity' => 'required|numeric|max:' . Variance::find($this->varianceId)->activeStock->maxPerUser,
            'quantity' => 'required|numeric',
            'productId' => ['required', 'integer', 'exists:products,productId'],
            'varianceId' => ['nullable', 'integer', 'exists:variances,varianceId', new VarianceHasStockValidator($this->productId, $this->quantity)],
            'bundleId' => 'nullable|integer|exists:bundles,bundleId',

        ];
    }


}