<?php

namespace App\Http\Requests\Website\Order;


use App\Http\Requests\BaseFormRequest;

class OrderPaymentFormRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {


        return [
            'email' => 'required|email',
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
        ];
        // $rules = [
        //     'paymentMethodId' => 'required|integer|exists:payments_methods,paymentMethodId',
        // ];
        // $paymentMethodId = $this->input('paymentMethodId');
        // $paymentMethod = PaymentsMethod::find($paymentMethodId);

        // if ($paymentMethod && $paymentMethod->module === PaymentMethodEnum::hyperpay->value) {
        //     // If the payment method type is 'hyperpay', require additional fields
        //     $rules = array_merge($rules, [
        //         'email' => 'required|email',
        //         'firstName' => 'required|string|max:255',
        //         'lastName' => 'required|string|max:255',
        //     ]);
        // }
        // return $rules;
    }


}