<?php

namespace App\Http\Requests\Website\Order;

use App\Http\Requests\BaseFormRequest;

class VerifyRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
         
        ];

    }

 
}