<?php

namespace App\Http\Requests\Website\Order;

use App\Enums\PaymentMethodEnum;
use App\Http\Requests\BaseFormRequest;
use App\Models\PaymentsMethod;

class OrderPaymentRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        $rules = [
            'paymentMethodId' => 'required|integer|exists:payments_methods,paymentMethodId',
        ];

        return $rules;
    }


}