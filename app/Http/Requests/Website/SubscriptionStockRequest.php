<?php

namespace App\Http\Requests\Website;

use App\Http\Requests\BaseFormRequest;

class SubscriptionStockRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'productId' => 'required',
            'varianceId' => 'required',
            "email" => 'nullable|email',
            "phone" => 'nullable|array',

            // email required for when user is not logged in


        ];
    }



}