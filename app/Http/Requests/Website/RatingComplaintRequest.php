<?php

namespace App\Http\Requests\Website;

use Illuminate\Foundation\Http\FormRequest;

class RatingComplaintRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 'review' => 'required|string',
            // 'rating' => 'required|integer|min:1|max:5',
            // 'complaintId' => 'required|integer|exists:complaints,complaintId',
            // 'model_type' => 'required|string',
            // 'model_id' => 'required|integer',
        ];
    }
}