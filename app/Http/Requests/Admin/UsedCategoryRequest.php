<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Rules\ContainsJsonENAR;
use App\Rules\UniqueJson;

class UsedCategoryRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => ['required', new ContainsJsonENAR, new UniqueJson('used_categories', 'name', ['ar', 'en'], $this->route('used_category')),],
            'parentId' => 'nullable|integer|exists:used_categories,usedCategoryId',
            'usedCategoriesAttributes' => 'required|exists:attributes,attributeId',
            'usedCategoriesBrands' => 'required|exists:brands,brandId',
            'media' => 'nullable',
            'metaDescription' => ['nullable', new ContainsJsonENAR],
            'metaTitle' => ['nullable', new ContainsJsonENAR],

        ];
    }
}
