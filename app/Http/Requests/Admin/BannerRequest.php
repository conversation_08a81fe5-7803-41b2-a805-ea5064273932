<?php

namespace App\Http\Requests\Admin;

use App\Models\Brand;
use App\Rules\ContainsJsonENAR;
use App\Rules\UniqueJson;
use App\Http\Requests\BaseFormRequest;


class BannerRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            'url' => 'required',
            'media' => 'required',
            'extra' => 'nullable|array',
            'isPublished' => 'nullable',
            'publishedAt' => 'nullable|date|date_format:Y-m-d H:i:s',
            'unPublishedAt' => 'nullable|date|date_format:Y-m-d H:i:s',

        ];

    }
}