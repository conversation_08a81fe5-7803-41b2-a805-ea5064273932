<?php

namespace App\Http\Requests\Admin;


use App\Rules\UniqueJson;
use App\Rules\ContainsJsonENAR;
use App\Http\Requests\BaseFormRequest;


class FilterGroupRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', new ContainsJsonENAR, new UniqueJson('filters_groups', 'name', ['ar', 'en'], $this->route('group_filter')),],
            'filters' => 'nullable'
        ];
    }
}