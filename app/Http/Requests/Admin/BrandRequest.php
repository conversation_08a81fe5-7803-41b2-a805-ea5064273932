<?php

namespace App\Http\Requests\Admin;

use App\Models\Brand;
use App\Rules\ContainsJsonENAR;
use App\Rules\UniqueJson;
use App\Http\Requests\BaseFormRequest;


class BrandRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        return [
            'name' => ['required', new ContainsJsonENAR, new UniqueJson(Brand::getTableName(), 'name', ['ar', 'en'], $this->route('brand'))],
            'media' => 'required',
            'inHomePage' => 'required',
            'isPublished' => 'nullable',
            'metaTitle' => 'nullable',
            'metaDescription' => 'nullable',
            'labels' => 'nullable|array',
            'publishedAt' => 'nullable|date|date_format:Y-m-d H:i:s',
            'unPublishedAt' => 'nullable|date|date_format:Y-m-d H:i:s',

        ];

    }
}