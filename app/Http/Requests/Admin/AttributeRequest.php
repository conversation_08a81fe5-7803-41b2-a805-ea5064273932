<?php

namespace App\Http\Requests\Admin;

use App\Rules\ContainsJsonENAR;
use App\Http\Requests\BaseFormRequest;

class AttributeRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', new ContainsJsonENAR],
            'prefix' => 'nullable',
            'suffix' => 'nullable',
            'key' => 'required',
            'isRequired' => 'boolean',
            'type' => 'required|in:radio,checkbox,chips,brand,range,color,text,select,number,textarea',
            'options' => 'nullable',
            // 'options.*.name' => ['nullable', new ContainsJsonENAR]
        ];
    }
}