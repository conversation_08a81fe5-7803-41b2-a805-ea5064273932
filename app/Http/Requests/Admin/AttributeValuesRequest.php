<?php

namespace App\Http\Requests\Admin;

use Illuminate\Contracts\Validation\Validator;
use App\Http\Requests\BaseFormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class AttributeValuesRequest extends BaseFormRequest
{


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return  [
            'varianceId' => 'required|integer|exists:variances,varianceId',
            'attributeId' => 'required|integer|exists:attributes,attributeId',
            'attributeOptionId' => 'required|integer|exists:attributes_options,attributeOptionId'
        ];
    }
}
