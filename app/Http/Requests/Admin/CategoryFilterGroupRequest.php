<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;


class CategoryFilterGroupRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
          'filtersGroupsId'=>'required|integer|exists:filters_groups,filtersGroupsId',
          'categoryId'=>'required|integer|exists:categories,categoryId',
        ];
    }
}