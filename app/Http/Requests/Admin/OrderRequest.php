<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Rules\ContainsJsonENAR;

class OrderRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [

            // 'userId' => 'required|exists:users,userId',
            // 'total' => 'required||numeric|min:1',
            // 'paymentMethodId' => 'required|exists:payments_methods,paymentMethodId',
            // 'note' => ['nullable', new ContainsJsonENAR],
            // 'addressId' => 'required|exists:addresses,addressId',
            // 'shippingCarrierId' => 'required|exists:shipping_carriers,shippingCarrierId',
            // 'tax' => 'numeric|min:1',
            // 'orderItems.*.varianceId' => 'required|exists:variances,varianceId',
            // 'orderItems.*.productId' => 'required|exists:products,productId',
            // "orderItems.*.quantity" => 'required|numeric|min:1',

            // "orderItems.*.price" => 'required|numeric|min:1',
            // "orderItems.*.discount" => 'numeric',
            // "orderItems.*.originalPrice" => 'required|numeric|min:1',
        ];
    }
}