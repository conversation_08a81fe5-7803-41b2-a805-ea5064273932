<?php

namespace App\Http\Requests\Admin;

use App\Rules\ContainsJsonENAR;

use App\Rules\ArrayExistsInTable;
use App\Http\Requests\BaseFormRequest;
use App\Rules\UniqueJson;

class UsedProductRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */


    public function rules()
    {

        return [
            'name' => ['required', new ContainsJsonENAR, new UniqueJson('used_products', 'name', ['ar', 'en'], $this->route('used_product'))],
            'description' => ['required', new ContainsJsonENAR],
            'isPublished' => 'nullable|boolean',
            'brandId' => 'required|exists:brands,brandId',
            'cityId' => 'required|exists:cities,cityId',
            'price.value' => 'required|numeric|min:1',
            'price.currencyId' => 'required|exists:currencies,currencyId',
            'attributes.*.attributeId' => 'exists:attributes,attributeId',
            'attributes.*.attributeOptionId' => 'exists:attributes_options,attributeOptionId',
            'attributes.*.value' => 'nullable',
            'usedCategories' => 'nullable',
            'media' => 'nullable',
            'publishedAt' => 'nullable',
            'unPublishedAt' => 'nullable',

        ];

    }



}