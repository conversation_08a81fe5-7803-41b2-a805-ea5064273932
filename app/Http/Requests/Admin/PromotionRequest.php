<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;

class PromotionRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required|unique:promotions,name',
            'rule' => 'required',
            'publishedAt'=>'nullable|date|date_format:Y-m-d H:i:s',
            'unPublishedAt'=>'nullable|date|date_format:Y-m-d H:i:s',
        ];
    }
}