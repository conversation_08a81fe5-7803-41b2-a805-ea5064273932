<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class VarianceRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return  [
            'brandId' => 'required|integer|exists:brands,brandId',
            'attributeId'  => 'required|integer|exists:attributes,attributeId',
            'SKU' => 'required|integer',
            'name' => 'required|max:255',
            'type' => 'required|in:physical,digital',
            'metaTitle' => 'nullable',
            'metaDescription' => 'nullable',
        ];
    }
}
