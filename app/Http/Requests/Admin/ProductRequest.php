<?php

namespace App\Http\Requests\Admin;

use App\Rules\UniqueJson;
use App\Enums\ProductTypeEnum;
use App\Enums\VarianceTypeEnum;
use App\Rules\ContainsJsonENAR;
use App\Rules\ArrayExistsInTable;
use App\Http\Requests\BaseFormRequest;
use App\Rules\HaveOneDefault;

class ProductRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        $rulesProduct = [
            // 'name' => ['required', new ContainsJsonENAR, new UniqueJson('products', 'name', ['ar', 'en'], $this->route('product')),],
            'name' => ['required', 'array', new ContainsJsonENAR],
            'description' => ['required', 'array', new ContainsJsonENAR],
            'type' => 'required|in:' . ProductTypeEnum::getString(),
            'isPublished' => 'required|boolean',
            "SKU" => 'nullable',
            'isListed' => 'required|boolean',
            'productGroupId' => 'nullable||exists:product_groups,productGroupId',
            'releaseAt' => 'nullable|date|date_format:Y-m-d',
            'publishedAt' => 'nullable|date', //date_format:Y-m-d\TH:i:s.u\Z
            // 'attributeIds' =>['nullable','array',new ArrayExistsInTable('attributes', 'attributeId')],
            'relatedTargetProductId' => ['nullable', 'array', new ArrayExistsInTable('products', 'productId')],
            'suggestedTargetProductId' => ['nullable', 'array', new ArrayExistsInTable('products', 'productId')],

            // 'stocks.*.note' => ['nullable', new ContainsJsonENAR],
            'categories' => ['required', 'array', 'min:1', new ArrayExistsInTable('categories', 'categoryId')],
            'variationAttributes' => ['nullable', 'array', new ArrayExistsInTable('attributes', 'attributeId')],
            'media' => 'nullable',
            'media.cover' => ['required', 'array', 'min:1'],
            'attributes.*.attributeId' => 'required|exists:attributes,attributeId',
            // 'attributes.*.attributeOptionId' => 'required|exists:attributes_options,attributeOptionId',
            'attributes.*.attributeOptionId' => 'required',
            'attributes.*.value' => 'nullable',

            //'paymentsMethod'=>['nullable','array',new ArrayExistsInTable('payments_methods', 'paymentMethodId')],
            'shippingId' => ['nullable', 'numeric', 'exists:shippings,shippingId'],

            'stocks' => 'nullable',
            'stocks.*.cost.value' => 'nullable|numeric',
            'stocks.*.cost.currencyId' => 'required|exists:currencies,currencyId',
            'stocks.*.price.value' => 'required|numeric|min:0.1',
            'stocks.*.quantity' => 'required|numeric|min:0',
            'stocks.*.maxPerUser' => 'nullable|numeric',
            'stocks.*.publishedAt' => 'nullable|date|required_if:stocks.*.isOffer,true',
            'stocks.*.unPublishedAt' => 'nullable|date|required_if:stocks.*.isOffer,true',
            'stocks.*.supplierId' => 'required|exists:suppliers,supplierId',
            'stocks.*.isOffer' => 'nullable|boolean',
        ];

        if ($this->request->get('type') === 'alternative') {
            $rulesProduct['brandId'] = 'required|exists:brands,brandId';
            $rulesVariances = [
                'variances' => ['required', 'array', new HaveOneDefault],
                'variances.*.isDefault' => 'required',
                'variances.*.SKU' => 'nullable',
                'variances.*.name' => ['required', new ContainsJsonENAR],
                'variances.*.slug' => ['required'],
                'variances.*.description' => 'nullable|array',
                //'variances.*.brandId'=>'required|exists:brands,brandId',
                //'variances.*.attributeId'=>'required|exists:attributes,attributeId',
                'variances.*.type' => 'required|in:' . VarianceTypeEnum::getString(),
                // 'variances.*.metaTitle' => ['required', new ContainsJsonENAR],
                // 'variances.*.metaDescription' => ['required', new ContainsJsonENAR],
                'variances.*.publishedAt' => 'nullable|date', //|date_format:Y-m-d\TH:i:s.u\Z
                'variances.*.stocks' => 'array|min:1',
                'variances.*.stocks.*.quantity' => 'required|numeric|min:0',
                'variances.*.stocks.*.maxPerUser' => 'nullable|numeric',
                'variances.*.stocks.*.publishedAt' => 'nullable|date|required_if:variances.*.stocks.*.isOffer,true',
                'variances.*.stocks.*.unPublishedAt' => 'nullable|date|required_if:variances.*.stocks.*.isOffer,true',
                'variances.*.stocks.*.supplierId' => 'required|exists:suppliers,supplierId',
                'variances.*.auctionId' => 'nullable',
                'variances.*.stocks.*.cost.value' => 'nullable|numeric',
                'variances.*.stocks.*.cost.currencyId' => 'required|exists:currencies,currencyId',
                'variances.*.stocks.*.price.value' => 'required|numeric|min:0.1',
                'variances.*.stocks.*.price.currencyId' => 'required|exists:currencies,currencyId',
                // 'variances.*.stocks.*.note' => ['nullable', new ContainsJsonENAR],
                'variances.*.attributes.*.attributeId' => 'required|exists:attributes,attributeId',
                // 'variances.*.attributes.*.attributeOptionId' => 'required|exists:attributes_options,attributeOptionId',
                'variances.*.attributes.*.attributeOptionId' => 'required',
                'variances.*.attributes.*.value' => 'nullable',
                'variances.*.media' => 'nullable',

            ];
            $rules = array_merge($rulesProduct, $rulesVariances);
        } elseif ($this->request->get('type') === 'bundle') {
            $rulesProduct['stokes'] = 'required|array|min:1';
            $rulesBundles = [
                'bundles' => ['required', 'array'],
                'bundles.*.varianceId' => 'nullable',
                // 'bundles.*.name' => ['required', new ContainsJsonENAR],
                // 'bundles.*.slug' => ['required', new ContainsJsonENAR],
                // 'bundles.*.stokes.*.quantity' => 'required|numeric|min:1',
                // 'bundles.*.stokes.*.maxPerUser' => 'nullable|numeric',
                // 'bundles.*.stokes.*.publishedAt' => 'nullable|date', //date_format:Y-m-d\TH:i:s.u\Z,
                // 'bundles.*.stokes.*.unPublishedAt' => 'nullable|date', //date_format:Y-m-d\TH:i:s.u\Z
                // 'bundles.*.stokes.*.supplierId' => 'required|exists:suppliers,supplierId',
                // 'bundles.*.stokes.*.cost' => 'required|numeric|min:1',
                // 'bundles.*.stokes.*.price' => 'required|numeric|min:1',
                // // 'bundles.*.stocks.*.note' => ['nullable', new ContainsJsonENAR],
                // 'bundles.*.variances.*.varianceId' => 'required|exists:variances,varianceId',

            ];

            $rules = array_merge($rulesProduct, $rulesBundles);
        } else { // simple
            // simple variance
            $rulesProduct['brandId'] = 'required|exists:brands,brandId';

            $rulesSimple = [
                'variances' => 'required|array',
                'variances.*.isDefault' => 'required',
                'variances.*.SKU' => 'nullable',
                'variances.*.name' => ['required', new ContainsJsonENAR],
                'variances.*.slug' => ['required'],
                //'variances.*.brandId'=>'required|exists:brands,brandId',
                //'variances.*.attributeId'=>'required|exists:attributes,attributeId',
                'variances.*.type' => 'required|in:' . VarianceTypeEnum::getString(),
                // 'variances.*.metaTitle' => ['required', new ContainsJsonENAR],
                // 'variances.*.metaDescription' => ['required', new ContainsJsonENAR],
                'variances.*.description' => 'nullable|array',
                'variances.*.publishedAt' => 'nullable|date', //|date_format:Y-m-d\TH:i:s.u\Z
                'variances.*.stocks' => 'array|min:1',
                'variances.*.stocks.*.cost.value' => 'nullable|numeric',
                'variances.*.stocks.*.cost.currencyId' => 'required|exists:currencies,currencyId',
                'variances.*.stocks.*.price.value' => 'required|numeric|min:0.1',
                'variances.*.stocks.*.quantity' => 'required|numeric|min:0',
                'variances.*.stocks.*.maxPerUser' => 'nullable|numeric',
                'variances.*.stocks.*.publishedAt' => 'nullable|date|required_if:variances.*.stocks.*.isOffer,true',
                'variances.*.stocks.*.unPublishedAt' => 'nullable|date|required_if:variances.*.stocks.*.isOffer,true',
                'variances.*.stocks.*.supplierId' => 'required|exists:suppliers,supplierId',
                // 'variances.*.stocks.*.note' => ['nullable', new ContainsJsonENAR],
                'variances.*.auctionId' => 'nullable',
                'variances.*.stocks.*.price.currencyId' => 'required|exists:currencies,currencyId',
                'variances.*.attributes.*.attributeId' => 'required|exists:attributes,attributeId',
                'variances.*.attributes.*.attributeOptionId' => 'required|exists:attributes_options,attributeOptionId',
                'variances.*.attributes.*.value' => 'nullable',
                'variances.*.media' => 'nullable',
            ];
            $rules = array_merge($rulesProduct, $rulesSimple);
        }
        return $rules;
    }
}