<?php

namespace App\Http\Requests\Admin;

use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;
use App\Http\Requests\BaseFormRequest;

class UserRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {

        $rules = [
            // Common rules for both create and update
            'phone' => 'required|unique:users,phone,' . $this->route('user') . ',userId',
            'email' => 'nullable|email|unique:users,email,' . $this->route('user') . ',userId',
            'firstName' => 'required',
            'lastName' => 'required',
            'gender' => 'nullable|in:male,female',
            'birthday' => 'nullable|date',
            'status' => 'required|in:pending,suspended,active',
            'media' => 'nullable',

        ];

        if ($this->isMethod('post')) {
            // Rules for creating a new user
            $rules['password'] = 'required|string|min:8';
            $rules['passwordConfirmation'] = 'required|string|same:password';
        }

        if ($this->isMethod('patch') || $this->isMethod('put')) {
            // Rules for updating an existing user
            $rules['password'] = 'nullable|string|min:8';
            $rules['passwordConfirmation'] = 'nullable|string|same:password';
        }
        return $rules;
        // return [
        //     'phone' => 'required|unique:users,phone',
        //     'email' => 'required|unique:users,email',
        //     'password' => 'required|string|min:8',
        //     'passwordConfirmation' => 'required|string|same:password',
        //     'firstName' => 'required',
        //     'lastName' => 'required',
        //     'gender' => 'required|in:male,female',
        //     'birthday' => 'nullable|date',
        //     'status' => 'required|in:pending,suspended,active',
        //     'media' => 'nullable',
        // ];
    }

}