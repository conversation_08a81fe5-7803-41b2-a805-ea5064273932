<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;


class OrderCostRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'cost' => ['required', 'array'],
            'cost.value' => 'numeric',
            'cost.currencyId' => 'required|exists:currencies,currencyId',
        ];
    }
}