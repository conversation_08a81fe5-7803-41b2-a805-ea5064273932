<?php

namespace App\Http\Requests\Admin;

use App\Enums\FilterTypeEnum;
use App\Enums\ComponentTypeEnum;
use App\Http\Requests\BaseFormRequest;


class FilterRequest extends BaseFormRequest
{

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "filterType" =>"required|in:".implode(',', FilterTypeEnum::getAll()),
            "componentType" =>"required|in:".implode(',', ComponentTypeEnum::getAll()),
            "filtersGroupsId" =>'required|integer|exists:filters_groups,filtersGroupsId',
            // "config"=>'json'

        ];
    }
}