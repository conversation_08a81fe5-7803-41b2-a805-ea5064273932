<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class BaseFormRequest extends FormRequest
{
    /**
     * Merge inputs to validated request method .
     * @return array
     */
    public function mergeToValidated(): array
    {
        return [];
    }

    /**
     * Override validated request method .
     *
     * @param $key
     * @param $default
     * @return array
     */
    public function validated($key = null, $default = null)
    {
        return [...parent::validated($key, $default), ...$this->mergeToValidated()];
    }


    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            "message" => $this->validator->errors()->first(),
            'errors' => $validator->errors(),
            'status' => 422
        ], 422));
    }
}
