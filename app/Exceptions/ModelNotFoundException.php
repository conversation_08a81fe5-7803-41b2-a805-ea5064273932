<?php
namespace App\Exceptions;

use Exception;

class ModelNotFoundException extends Exception
{
    protected $redirectUrl;

    public function __construct($message = "model not found", $code = 301, $redirectUrl = null)
    {
        parent::__construct($message, $code);
        $this->redirectUrl = $redirectUrl;
    }

    public function getRedirectUrl()
    {
        return $this->redirectUrl;
    }
}