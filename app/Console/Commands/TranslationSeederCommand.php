<?php

namespace App\Console\Commands;

use App\Enums\CacheKeysEnum;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class TranslationSeederCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translation:seeder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'this comand for translation websaite';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        Artisan::call('db:seed', ['--class' => 'LanguageSeeder']);
        Artisan::call('db:seed', ['--class' => 'TranslationKeysTableSeeder']);
        Artisan::call('db:seed', ['--class' => 'TranslationValuesTableSeeder']);
        Artisan::call('db:seed', ['--class' => 'LanguageHasTranslationsTableSeeder']);
        Artisan::call('db:seed', ['--class' => 'TagHasTranslationKeysTableSeeder']);
        // Artisan::call('db:seed', ['--class' => 'TranslationKeyHasTranslationValuesTableSeeder']);

        $cacheKey = CacheKeysEnum::translations->value;
        cache()->forget($cacheKey . '.*');

        //logs the message on the console.
        $this->info('translation seed complete successfully!!!');

        return 0;
    }
}
