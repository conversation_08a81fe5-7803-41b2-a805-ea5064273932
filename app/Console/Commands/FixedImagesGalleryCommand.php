<?php

namespace App\Console\Commands;

use App\Enums\ProductTypeEnum;
use App\Helper\SlugHelper;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixedImagesGalleryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixed:images_gallery';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        dd('sss');
        // $products = Product::all();

        // foreach ($products as $product) {
        //     $migrationProduct = DB::table('migration_products')->select(['*'])->where('productId', $product->productId)->first();
        //     if (!is_null($migrationProduct)) {
        //         if ($product->gallery->count() == 0) {
        //             $this->info("start product $product->productId");
        //             $images = DB::connection('old')->table('items_images')->select('*')->where('item_id', $migrationProduct->oldProductId)->get();
        //             foreach ($images as $image) {
        //                 try {
        //                     $img = "https://s3.eu-west-1.amazonaws.com/media.action.jo/images/$image->image";
        //                     $this->saveMedia($product, $img, "gallery");
        //                 } catch (\Exception $exception) {
        //                     $this->error($exception->getMessage());
        //                 }

        //             }

        //         }


        //     }

        // }

    }



    public function saveMedia($model, $imagePath, $collection)
    {

        try {
            $model->addMediaFromUrl($imagePath)
                ->toMediaCollection($collection, env('MEDIA_DISK'));
            $this->info("save image  $imagePath");
        } catch (\Exception $exception) {

            $this->error($exception->getMessage());
        }

    }
}