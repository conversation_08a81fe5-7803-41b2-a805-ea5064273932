<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportAuction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:auction';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        DB::table('migration_auction')->truncate();
        DB::connection('old')->table('auction')->select('*')->orderBy('auction_id', 'asc')->chunk(100, function ($oldAuctions) {
            $newAuctionsArray = [];
            foreach ($oldAuctions as $auction) {
                $newAuctionsArray[] = [
                    'auction_id' => $auction->auction_id,
                    'start_price' => $auction->start_price,
                    'start_time' => $auction->start_time,
                    'end_time' => $auction->end_time,
                    'stop_time' => $auction->stop_time,
                    'image' => $auction->image,
                    'item_id' => $auction->item_id,
                    'winner' => $auction->winner,
                    'last_bid' => $auction->last_bid,
                    'returned_money' => $auction->returned_money,
                    'deleted_at' => $auction->deleted_at,
                    'published_at' => $auction->published_at,
                    'created_at' => $auction->created_at,
                    'updated_at' => $auction->updated_at,
                ];
            }

            DB::table('migration_auction')->insert($newAuctionsArray);

        });
    }
}
