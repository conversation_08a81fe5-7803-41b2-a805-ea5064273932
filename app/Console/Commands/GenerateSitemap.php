<?php

namespace App\Console\Commands;

use App\Enums\ProductTypeEnum;
use App\Models\Category;
use App\Models\Page;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Spatie\Sitemap\Sitemap;
use <PERSON><PERSON>\Sitemap\Tags\Url;
class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'generate:sitemap';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $frontUrl = '';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $productsitemap = Sitemap::create();

        $this->frontUrl = env('FRONTEND_URL');


        // home page 
        $productsitemap->add(
            Url::create($this->frontUrl) // Use FRONTEND_URL for homepage
                ->setPriority(1.0) // Set a high priority for the homepage
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_DAILY) // Change frequency can be daily or weekly
        );



        Category::all()->each(function (Category $category) use ($productsitemap) {

            $url = Url::create(url: "$this->frontUrl/category/en/" . $category->slug)
                ->setPriority(0.7)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY);

            if ($category->updatedAt) {
                $url->setLastModificationDate($category->updatedAt);
            }
            $productsitemap->add($url);


            $url = Url::create("$this->frontUrl/category/ar/" . $category->slug)
                ->setPriority(0.7)
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_WEEKLY);

            if ($category->updatedAt) {
                $url->setLastModificationDate($category->updatedAt);
            }

            $productsitemap->add($url);

        });


        Page::all()->each(function (Page $page) use ($productsitemap) {
            $url = Url::create("$this->frontUrl/en/page/$page->slug")// Adjust the route as necessary
                ->setPriority(0.8) // Set priority for pages
                ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY);
            // Set last modified if available
            if ($page->updatedAt) {
                $url->setLastModificationDate($page->updatedAt);
            }




            $this->info($page->slug);
            $productsitemap->add($url);

        });



        Product::all()->each(function (Product $product) use ($productsitemap) {
            // Check if the product has a cover image

            // $this->info($product->covers->first()->getFullUrl());
            if ($product->covers->count()) {

                foreach (["en", 'ar'] as $lang) {
                    $element = Url::create("$this->frontUrl/product/$lang/" . $product->slug)
                        ->setPriority(0.9)
                        ->addImage($product->covers->first()->getFullUrl()) // Ensure cover exists
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY);


                    if ($product->type == ProductTypeEnum::alternative->value && $product->variances->count()) {
                        foreach ($product->variances as $variance) {
                            $element->addAlternate("{$this->frontUrl}/product/$lang/" . $product->slug . "/" . $variance->slug, $lang);
                        }
                    }

                    $productsitemap->add($element);

                }



            } else {

                foreach (["en", 'ar'] as $lang) {
                    $element = Url::create("$this->frontUrl/product/$lang/" . $product->slug)
                        ->setPriority(0.9)
                        ->setChangeFrequency(Url::CHANGE_FREQUENCY_MONTHLY);


                    if ($product->type == ProductTypeEnum::alternative->value && $product->variances->count()) {
                        foreach ($product->variances as $variance) {
                            $element->addAlternate("{$this->frontUrl}/product/$lang/" . $product->slug . "/" . $variance->slug, $lang);
                        }
                    }

                    $productsitemap->add($element);

                }

            }


            $this->info($product->slug);
            $this->info($product->slug);
        });




        $productsitemap->writeToDisk('s3', "sitemap/sitemap.xml");
    }
}