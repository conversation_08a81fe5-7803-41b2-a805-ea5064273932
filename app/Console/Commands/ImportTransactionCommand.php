<?php

namespace App\Console\Commands;

use App\Enums\OrderPaymentStatusEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Models\Address;
use App\Models\Order;
use App\Models\ShippingCarriers;
use App\Models\Transaction;
use App\PaymentMethod\ArabBank;
use App\PaymentMethod\HyperPay;
use App\Repositories\PaymentMethod\PaymentMethodRepositoryInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportTransactionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:transaction';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        dd('sss');
        $shippingCarrier = ShippingCarriers::first();
        DB::connection('old')->table('transactions')->select('transactions.*', 'orders.*')
            ->join('orders', 'transactions.order_id', 'orders.id')->where('status', "success")
            ->chunk(100, function ($transactions) use ($shippingCarrier) {

                $arabBankModel = resolve(PaymentMethodRepositoryInterface::class)->findByModule(PaymentMethodEnum::arabbank->value);
                $hyperpayModel = resolve(PaymentMethodRepositoryInterface::class)->findByModule(PaymentMethodEnum::hyperpay->value);


                foreach ($transactions as $transaction) {
                    $modelName = is_null($transaction->checkout_id) ? HyperPay::class : ArabBank::class;
                    $modelId = is_null($transaction->checkout_id) ? $arabBankModel->paymentMethodId : $hyperpayModel->paymentMethodId;

                    Transaction::create([
                        "openingBalance" => 0,
                        "amount" => (float) $transaction->total,
                        "closingBalance" => 0,
                        "userId" => $transaction->user_id,
                        "date" => $transaction->created_at,
                        "status" => $transaction->status,
                        "paymentMethodId" => $transaction->payment_method_id,
                        "response" => $transaction->provider_response,
                        "model_type" => $modelName,
                        "model_id" => $modelId,

                    ]);

                    $address = Address::create([
                        "city" => 1,
                        "district" => "amman",
                        "userId" => 0,
                        "phone" => [],
                        "street" => $transaction->location,
                        "recipientName" => $transaction->city,
                        "apartmentNumber" => 1,
                        "buildingNumber" => 1,
                        "default" => 1,

                    ]);

                    $order = Order::create([
                        "status" => OrderStatusEnum::received->value,
                        "paymentMethodId" => $modelId,
                        "userId" => 0,
                        "addressId" => $address->addressId,
                        "shippingPrice" => (float) (float) $transaction->delivery_cost,
                        "total" => (float) $transaction->total,
                        "subTotal" => (float) $transaction->total - (float) $transaction->delivery_cost,
                        "shippingCarrierId" => $shippingCarrier->shippingCarrierId,
                        "paymentStatus" => OrderPaymentStatusEnum::paid->value

                    ]);


                    Transaction::create([
                        "openingBalance" => 0,
                        "amount" => -1 * (float) $transaction->total,
                        "closingBalance" => 0,
                        "userId" => $transaction->user_id,
                        "model_type" => Order::class,
                        "model_id" => $order->orderId,
                        "status" => $transaction->status,
                        "createdAt" => $transaction->created_at,
                    ]);


                }

            });



        return 0;
    }
}