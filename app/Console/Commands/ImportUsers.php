<?php

namespace App\Console\Commands;

use App\Enums\OrderPaymentStatusEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\TransactionStatusEnum;
use App\Enums\UserStatusEnum;
use App\Models\Address;
use App\Models\City;
use App\Models\Country;
use App\Models\FillWallet;
use App\Models\Order;
use App\Models\ShippingCarriers;
use App\Models\Transaction;
use App\Models\User;
use App\PaymentMethod\ArabBank;
use App\PaymentMethod\HyperPay;
use App\Repositories\PaymentMethod\PaymentMethodRepositoryInterface;
use DB;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use libphonenumber\PhoneNumberUtil;

class ImportUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {



        // DB::table('migration_users')->delete();

        // Transaction::where('model_type', FillWallet::class)->delete();
        // Schema::disableForeignKeyConstraints();
        // User::truncate();

        $phoneUtil = \libphonenumber\PhoneNumberUtil::getInstance();
        $localization = new \ArPHP\I18N\Arabic();
        DB::connection('old')->table('users')->select('*')->where('id', '>', 41573)->orderBy('id', 'asc')->chunk(100, function ($oldUsers) use ($phoneUtil, $localization) {

            $oldUsers->each(function ($oldUser) use ($phoneUtil, $localization) {
                $phone = $this->getPhoneFormatted($oldUser, $phoneUtil);
                $hasError = $phone['hasError'];

                unset($phone['hasError']);



                // if (!is_null($oldUser->email)) {
                //     $user = User::where('email', trim($oldUser->email))->orWhereJsonContains('phone->number', $phone['number'])->first();

                // } else {
                //     $user = User::whereJsonContains('phone->number', $phone['number'])->first();

                // }



                // if (is_null($user)) {

                // $userModel = User::create(
                //     [
                //         'firstName' => $oldUser->first_name,
                //         'lastName' => $oldUser->last_name,
                //         'email' => $oldUser->email,
                //         'phone' => $phone,
                //         'status' => $oldUser->status == 'suspended' ? UserStatusEnum::suspended->value : UserStatusEnum::active->value,
                //         'password' => $oldUser->password,
                //         'wallet' => $oldUser->wallet,
                //         'createdAt' => $oldUser->created_at,
                //         'updatedAt' => $oldUser->updated_at,
                //         'deleted_at' => $oldUser->deleted_at
                //     ]
                // );
                $user = User::where('email', trim($oldUser->email))->first();

                $userId = DB::table('users')->insertGetId(
                    [
                        'firstName' => $oldUser->first_name,
                        'lastName' => $oldUser->last_name,
                        'email' => $user ? NULL : $oldUser->email,
                        'phone' => json_encode($phone),
                        'status' => $oldUser->status == 'suspended' ? UserStatusEnum::suspended->value : UserStatusEnum::active->value,
                        'password' => $oldUser->password,
                        'wallet' => $oldUser->wallet,
                        'createdAt' => $oldUser->created_at,
                        'updatedAt' => $oldUser->updated_at,
                        // 'deletedAt' => $oldUser->deleted_at
                    ]
                );

                $userModel = User::find($userId);


                // } else {

                //     DB::table('users')->where('userId', $user->userId)->update(

                //         [
                //             'firstName' => $oldUser->first_name,
                //             'lastName' => $oldUser->last_name,
                //             // 'email' => $oldUser->email,
                //             'phone' => json_encode($phone),
                //             'status' => $oldUser->status == 'suspended' ? UserStatusEnum::suspended->value : UserStatusEnum::active->value,
                //             'password' => $oldUser->password,
                //             'wallet' => $oldUser->wallet,
                //             'createdAt' => $oldUser->created_at,
                //             'updatedAt' => $oldUser->updated_at,
                //             // 'deletedAt' => $oldUser->deleted_at
                //         ]
                //     );

                //     // $userModel = tap($user)->update(
                //     //     [
                //     //         'firstName' => $oldUser->first_name,
                //     //         'lastName' => $oldUser->last_name,
                //     //         'email' => $oldUser->email,
                //     //         'phone' => $phone,
                //     //         'status' => $oldUser->status == 'suspended' ? UserStatusEnum::suspended->value : UserStatusEnum::active->value,
                //     //         'password' => $oldUser->password,
                //     //         'wallet' => $oldUser->wallet,
                //     //         'createdAt' => $oldUser->created_at,
                //     //         'updatedAt' => $oldUser->updated_at,
                //     //         'deleted_at' => $oldUser->deleted_at
                //     //     ]
                //     // );

                //     $userModel = User::find($user->userId);
                // }



                if ($oldUser->type == 'admin') {
                    //  $userModel->assignRole('admin');
                }


                $this->info("#id number $userModel->userId  insert $oldUser->first_name  $oldUser->last_name complete successfully ");

                DB::table('migration_users')->insert(
                    [
                        'userId' => $userModel->userId,
                        'oldUserId' => $oldUser->id,
                        'wallet' => (float) $oldUser->wallet,
                        "hasError" => $hasError
                    ]
                );

                $this->info("insert  migration users and wallet id $oldUser->wallet");

                if ($oldUser->type == 'customer') {
                    Transaction::create([
                        "openingBalance" => 0,
                        "amount" => (float) $oldUser->wallet,
                        "closingBalance" => (float) $oldUser->wallet,
                        "userId" => $userModel->userId,
                        "status" => TransactionStatusEnum::success->value,
                        "response" => [],
                        "model_type" => FillWallet::class,
                        "model_id" => 1,
                    ]);

                }

                $this->info("fill wallet ($oldUser->wallet) for $oldUser->first_name  $oldUser->last_name user id  $userModel->userId");


                try {
                    $order = DB::connection('old')->table('orders')->where('user_id', $oldUser->id)->first();
                    if (!is_null($order) && $order->city !== '') {
                        $cityModel = null;
                        $cityName = ucfirst(trim($order->city));
                        $cityModel = City::whereJsonContains('name->en', $cityName)->first();
                        if (is_null($cityModel)) {
                            $country = Country::updateOrCreate(['ISOCode' => 'JO'], ["name" => ["ar" => "الاردن", "en" => "jordan"], "phoneCode" => "962"]);
                            $countryId = $country->countryId;
                            // $cityModel = City::updateOrCreate(
                            //     [
                            //         'name->en' => $cityName
                            //     ],
                            //     [
                            //         'name' => [
                            //             'ar' => $cityName,
                            //             'en' => $localization->en2ar($cityName)
                            //         ],
                            //         'codeCity' => strtoupper($cityName),
                            //         'countryId' => $countryId
                            //     ],
                            // );

                            $this->info("add City ");
                        }
                        $address = Address::updateOrCreate([
                            'default' => true,
                            'userId' => $userModel->userId,
                        ], [
                            'cityId' => $cityModel->cityId,
                            'district' => $order->location,
                            'phone' => $phone,
                            'street' => $order->location,
                            'userId' => $userModel->userId,
                            'visitorId' => null,
                            'recipientName' => $userModel->firstName . ' ' . $userModel->lastName,
                            'apartmentNumber' => 1,
                            'buildingNumber' => 1,
                            'firstName' => $userModel->firstName,
                            'lastName' => $userModel->lastName,
                            'email' => $userModel->email,
                            'default' => true,
                        ]);

                        $this->info("add address for user id $userModel->userId in $address->location");

                    }

                } catch (\Exception $e) {
                    $this->error($e->getMessage());
                }


            });


        });
    }





    private function getPhoneFormatted($oldUser, PhoneNumberUtil $phoneUtil)
    {

        try {
            $swissNumberProto = $phoneUtil->parse($oldUser->mobile);
            $countryCode = $swissNumberProto->getCountryCode();
            $nationalNumber = $swissNumberProto->getNationalNumber();
            $regionCodeForNumber = $phoneUtil->getRegionCodeForNumber($swissNumberProto);

            return [
                'number' => (string) $nationalNumber,
                'code' => (string) $countryCode,
                'iso' => (string) $regionCodeForNumber,
                "hasError" => false,
            ];


        } catch (\libphonenumber\NumberParseException $e) {
            $this->error($e->getMessage() . " for $oldUser->mobile and user id $oldUser->id", 1);
            return [
                'number' => (string) $oldUser->mobile,
                'code' => (string) $oldUser->mobile,
                'iso' => (string) $oldUser->mobile,
                "hasError" => true
            ];
        }




    }
}