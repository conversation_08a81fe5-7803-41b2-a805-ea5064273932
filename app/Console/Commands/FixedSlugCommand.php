<?php

namespace App\Console\Commands;

use App\Helper\SlugHelper;
use App\Models\Attribute;
use App\Models\AttributesOption;
use App\Models\Category;
use App\Models\Product;
use App\Models\Variance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FixedSlugCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixed:slug';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        // $products = Product::all();

        // Product::query()->withTrashed()->update(['slug' => null]);

        // foreach ($products as $product) {
        //     $name = strtolower($product->name);
        //     $slug = Str::slug($name);
        //     $slug = preg_replace('/[_-]+/', '-', $slug);
        //     $slug = SlugHelper::slugChecker($slug, Product::getTableName());
        //     $this->info($slug);
        //     DB::table('products')
        //         ->where('productId', $product->productId)
        //         ->update(['slug' => $slug]);
        // }
        // //get all products truashed
        // $products = Product::onlyTrashed()->get();

        // foreach ($products as $product) {
        //     $name = strtolower($product->name);
        //     $slug = Str::slug($name);
        //     $slug = preg_replace('/[_-]+/', '-', $slug);
        //     $slug = SlugHelper::slugChecker($slug, Product::getTableName());
        //     $this->info($slug);
        //     DB::table('products')
        //         ->where('productId', $product->productId)
        //         ->update(['slug' => $slug]);
        // }



        $variances = Variance::all();
        Variance::query()->withTrashed()->update(['slug' => null]);
        foreach ($variances as $variance) {
            $name = "";

            $variance->attributesWithValue->each(function ($attribute) use (&$name) {
                $name .= "$attribute->name  $attribute->option_name ,";
            });

            $name = strtolower(trim($name));
            $slug = Str::slug($name);

            if ($slug === '') {
                $name = strtolower($variance->name);
                $slug = Str::slug($name);
            }

            $slug = preg_replace('/[_-]+/', '-', $slug);
            $slug = SlugHelper::slugCheckerWithinCondition($slug, Variance::getTableName(), ['productId' => $variance->productId]);

            $this->info($slug);
            DB::table('variances')
                ->where('varianceId', $variance->varianceId)
                ->update(['slug' => $slug]);
        }


        $variances = Variance::onlyTrashed()->get();

        foreach ($variances as $variance) {
            $name = strtolower($variance->name);
            $slug = Str::slug($name);
            $slug = preg_replace('/[_-]+/', '-', $slug);
            $slug = SlugHelper::slugChecker($slug, Variance::getTableName());
            $this->info($slug);
            DB::table('variances')
                ->where('varianceId', $variance->varianceId)
                ->update(['slug' => $slug]);
        }



        // $attributes = Attribute::all();
        // Attribute::query()->update(['slug' => null]);
        // foreach ($attributes as $attribute) {
        //     $name = strtolower($attribute->name);
        //     $slug = Str::slug($name);
        //     $slug = preg_replace('/[_-]+/', '-', $slug);
        //     $slug = SlugHelper::slugChecker($slug, Attribute::getTableName());
        //     $this->info($slug);
        //     DB::table('attributes')
        //         ->where('attributeId', $attribute->attributeId)
        //         ->update(['slug' => $slug]);
        // }



        // $attributes_options = AttributesOption::all();
        // AttributesOption::query()->update(['slug' => null]);
        // foreach ($attributes_options as $attributes_option) {
        //     $name = strtolower($attributes_option->name);
        //     $slug = Str::slug($name);
        //     $slug = SlugHelper::slugChecker($slug, AttributesOption::getTableName());
        //     $slug = preg_replace('/[_-]+/', '-', $slug);
        //     $this->info($slug);
        //     DB::table('attributes_options')
        //         ->where('attributeOptionId', $attributes_option->attributeOptionId)
        //         ->update(['slug' => $slug]);
        // }


        // $categories = Category::all();


        // Category::query()->update(['slug' => null]);
        // foreach ($categories as $category) {
        //     $name = strtolower($category->name);
        //     $slug = Str::slug($name);
        //     $slug = preg_replace('/[_-]+/', '-', $slug);
        //     $slug = SlugHelper::slugChecker($slug, Category::getTableName());
        //     $this->info($slug);
        //     DB::table('categories')
        //         ->where('categoryId', $category->categoryId)
        //         ->update(['slug' => $slug]);

        // }



    }

}