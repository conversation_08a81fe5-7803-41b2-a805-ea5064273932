<?php

namespace App\Console\Commands;

use App\Helper\SlugHelper;
use App\Models\Attribute;
use App\Models\AttributesOption;
use App\Models\Category;
use App\Models\Product;
use App\Models\Variance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ProductPricesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixed:prices';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $products = Product::withTrashed()->get();
        foreach ($products as $product) {
            $product->save();
            $product->touchWithCalculation();
        }

    }

}