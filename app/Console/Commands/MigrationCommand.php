<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class MigrationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migration:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        Artisan::call('import:auction');
        Artisan::call('import:orders');
        Artisan::call('import:users');
        Artisan::call('import:order-transaction');
        Artisan::call('import:wallet-transaction');
        Artisan::call('import:products');

        $this->info('setup seed complete successfully!!!');
    }
}
