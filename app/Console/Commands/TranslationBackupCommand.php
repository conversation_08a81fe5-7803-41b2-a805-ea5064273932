<?php

namespace App\Console\Commands;

use App\Enums\CacheKeysEnum;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class TranslationBackupCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translation:backup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Backup translation table to seeders';

    /**
     * Execute the console command.ye
     *
     * @return int
     */
    public function handle()
    {

        //make ./vendor/bin/sail artisan iseed language_has_translations


        $tables = 'language_has_translations,tag_has_translation_keys,translation_keys,translation_tags,translation_values';

        // Call the iseed command
        Artisan::call('iseed', ['tables' => $tables]);

        // Artisan::call('iseed', ['tables' => 'language_has_translations']);
        // Artisan::call('iseed', ['tables' => 'tag_has_translation_keys']);
        // Artisan::call('iseed', ['tables' => 'translation_keys']);
        // Artisan::call('iseed', ['tables' => 'translation_tags']);
        // Artisan::call('iseed', ['tables' => 'translation_values']);





        //logs the message on the console.
        $this->info('translation backup completed successfully !');

        return 0;
    }
}
