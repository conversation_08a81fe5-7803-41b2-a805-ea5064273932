<?php

namespace App\Console\Commands;

use App\Enums\ProductTypeEnum;
use App\Helper\SlugHelper;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixedImageProductSimpleCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixed:product_simple';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        dd('sss');
        $products = Product::where('type', ProductTypeEnum::simple->value)->get();

        foreach ($products as $product) {
            $migrationProduct = DB::table('migration_products')->select(['*'])->where('productId', $product->productId)->first();

            $itemColors = DB::connection('old')->table('items_images')->select('*')->where('item_id', $migrationProduct->oldProductId)->get();

            foreach ($itemColors as $itemColor) {
                try {
                    $img = "https://s3.eu-west-1.amazonaws.com/media.action.jo/images/$itemColor->image";
                    $this->saveMedia($product, $img, "gallery");
                } catch (\Exception $exception) {

                    $this->error($exception->getMessage());
                }

            }

            foreach ($itemColors as $itemColor) {
                try {
                    $img = "https://s3.eu-west-1.amazonaws.com/media.action.jo/images/$itemColor->image";
                    $this->saveMedia($product->variance, $img, "gallery");
                } catch (\Exception $exception) {

                    $this->error($exception->getMessage());
                }

            }

        }

    }



    public function saveMedia($model, $imagePath, $collection)
    {

        try {
            $model->addMediaFromUrl($imagePath)
                ->toMediaCollection($collection, env('MEDIA_DISK'));
        } catch (\Exception $exception) {

            $this->error($exception->getMessage());
        }

    }
}