<?php

namespace App\Console\Commands;

use App\Enums\ProductTypeEnum;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixedAttributesProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixed:product_attributes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        $products = Product::all();

        foreach ($products as $product) {

            if ($product->attributeValues->count() != 0) {
                $attributes = $product->attributeValues->pluck('attributeId', 'attributeOptionId', 'value', 'extra', 'productId')->toArray();

                $attributes = $product->attributeValues->map(function ($attribute) {
                    return ['attributeId' => $attribute->attributeId, 'attributeOptionId', $attribute->attributeOptionId, 'value' => $attribute->value, 'extra' => $attribute->extra];
                });

                $product->attributeValues()->sync($attributes);

                $product->productAttributes()->sync($product->attributeValues->map(function ($attribute) {
                    return ['attributeId' => $attribute->attributeId, 'attributeValuesId' => $attribute->attributeValuesId];
                }));
                $this->info($product->productId);
            }





        }

    }



}