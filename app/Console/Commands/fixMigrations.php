<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Str;

class fixMigrations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // dd( app_path()."\database\migrations");
        $path = base_path() . "/database/migrations";
        $files = array_filter(scandir($path), function ($file) {
            return !in_array($file, ['.', '..']);
        });
        $re = '/(?:(?:create|dropIfExists)\((?:\'|"))(.*)(?:\')/m';
        foreach ($files as $key => $file) {
            $fileContent = file_get_contents($path . '/' . $file);
            

            $re = '/\$table->enum\(\'(?<col>[a-z]+)\'\)->comment\("(?<arr>[a-z|\',]+)"\)/m';
    
            $subst = '$table->enum(\'$1\',[$2])';
            
            $fileContent = preg_replace($re, $subst, $fileContent);

            $subst = "\t\$table->timestamp('createdAt')->useCurrent();\n\t\t\t\$table->timestamp('updatedAt')->useCurrent();\n\t\t});";

            $fileContent = preg_replace('/\}\)\;/', $subst, $fileContent);

            // $fileContent =  preg_replace_callback($re, function ($match) {
            //     // var_dump($match);
            //     // $match[1] = '***';
            //     return str_replace('Pivot','',str_replace($match[1], Str::camel($match[1]), $match[0]));
            // }, $fileContent);

            file_put_contents($path . '/' . $file, $fileContent);
        
        }
        dd($files);
        return 0;
    }
}
