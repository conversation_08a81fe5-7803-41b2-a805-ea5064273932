<?php

namespace App\Console\Commands;

use App\Services\GoogleDriveService;
use Illuminate\Console\Command;

class ListSharedDrivesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'drive:list-shared';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List all shared drives accessible to the service account';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Fetching shared drives...');
        
        $driveService = new GoogleDriveService();
        $sharedDrives = $driveService->listSharedDrives();
        
        if (empty($sharedDrives)) {
            $this->warn('No shared drives found or error occurred.');
            $this->info('Make sure:');
            $this->info('1. You have created a shared drive in Google Drive');
            $this->info('2. You have added your service account email to the shared drive');
            $this->info('3. Service account email: <EMAIL>');
            return 1;
        }
        
        $this->info('Found ' . count($sharedDrives) . ' shared drive(s):');
        $this->newLine();
        
        $headers = ['Drive ID', 'Drive Name'];
        $this->table($headers, $sharedDrives);
        
        $this->newLine();
        $this->info('Copy the Drive ID you want to use and add it to your .env file:');
        $this->info('GOOGLE_DRIVE_SHARED_DRIVE_ID=your_drive_id_here');
        
        return 0;
    }
}
