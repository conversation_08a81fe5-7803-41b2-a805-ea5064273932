<?php

namespace App\Console\Commands;

use App\Helper\SlugHelper;
use App\Models\Category;
use App\Models\Page;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixedDataCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixed:data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        $categories = Category::all();

        foreach ($categories as $category) {
            $category->translatable = [];
            $slug = SlugHelper::makeSlug([
                'ar' => $category->name['ar'],
                'en' => $category->name['en'],
            ]);

            if (!$this->isUnique(slug: $slug, tableName: 'categories')) {
                $slug = [
                    'ar' => $slug['ar'] . '-' . rand(1, 100),
                    'en' => $slug['en'] . '-' . rand(1, 100),
                ];
            }


            $category->slug = $slug;
            $category->save();

        }

        $products = Product::all();
        foreach ($products as $product) {
            $product->productShippings()->sync([1]);
        }

    }



    public function isUnique(array $slug, string $tableName): bool
    {
        $count = DB::table($tableName)->select('*')
            ->orWhereJsonContains("slug->ar", $slug['ar'])
            ->orWhereJsonContains("slug->en", $slug['en'])
            ->count();
        return $count ? false : true;

    }
}