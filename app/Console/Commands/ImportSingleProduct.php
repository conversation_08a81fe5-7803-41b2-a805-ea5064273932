<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;

class ImportSingleProduct extends Command
{
    protected $signature = 'scout:import-single-product {productId}';
    protected $description = 'Import a single product into the search index';

    public function handle()
    {
        $productId = $this->argument('productId');
        $product = Product::where('productId', $productId)->first();

        if ($product) {
            $product->save();
            $product->searchable();
            $this->info("Product with ID {$productId} has been imported into the search index.");
        } else {
            $this->error("Product with ID {$productId} not found.");
        }
    }
}