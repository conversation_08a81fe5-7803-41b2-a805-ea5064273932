<?php

namespace App\Console\Commands;

use App\Helper\SlugHelper;
use App\Models\Product;
use App\Models\Variance;
use Illuminate\Console\Command;

class UpdateSlugCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:slug';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Product::withTrashed()->get()->each(function (Product $product) {
            $product->translatable = [];
            $slug = [
                'en' => $product->name['en'] . "  " . rand(9999, 99999),
                'ar' => $product->name['ar'] . "  " . rand(9999, 99999),
            ];
            $slug = SlugHelper::slugChecker($slug, Product::getTableName());
            $product->slug = $slug;
            $product->save();
        });

        Product::all()->each(function (Product $product) {
            $product->translatable = [];
            $slug = [
                'en' => $product->name['en'],
                'ar' => $product->name['ar'],
            ];
            $slug = SlugHelper::slugChecker($slug, Product::getTableName());
            $product->slug = $slug;
            $product->save();
        });


        Variance::withTrashed()->get()->each(function (Variance $variance) {

            $variance->translatable = [];
            $slug = [
                'en' => $variance->name['en'] . "  " . rand(9999, 99999),
                'ar' => $variance->name['ar'] . "  " . rand(9999, 99999),
            ];
            $slug = SlugHelper::slugChecker($slug, Variance::getTableName());
            $variance->slug = $slug;
            $variance->save();
        });

        Variance::all()->each(function (Variance $variance) {
            $variance->translatable = [];
            $slug = [
                'en' => $variance->name['en'],
                'ar' => $variance->name['ar'],
            ];
            $slug = SlugHelper::slugChecker($slug, Variance::getTableName());
            $variance->slug = $slug;
            $variance->save();
        });






    }
}