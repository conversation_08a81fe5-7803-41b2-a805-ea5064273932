<?php

namespace App\Console\Commands;

use App\Models\Product;
use Illuminate\Console\Command;

class SetShippingForAllProductCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'set:shipping-for-all-product  {shippingId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $shippingId = $this->argument('shippingId');
        $this->info($shippingId);

        $products = Product::select('productId')->get();
        foreach ($products as $product) {
            $product->productShippings()->sync(['shippingCarrierId' => $shippingId]);
        }

    }
}