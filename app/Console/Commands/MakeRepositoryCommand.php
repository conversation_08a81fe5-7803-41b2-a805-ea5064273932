<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class MakeRepositoryCommand extends Command
{
    public $class;

    public $namespace;

    public $directoryPath;

    public $modelClass;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:repo {class}';

    /**
     * @var bool
     */
    protected $hidden = true;

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Make repository command';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->class = $this->argument('class');
        $this->namespace = ucwords($this->class);
        $this->directoryPath = 'App/Repositories/' . $this->namespace;

        try {
            $this->modelClass = resolve("App\Models\\$this->class");
        } catch (\Exception $exception) {
            $this->newLine();
            $this->warn("The class model for ($this->class) not exists ! ");
            $this->newLine();
        }

        if (File::exists($this->directoryPath)) {
            $this->error('File directory already exists !');
        } else {
            if (File::makeDirectory($this->directoryPath)) {
                $this->createRepository();
                $this->createInterface();
            }

            $this->info('Successfully create Repository !');
        }
    }

    private function createRepository()
    {
        $data = $this->getStubContents(base_path('stubs/repository.stub'), 'Repository');

        $filePath = $this->directoryPath . '/' . $this->class . 'Repository.php';

        if (!File::put($filePath, $data)) {
            $this->error('There is error when create repository !');
        }
    }

    public function createInterface()
    {
        $data = $this->getStubContents(base_path('stubs/repository-interface.stub'), 'RepositoryInterface');

        $filePath = $this->directoryPath . '/' . $this->class . 'RepositoryInterface.php';

        if (!File::put($filePath, $data)) {
            $this->error('There is error when create interface !');
        }
    }

    private function getStubContents($stub, $className): array|bool|string
    {
        $variables = [
            'class' => ucwords($this->class . $className),
            'namespace' => str_replace('/', '\\', $this->directoryPath),
            'interface' => ucwords($this->class . $className) . 'Interface',
            'modelClass' => "$this->class::class",
            'modelClassImport' => $this->modelClass ? $this->modelClass::class : null,
        ];

        $contents = file_get_contents($stub);

        foreach ($variables as $search => $replace) {
            if (is_array($replace)) {
                $contents = strtr($contents, [
                    '{{' . $search . '}}' => var_export($replace, true),
                ]);
            } else {
                $contents = str_replace('{{ ' . $search . ' }}', $replace, $contents);
            }
        }

        return $contents;
    }
}
