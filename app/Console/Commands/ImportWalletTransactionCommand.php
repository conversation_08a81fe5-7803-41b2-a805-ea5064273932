<?php

namespace App\Console\Commands;

// use App\Enums\OrderPaymentStatusEnum;
// use App\Enums\OrderStatusEnum;
// use App\Enums\PaymentMethodEnum;
// use App\Enums\TransactionStatusEnum;
// use App\Models\Address;
// use App\Models\MigrationAuction;
// use App\Models\MigrationOrder;
// use App\Models\Order;
// use App\Models\ShippingCarriers;
// use App\Models\Transaction;
// use App\PaymentMethod\ArabBank;
// use App\PaymentMethod\HyperPay;
use App\Repositories\PaymentMethod\PaymentMethodRepositoryInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportWalletTransactionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:wallet-transaction';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        DB::connection('old')->table('wallet_transactions')
            ->orderBy('wallet_transactions.id', 'asc')
            ->chunk(100, function ($transactions) {

                foreach ($transactions as $transaction) {
                    DB::table('migration_wallet_transactions')->insert([
                        "id" => $transaction->id,
                        'user_id' => $transaction->user_id,
                        'type' => $transaction->type,
                        'amount' => $transaction->amount,
                        'reference_id' => $transaction->reference_id,
                        "reference_type" => $transaction->reference_type,
                        "status" => $transaction->status,
                        "checkout_id" => $transaction->checkout_id,
                        "provider_response" => $transaction->provider_response,
                        "created_at" => $transaction->created_at,
                        "info" => $transaction->info,
                        "device" => $transaction->device,
                        "deleted_at" => $transaction->deleted_at
                    ]);
                }

            });



    }
}
