<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;

class SetNumberOfOrderForProductCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'set-number-of-order-for-product:all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $orders = Order::with('orderItems')->get();
        $totalOrders = $orders->count();

        if ($totalOrders === 0) {
            $this->info('No orders found.');
            return 0;
        }

        $this->info("Processing {$totalOrders} orders...");
        $progressBar = $this->output->createProgressBar($totalOrders);
        $progressBar->start();

        foreach ($orders as $order) {
            foreach ($order->orderItems as $orderItem) {
                $orderItem?->model?->product?->increment('numberOfOrder');
            }
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->info('Command completed successfully!');

        return 0;
    }
}