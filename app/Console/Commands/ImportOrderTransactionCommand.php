<?php

namespace App\Console\Commands;

// use App\Enums\OrderPaymentStatusEnum;
// use App\Enums\OrderStatusEnum;
// use App\Enums\PaymentMethodEnum;
// use App\Enums\TransactionStatusEnum;
// use App\Models\Address;
// use App\Models\MigrationOrder;
// use App\Models\Order;
// use App\Models\ShippingCarriers;
// use App\Models\Transaction;
// use App\PaymentMethod\ArabBank;
// use App\PaymentMethod\HyperPay;
// use App\Repositories\PaymentMethod\PaymentMethodRepositoryInterface;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportOrderTransactionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:order-transaction';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        DB::table('migration_transactions')->truncate();
        DB::connection('old')->table('transactions')->orderBy('transactions.id', 'asc')
            ->chunk(100, function ($transactions) {
                foreach ($transactions as $transaction) {
                    DB::table('migration_transactions')->insert([
                        "id" => $transaction->id,
                        'order_id' => $transaction->order_id,
                        'provider_response' => $transaction->provider_response,
                        'checkout_id' => $transaction->checkout_id,
                        "gateway_transaction_id" => $transaction->gateway_transaction_id,
                        "status" => $transaction->status,
                        "provider_status" => $transaction->provider_status,
                        "created_at" => $transaction->created_at
                    ]);
                }
            });


    }
}
