<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupSeedCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'setup:seed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        Artisan::call('passport:install');
        Artisan::call('scout:sync-index-settings');
        Artisan::call('db:seed', ['--class' => 'CurrencySeeder']);
        Artisan::call('db:seed', ['--class' => 'LanguageSeeder']);
        Artisan::call('db:seed', ['--class' => 'TranslationTagSeeder']);
        Artisan::call('db:seed', ['--class' => 'CountrySeeder']);
        Artisan::call('db:seed', ['--class' => 'ShippingCarrierSeeder']);
        Artisan::call('db:seed', ['--class' => 'CitySeeder']);
        Artisan::call('db:seed', ['--class' => 'RoleSeeder']);
        // Artisan::call('db:seed', ['--class' => 'UserDefaultSeed']);
        Artisan::call('db:seed', ['--class' => 'PaymentsMethodSeeder']);
        Artisan::call('db:seed', ['--class' => 'AiTableSeeder']);
        Artisan::call('db:seed', ['--class' => 'PagesTableSeeder']);
        Artisan::call('db:seed', ['--class' => 'FilterGroupSeeder']);
        Artisan::call('translation:seeder');

        $this->info('setup seed complete successfully!!!');
    }
}
