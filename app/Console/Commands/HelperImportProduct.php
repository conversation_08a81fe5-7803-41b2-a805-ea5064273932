<?php

namespace App\Console\Commands;

use App\Enums\VarianceTypeEnum;
use App\Helper\SlugHelper;
use App\Models\Attribute;
use App\Models\AttributesValue;
use App\Models\Brand;
use App\Models\Category;
use App\Models\City;
use App\Models\Product;
use App\Models\ProductsAttribute;
use App\Models\ShippingCarrierPrice;
use App\Models\ShippingCarriers;
use App\Models\Stock;
use App\Models\Supplier;
use App\Models\VariancesAttribute;
use Exception;
use Illuminate\Support\Facades\DB;



trait HelperImportProduct
{


    public $allowDownloadImage = true;

    public $categoriesModels;

    public $brands;

    public $supplier;


    public $shippingCarrier;

    public $attributesMobile;

    public $attributesLaptops;

    public $attributeColor;


    public $attributeColorId;


    public $optionsColor;


    public function assignCategoryToAccessories($type, $product)
    {
        // Airpods ,Airpods Covers,Smart Watch,Headphones,Smart Watch Accessories,Phone Covers,Tablet Covers,Chargers,Car Chargers,Wireless Charger,
        // Power Bank,Earphone,Speaker,Aux Cable,Adapters,Google Products,Storage,Smart Home Products,Screen Protectors,Holders,Ring Light,
        // Others,Routers,Backpack,microphones,Cables

        try {
            switch (trim($type)) {
                case 'Airpods':
                    $product->categories()->sync([$this->categoriesModels[11]['categoryId'], $this->categoriesModels[9]['categoryId'], $this->categoriesModels[10]['categoryId']]);
                case 'Airpods Covers':
                    $product->categories()->sync([$this->categoriesModels[14]['categoryId'], $this->categoriesModels[9]['categoryId'], $this->categoriesModels[10]['categoryId']]);

                case 'Smart Watch':
                    $product->categories()->sync([$this->categoriesModels[12]['categoryId'], $this->categoriesModels[9]['categoryId']]);

                case 'Headphones':
                    $product->categories()->sync([$this->categoriesModels[32]['categoryId']]);
                case 'Smart Watch Accessories':
                    $product->categories()->sync([$this->categoriesModels[15]['categoryId'], $this->categoriesModels[13]['categoryId']]);


                case 'Phone Covers':
                    $product->categories()->sync([$this->categoriesModels[7]['categoryId'], $this->categoriesModels[4]['categoryId']]);

                case 'Tablet Covers':
                    // Handle Tablet Covers logic
                    $product->categories()->sync([$this->categoriesModels[16]['categoryId'], $this->categoriesModels[21]['categoryId']]);
                    return "You selected Tablet Covers.";

                case 'Chargers':
                    // Handle Chargers logic
                    $product->categories()->sync([$this->categoriesModels[23]['categoryId']]);

                    return "You selected Chargers.";

                case 'Car Chargers':
                    // Handle Car Chargers logic
                    $product->categories()->sync([$this->categoriesModels[23]['categoryId']]);
                    return "You selected Car Chargers.";

                case 'Wireless Charger':
                    // Handle Wireless Charger logic
                    $product->categories()->sync([$this->categoriesModels[25]['categoryId']]);
                    return "You selected Wireless Charger.";

                case 'Power Bank':
                    $product->categories()->sync([$this->categoriesModels[8]['categoryId'], $this->categoriesModels[4]['categoryId']]);



                case 'Earphone':
                    // Handle Earphone logic
                    $product->categories()->sync([$this->categoriesModels[22]['categoryId'], $this->categoriesModels[4]['categoryId']]);
                    return "You selected Earphone.";

                case 'Speaker':
                    // Handle Speaker logic
                    $product->categories()->sync([$this->categoriesModels[22]['categoryId']]);
                    return "You selected Speaker.";

                case 'Aux Cable':
                    $product->categories()->sync([$this->categoriesModels[27]['categoryId']]);


                case 'Adapters':
                    // Handle Adapters logic
                    $product->categories()->sync([$this->categoriesModels[22]['categoryId']]);

                    return "You selected Adapters.";

                case 'Google Products':
                    // Handle Google Products logic
                    $product->categories()->sync([$this->categoriesModels[22]['categoryId']]);
                    return "You selected Google Products.";

                case 'Storage':
                    // Handle Storage logic
                    return "You selected Storage.";

                case 'Smart Home Products':
                    // Handle Smart Home Products logic
                    $product->categories()->sync([$this->categoriesModels[104]['categoryId']]);
                    return "You selected Smart Home Products.";

                case 'Screen Protectors':
                    $product->categories()->sync([$this->categoriesModels[4]['categoryId'], $this->categoriesModels[22]['categoryId']]);



                case 'Holders':
                    // Handle Holders logic
                    $product->categories()->sync([$this->categoriesModels[22]['categoryId'], $this->categoriesModels[20]['categoryId']]);
                    return "You selected Holders.";

                case 'Ring Light':
                    $product->categories()->sync([$this->categoriesModels[60]['categoryId']]);


                case 'Others':
                    // Handle Others logic
                    $product->categories()->sync([$this->categoriesModels[22]['categoryId']]);
                    return "You selected Others.";

                case 'Routers':
                    // Handle Routers logic
                    $product->categories()->sync([$this->categoriesModels[63]['categoryId']]);
                    return "You selected Routers.";

                case 'Backpack':
                    // Handle Backpack logic
                    $product->categories()->sync([$this->categoriesModels[22]['categoryId']]);
                    return "You selected Backpack.";

                case 'Microphones':
                    // Handle Microphones logic
                    $product->categories()->sync([$this->categoriesModels[36]['categoryId']]);
                    return "You selected Microphones.";

                case 'Cables':
                    $product->categories()->sync([$this->categoriesModels[27]['categoryId']]);
                default:
                    $product->categories()->sync([$this->categoriesModels[22]['categoryId']]);
            }

        } catch (Exception $exception) {

            $this->error($exception->getMessage());
        }
    }




    public function saveMedia($model, $imagePath, $collection)
    {

        if ($this->allowDownloadImage) {
            try {
                $model->addMediaFromUrl($imagePath)
                    ->toMediaCollection($collection, env('MEDIA_DISK'));
            } catch (\Exception $exception) {

                $this->error($exception->getMessage());
            }
        }

    }



    public function attributeValueModel($model, $attributeOptionId)
    {


        try {
            //code...
            if ($model::class == Product::class) {

                $attributeValues = AttributesValue::create([
                    'attributeId' => $this->attributeColorId,
                    'attributeOptionId' => $attributeOptionId,
                    'productId' => $model->productId
                ]);


                ProductsAttribute::create([
                    'productId' => $model->productId,
                    'attributeId' => $this->attributeColorId,
                    "attributeValuesId" => $attributeValues->attributeValuesId, // id row in attribute value  for primraray key 

                ]);
                $this->info("ProductsAttribute  AttributesValue  create ");


            } else {

                $attributeValues = AttributesValue::create([
                    'attributeId' => $this->attributeColorId,
                    'attributeOptionId' => $attributeOptionId,
                    'varianceId' => $model->varianceId
                ]);

                VariancesAttribute::create([
                    'varianceId' => $model->varianceId,
                    'attributeId' => $this->attributeColorId,
                    "attributeValuesId" => $attributeValues->attributeValuesId, // id row in attribute value  for primraray key 

                ]);

                $this->info("attributeValues  VariancesAttribute  create ");
            }
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }







    }

    public function makeBrands(): array
    {
        $localization = new \ArPHP\I18N\Arabic();

        $brands = DB::connection('old')->table('brands')->select('*')->get();

        $arrayOfBrand = [];

        foreach ($brands as $key => $brand) {

            $name = [
                'en' => $brand->brand,
                'ar' => $localization->en2ar($brand->brand),
            ];

            $slug = SlugHelper::makeSlug($name);

            $modelBrand = Brand::updateOrCreate(
                [
                    'slug->en' => $slug['en'],
                ],
                [
                    'name' => $name,
                    'slug' => $slug,
                    'isPublished' => true,
                    'publishedAt' => null,
                    'UnPublishedAt' => null
                ]
            );

            $arrayOfBrand[$brand->id] = $modelBrand;

            $this->info("insert brand complete successfully ($key)");

            $img = "https://s3.eu-west-1.amazonaws.com/media.action.jo/brands/$brand->image";
            $this->saveMedia($modelBrand, $img, "logo");


            $img = "https://s3.eu-west-1.amazonaws.com/media.action.jo/brands/$brand->image";
            $this->saveMedia($modelBrand, $img, "logoName");


        }

        return $arrayOfBrand;
    }



    public function makeSupplier()
    {
        return Supplier::updateOrCreate(
            [
                'name->en' => 'Action Mobile',
            ],
            [
                'name' => [
                    'en' => 'Action Mobile',
                    'ar' => 'Action Mobile',
                ]
            ]
        );
    }



    public function makeProduct($item, $type)
    {

        $name = ['en' => $item->name, 'ar' => $item->name_ar];
        $slug = SlugHelper::makeSlug($name);
        if (!$this->isUnique(slug: $slug, tableName: 'products')) {
            $slug = [
                'ar' => $slug['ar'] . '-' . rand(1, 100),
                'en' => $slug['en'] . '-' . rand(1, 100),
            ];
        }
        $description = ['en' => $item->description_en, 'ar' => $item->description_ar];

        $product = Product::create([
            'name' => $name,
            'slug' => $slug,
            'description' => $description,
            'type' => $type,
            'isPublished' => true,
            'isListed' => false,
            'variationAttributes' => null,
            'releaseAt' => null,
            "brandId" => 69,
        ]);

        DB::table('migration_products')->insert([
            'productId' => $product->productId,
            'oldProductId' => $item->id,
            'oldSlug' => urlTitle($item->name, '-', true)
        ]);

        return $product;

    }

    public function makeCategoriesMaintenance($maintenance)
    {
        $categoriesMaintenance = [
            "lcd" => [
                "icon" => "lcd.svg",
                "name" => "LCD",
                "slug" => "lcd",
                "meta_tag_ar" => 'صيانة شاشات ايفون وصيانة شاشات ابل وصيانة شاشات هواوي',
                "meta_tag_en" => 'iPhone screen maintenance, Apple screen maintenance, Huawei screen maintenance.',
                "meta_desc_ar" => "ان عملية صيانة شاشات ايفون وسامسونج و هواوي في اكشن موبايل تتم على يد ابرع الفنيين لنضمن لكم الجودة والدقة في العمل كما ويتوفر شاشات ايفون وشاشات سامسونج اصلية وأخرى عالية الجودة",
                "meta_desc_en" => "The process of maintaining iPhone, Samsung and Huawei screens in Action WebSite is carried out by the most skilled technicians to ensure you the quality and accuracy in work. iPhone screens, original Samsung screens and other high-quality ones are available",
            ],
            "charge" => [
                "icon" => "charge.svg",
                "name" => "Charge",
                "slug" => "charge",
                "meta_desc_ar" => "في اكشن موبايل نقوم بعمل صيانة أجهزة ايفون وسامسونج و هواوي وصيانة شاشة الموبايل ونوفر لك الحصول على افضل سعر وخدمة
                صيانة تناسبك وكفالة معتمدة",
                "meta_desc_en" => "At Action WebSite, we repair iPhone, Samsung, Huawei, and mobile screen maintenance, and we provide you with the best
                price, maintenance service that suits you, and a certified warranty.",
            ],
            "camera" => [
                "icon" => "camera.svg",
                "name" => "Camera",
                "slug" => "camera",
                "meta_desc_ar" => "في اكشن موبايل نقوم بعمل صيانة أجهزة ايفون وسامسونج و هواوي وصيانة شاشة الموبايل ونوفر لك الحصول على افضل سعر وخدمة
                صيانة تناسبك وكفالة معتمدة",
                "meta_desc_en" => "At Action WebSite, we repair iPhone, Samsung, Huawei, and mobile screen maintenance, and we provide you with the best
                price, maintenance service that suits you, and a certified warranty.",
            ],
            "sounds" => [
                "icon" => "sounds.svg",
                "name" => "Sounds",
                "slug" => "sounds",
                "meta_desc_ar" => "في اكشن موبايل نقوم بعمل صيانة أجهزة ايفون وسامسونج و هواوي وصيانة شاشة الموبايل ونوفر لك الحصول على افضل سعر وخدمة
                صيانة تناسبك وكفالة معتمدة",
                "meta_desc_en" => "At Action WebSite, we repair iPhone, Samsung, Huawei, and mobile screen maintenance, and we provide you with the best
                price, maintenance service that suits you, and a certified warranty.",
            ],
            "chases" => [
                "icon" => "chases.svg",
                "name" => "Chases",
                "slug" => "chases",
                "meta_desc_ar" => "في اكشن موبايل نقوم بعمل صيانة أجهزة ايفون وسامسونج و هواوي وصيانة شاشة الموبايل ونوفر لك الحصول على افضل سعر وخدمة
                صيانة تناسبك وكفالة معتمدة",
                "meta_desc_en" => "At Action WebSite, we repair iPhone, Samsung, Huawei, and mobile screen maintenance, and we provide you with the best
                price, maintenance service that suits you, and a certified warranty.",
            ],
            "ic" => [
                "icon" => "back_cover.svg",
                "name" => "back cover",
                "slug" => "back-cover",
                "meta_desc_ar" => "في اكشن موبايل نقوم بعمل صيانة أجهزة ايفون وسامسونج و هواوي وصيانة شاشة الموبايل ونوفر لك الحصول على افضل سعر وخدمة
                صيانة تناسبك وكفالة معتمدة",
                "meta_desc_en" => "At Action WebSite, we repair iPhone, Samsung, Huawei, and mobile screen maintenance, and we provide you with the best
                price, maintenance service that suits you, and a certified warranty.",
            ],

            "Speakers" => [
                "icon" => "back_cover.svg",
                "name" => "Speakers",
                "slug" => "speakers",
                "meta_desc_ar" => "",
                "meta_desc_en" => "",
            ],
            "MIC" => [
                "icon" => "back_cover.svg",
                "name" => "MIC",
                "slug" => "mic",
                "meta_desc_ar" => "",
                "meta_desc_en" => "",
            ],
            "Back" => [
                "icon" => "back_cover.svg",
                "name" => "Back",
                "slug" => "back",
                "meta_desc_ar" => "",
                "meta_desc_en" => "",
            ],
            "Earphone" => [
                "icon" => "back_cover.svg",
                "name" => "Earphone",
                "slug" => "earphone",
                "meta_desc_ar" => "",
                "meta_desc_en" => "",
            ],
            "batteries" => [
                "icon" => "batteries.svg",
                "name" => "Batteries",
                "slug" => "batteries",
                "meta_desc_ar" => "في اكشن موبايل نقوم بعمل صيانة أجهزة ايفون وسامسونج و هواوي وصيانة شاشة الموبايل ونوفر لك الحصول على افضل سعر وخدمة
                صيانة تناسبك وكفالة معتمدة",
                "meta_desc_en" => "At Action WebSite, we repair iPhone, Samsung, Huawei, and mobile screen maintenance, and we provide you with the best
                price, maintenance service that suits you, and a certified warranty.",
            ],
            "other" => [
                "icon" => "others 2.svg",
                "name" => "Other",
                "slug" => "other",
                "meta_desc_ar" => "في اكشن موبايل نقوم بعمل صيانة أجهزة ايفون وسامسونج و هواوي وصيانة شاشة الموبايل ونوفر لك الحصول على افضل سعر وخدمة
                صيانة تناسبك وكفالة معتمدة",
                "meta_desc_en" => "At Action WebSite, we repair iPhone, Samsung, Huawei, and mobile screen maintenance, and we provide you with the best
                price, maintenance service that suits you, and a certified warranty.",
            ],
        ];

        $categories = [];
        $localization = new \ArPHP\I18N\Arabic();

        foreach ($categoriesMaintenance as $key => $category) {
            $name = [
                'en' => $category['name'],
                'ar' => $localization->en2ar($category['name'])
            ];

            $slug = [
                'en' => $category['slug'],
                'ar' => $localization->en2ar($category['slug'])
            ];

            $categoryModel = Category::updateOrCreate(
                [
                    'slug->en' => $slug['en'],
                ],
                [
                    'parentId' => $maintenance->categoryId,
                    "name" => $name,
                    "slug" => $slug,
                    'metaTitle' => $name,
                    'metaDescription' => ['ar' => $category['meta_desc_ar'], 'en' => $category['meta_desc_en']],
                ]
            );


            $this->info("insert category maintenance  successfully  " . $category['name']);

            $categories[$category['name']] = $categoryModel;
        }

        return $categories;

    }


    public function makeShippingCarrier()
    {

        $shippingCarriers = ShippingCarriers::updateOrCreate(
            [
                'slug->en' => 'drop-shop',
            ],
            [
                'name' => [
                    'en' => 'drop shop',
                    'ar' => 'drop shop',
                ],
                'slug' => [
                    'en' => 'drop-shop',
                    'ar' => 'دروب-شوب',
                ],
                'label' => [
                    'ar' => 'توصيل خلال ٢٤ ساعة',
                    'en' => 'drop shop',
                ],
                'phone' => [],
                'default' => true,
                'haveFastShipping' => false
            ]
        );


        foreach (City::all() as $city) {

            ShippingCarrierPrice::updateOrCreate(
                [
                    'cityId' => $city->cityId,
                    'shippingCarrierId' => $shippingCarriers->shippingCarrierId
                ],
                [
                    'price' => 5,
                    'cityId' => $city->cityId,
                    'shippingCarrierId' => $shippingCarriers->shippingCarrierId
                ]
            );
        }

        return $shippingCarriers;
    }

    public function isUnique(array $slug, string $tableName): bool
    {
        $count = DB::table($tableName)->select('*')
            ->orWhereJsonContains("slug->ar", $slug['ar'])
            ->orWhereJsonContains("slug->en", $slug['en'])
            ->count();
        return $count ? false : true;

    }






    public function makeAttributeColorWithOptions()
    {
        $colors = DB::connection('old')->table('items_colors')->select('*')->get();
        $localization = new \ArPHP\I18N\Arabic();
        $options = [];
        $attribute = Attribute::updateOrCreate(
            [
                'key' => 'color',
            ],
            [
                "name" => ['en' => 'color', 'ar' => 'اللون'],
                "slug" => ['en' => 'color', 'ar' => 'اللون'],
                "key" => 'color',
                "suffix" => null,
                'prefix' => null,
                'hasFilter' => 1,
                'isRequired' => 1,
                'type' => 'color',
            ]
        );
        $this->attributeColorId = $attribute->attributeId;
        $this->info("insert attribute color complete successfully");

        foreach ($colors as $key => $color) {
            $name = [
                'en' => $color->colorName,
                'ar' => $localization->en2ar($color->colorName)
            ];
            $slug = SlugHelper::makeSlug($name);

            $optionModel = $attribute->options()->updateOrCreate(
                [
                    'slug->en' => $slug['en'],
                ],
                [
                    "name" => $name,
                    "slug" => $slug,
                    "hexCode" => $color->color,
                    "number" => null
                ]
            );

            $options[$color->id] = $optionModel;



            $this->info("insert color complete successfully ($key)");
        }


        return [
            "attribute" => $attribute,
            "options" => $options,

        ];
    }



    public function makeVarianceModel($product, $item, $name, $slug)
    {


        if (!$this->isUnique(slug: $product->slug, tableName: 'variances')) {
            $slug = [
                'ar' => $slug['ar'] . '-' . rand(1, 100),
                'en' => $slug['en'] . '-' . rand(1, 100),
            ];
        }



        return $product->variance()->create([
            "name" => $product->name,
            "slug" => $slug,
            "brandId" => 69,
            'isDefault' => true,
            "SKU" => is_null($item->sku) ? random_int(1000, 1000000000) : $item->sku,
            "type" => VarianceTypeEnum::physical->value,
            "metaTitle" => ['en' => $item->meta_tag_en ?? $item->name, 'ar' => $item->meta_tag_ar ?? $item->name],
            "metaDescription" => ['en' => $item->meta_desc_en ?? null, 'ar' => $item->meta_desc_ar ?? null],
            'isPublished' => true,
            "publishedAt" => null,
            "unPublishedAt" => null,
            "auctionId" => null,
        ]);
    }


    public function makeStock($model, $item, $supplier)
    {


        $stockModel = Stock::create([
            'quantity' => $item->quantity,
            'maxPerUser' => 1,
            'publishedAt' => null,
            'unPublishedAt' => null,
            "isPublished" => true,
            'supplierId' => $supplier->supplierId,
            'sort' => 1,
            'isOffer' => false,
            'price' => $item->price,
            'cost' => $item->price,
            'priceBeforeOffer' => null,
        ]);

        $model->variancesStocks()->sync([$stockModel->stockId => ['varianceId' => $model->varianceId]]);
    }

}