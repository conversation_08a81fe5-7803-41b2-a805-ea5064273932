<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        DB::table('migration_orders')->truncate();

        DB::connection('old')->table('orders')->select('*')->orderBy('id', 'asc')->chunk(100, function ($oldOrders) {
            $newOrdersArray = [];
            foreach ($oldOrders as $order) {
                $newOrdersArray[] = [
                    'id' => $order->id,
                    'coupon' => (int) $order->coupon,
                    'city' => (string) $order->city,
                    'location' => (string) $order->location,
                    'delivery_cost' => (float) $order->delivery_cost,
                    'latitude' => (float) $order->latitude,
                    'longitude' => (float) $order->longitude,
                    'payment_method' => (int) $order->payment_method,
                    'cc_name' => $order->cc_name,
                    'cc_number' => $order->cc_number,
                    'cc_expiry_month' => $order->cc_expiry_month,
                    'cc_expiry_year' => $order->cc_expiry_year,
                    'total' => (float) $order->total,
                    'status' => (string) $order->status == "" || is_null($order->status) ? "unpaid" : $order->status,
                    'reason' => (string) $order->reason,
                    'user_id' => (int) $order->user_id,
                    'move_to_delivary' => (float) $order->move_to_delivary,
                    'payment_gateway_id' => (int) $order->payment_gateway_id,
                    'voucher_id' => $order->voucher_id,
                    'note' => $order->note,
                    'created_at' => $order->created_at,
                    'updated_at' => $order->updated_at,

                ];


            }

            DB::table('migration_orders')->insert($newOrdersArray);

        });


        DB::table('migration_order_items')->truncate();
        DB::connection('old')->table('order_items')->select('*')->orderBy('id', 'asc')->chunk(100, function ($orderItems) {
            $newOrderItemsArray = [];
            foreach ($orderItems as $item) {
                $newOrderItemsArray[] = [
                    'id' => $item->id,
                    'order_id' => $item->order_id,
                    'snapshot' => $item->snapshot,
                    'coupon_discount' => $item->coupon_discount,
                    'quantity' => (int) $item->quantity,
                    'used_points' => $item->used_points,
                    'price' => $item->price,
                    'original_price' => $item->original_price,
                    'color_id' => $item->color_id,
                    'item_id' => $item->item_id,
                    'lock_until' => $item->lock_until,
                    'reason' => $item->reason,
                    'status' => (string) $item->status == "" || is_null($item->status) ? "failed" : $item->status,
                    'created_at' => $item->created_at,

                ];

            }

            DB::table('migration_order_items')->insert($newOrderItemsArray);

        });
    }
}
