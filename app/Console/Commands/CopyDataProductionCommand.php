<?php

namespace App\Console\Commands;


use App\Models\AttributesOption;
use App\Models\Product;
use App\Models\User;
use App\Models\Variance;
use Attribute;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CopyDataProductionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'copy-data:production  {endFrom?}';

    /**
     * The console command description.
     * @var string
     */
    protected $description = 'Command description';

    /**
     * The tables that should be not insert data
     * @var array
     */
    protected $expectedTables = [
        'activity_log',
        'telescope_entries',
        'telescope_entries_tags',
        'telescope_monitoring',
        'jobs',
        'oauth_access_tokens',
    ];
    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $endFrom = $this->argument('endFrom') ?? '';

        DB::statement('SET FOREIGN_KEY_CHECKS=0;'); // Disable foreign key checks
        $host = Schema::connection('mysql')->getConnection()->getConfig()['host'];

        if ((App::environment('production') || !in_array($host, ['mysql', 'localhost', '127.0.0.1']))) {
            $this->info("environment production can't copy");
            exit;
        }

        $tables = Schema::connection('mysql')->getConnection()->getDoctrineSchemaManager()->listTableNames();

        $startTruncate = $endFrom == '' ? true : false;

        foreach ($tables as $table) {

            if ($startTruncate) {
                DB::table($table)->truncate();
                $this->info("truncate $table");
            } elseif ($table == $endFrom) {
                $startTruncate = true;
                DB::table($table)->truncate();
                $this->info("truncate $table");
            }

        }

        $tables = Schema::connection('production')->getConnection()->getDoctrineSchemaManager()->listTableNames();
        $startTruncate = $endFrom == '' ? true : false;

        foreach ($tables as $table) {
            // Fetch all data from each table in the source connection

            if (!in_array($table, $this->expectedTables)) {

                if ($startTruncate) {

                    $primaryKey = DB::connection('production')
                        ->select("SHOW KEYS FROM $table WHERE Key_name = 'PRIMARY'");
                    if (!empty($primaryKey)) {
                        $primaryKey = $primaryKey[0]->Column_name;
                    } else {
                        $firstColumn = DB::connection('production')
                            ->select("SHOW COLUMNS FROM  $table");
                        if (is_array($firstColumn)) {
                            $primaryKey = $firstColumn[0]->Field;
                        } else {
                            $primaryKey = 'createdAt';
                        }

                    }


                    DB::connection('production')->table($table)->orderBy("$primaryKey", 'ASC')->chunk(300, function ($rows) use ($table) {
                        // For each chunk of rows, insert the data into the destination database
                        $dataToInsert = [];

                        foreach ($rows as $row) {
                            $dataToInsert[] = (array) $row;
                        }

                        if (!empty($dataToInsert)) {
                            DB::table($table)->insert($dataToInsert);
                            $this->info("insert to table $table");
                        }
                    });

                } elseif ($table == $endFrom) {
                    $startTruncate = true;

                    $primaryKey = DB::connection('production')
                        ->select("SHOW KEYS FROM $table WHERE Key_name = 'PRIMARY'");
                    if (!empty($primaryKey)) {
                        $primaryKey = $primaryKey[0]->Column_name;
                    } else {
                        $primaryKey = null;
                    }

                    DB::connection('production')->table($table)->orderBy("$primaryKey", 'ASC')->chunk(300, function ($rows) use ($table) {
                        // For each chunk of rows, insert the data into the destination database
                        $dataToInsert = [];

                        foreach ($rows as $row) {
                            $dataToInsert[] = (array) $row;
                        }

                        if (!empty($dataToInsert)) {
                            DB::table($table)->insert($dataToInsert);
                            $this->info("insert to table $table");
                        }
                    });
                }

            }


        }

        DB::statement('SET FOREIGN_KEY_CHECKS=1;');


    }


}