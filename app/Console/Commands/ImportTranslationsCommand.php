<?php

namespace App\Console\Commands;

use App\Models\Language;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;

class ImportTranslationsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:trans';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $translationFiles = collect(File::glob('lang/ar/auth.php'));

        $result = [];

        foreach ($translationFiles as $translationFile) {
            $data = [];
            $translationFilePath = $translationFile;
            $translationFile = str($translationFile)->explode('/')->toArray();
            $languageId = Language::query()->where('abbreviation' , head(array_slice($translationFile, 1, 1)))->value('id');
            $group = str(head(array_slice($translationFile, 2, 2)))->replaceLast('.php', '')->toString();
            $keys = include($translationFilePath);
            $data['group'] = $group;

            foreach ($keys as $key => $value) {
                $data['key'] = $key;
                $data['value'] = $value;
                $data['language_id'] = $languageId;
            }

            $result[] = $data;
        }

        dd($result);
    }
}
