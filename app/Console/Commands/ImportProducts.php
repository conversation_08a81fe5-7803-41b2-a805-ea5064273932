<?php

namespace App\Console\Commands;

use App\Enums\ComponentTypeEnum;
use App\Enums\FilterTypeEnum;
use App\Helper\SlugHelper;
use App\Helper\SortableHelper;
use App\Models\Attribute;
use App\Models\AttributesOption;
use App\Models\AttributesValue;
use App\Models\Category;
use App\Models\FilterGroup;
use App\Models\Filters;
use App\Models\Product;
use App\Models\ProductsAttribute;

use App\Models\VariancesAttribute;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;

class ImportProducts extends Command
{

    use HelperImportProduct;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:products';


    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';





    /**
     * this for expedited Categories for accessories
     */
    private $expeditedCategories = [];




    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        dd('sss');
        DB::table('variances_stocks')->truncate();
        DB::table('categories_attributes')->truncate();
        DB::table('products_attributes')->truncate();
        DB::table('variances_attributes')->truncate();
        DB::table('categories_brands')->truncate();
        DB::table('categories_product')->truncate();
        DB::table('features_attribute_options')->truncate();
        DB::table('media')->truncate();
        DB::table('variances_attributes')->delete();
        DB::table('attributes_values')->delete();
        DB::table('attributes_options')->delete();
        DB::table('brands')->delete();
        DB::table('attributes')->delete();
        DB::table('products')->delete();
        DB::table('variances')->delete();
        DB::table('categories')->delete();

        Artisan::call('scout:flush', [
            'model' => 'App\\Models\\Variance'
        ]);

        Artisan::call('scout:flush', [
            'model' => 'App\\Models\\Category'
        ]);

        $this->brands = $this->makeBrands();
        $this->supplier = $this->makeSupplier();
        $this->attributeColor = $this->makeAttributeColorWithOptions();
        $this->shippingCarrier = $this->makeShippingCarrier();
        $categories = $this->makeCategories();

        $this->makeAttributesAndOptions(featuresIndex: ['Mobile Features', 'Laptops'], expedited: ['type']);

        // make all categories

        $maintenance = $categories['maintenance'];
        $this->categoriesModels = $categories['categories'];

        $categoriesMaintenance = $this->makeCategoriesMaintenance($maintenance);


        // 1 is mobileAndTablets 
        $this->importProducts(maintenance: null, categoryId: 1);


        // 4 is accessories 
        $this->importProducts(maintenance: null, categoryId: 4);

        // 9 is pcAndLaptops 
        $this->importProducts(maintenance: null, categoryId: 9);

        // 12 is homeAppliances 
        $this->importProducts(maintenance: null, categoryId: 12);

        // 16 is smartHomeProduct 
        $this->importProducts(maintenance: null, categoryId: 16);

        // 15 is entertainmentAndToys 
        $this->importProducts(maintenance: null, categoryId: 15);

        // 11 is cameras 
        $this->importProducts(maintenance: null, categoryId: 22);

        // 6 is Maintenance
        $this->importProducts(maintenance: $maintenance, categoryId: 6, categoriesMaintenance: $categoriesMaintenance);


        $this->info('complete all products successfully!!!');
    }















    public function importProducts($maintenance, $categoryId, $categoriesMaintenance = [])
    {





        DB::connection('old')->table('inventory_items')->select('*')
            ->where('category', $categoryId)
            ->latest('id')
            ->whereNull('deleted_at')
            ->where('is_show', 1)
            ->where('move_to_new_website', 1)

            ->chunk(100, function ($items) use ($maintenance, $categoryId, $categoriesMaintenance) {
                $items->each(function ($item) use ($maintenance, $categoryId, $categoriesMaintenance) {

                    try {


                        $itemColorsGroup = DB::connection('old')->table('items_images')
                            ->select('color_id', DB::raw('COUNT(color_id) as color_count'))
                            ->where('item_id', $item->id)
                            ->groupBy('color_id')
                            ->get();

                        // number of colors and if category is maintenance set product simple 
                        $type = $itemColorsGroup->count() == 1 || $categoryId == 6 ? 'simple' : "alternative";

                        $product = $this->makeProduct($item, $type);



                        if ($categoryId == 6) { // 6 is Maintenance
    
                            //add sub category to product  
                            $fclist = DB::connection('old')->table('features_list')->select('*')->where('item_id', $item->id)->first();

                            if (!is_null($fclist)) {
                                try {
                                    $product->categories()->sync([
                                        $categoriesMaintenance[$fclist->value]->categoryId,
                                        $maintenance->categoryId
                                    ]);
                                } catch (\Exception $exception) {
                                    $product->categories()->sync([
                                        $maintenance->categoryId
                                    ]);

                                    $this->error($exception->getMessage());
                                }
                            }


                        } elseif ($categoryId == 1) {// 1 is mobileAndTablets 
                            // mobile 
                            if ($item->brand == 1) {
                                $product->categories()->sync([$this->categoriesModels[2]['categoryId']]);  // iOS
                            } else {
                                $product->categories()->sync([$this->categoriesModels[3]['categoryId']]); //Android
                            }
                        } elseif ($categoryId == 4) { // 4 is accessories 
                            $type = DB::connection('old')->table('features_list')->select('*')->where('item_id', $item->id)->where('feature_id', 23)->first();
                            if (!is_null($type)) {
                                $this->assignCategoryToAccessories($type->value, $product);
                            }
                        } elseif ($categoryId == 9) {   // 9 is pcAndLaptops 
    
                            if (
                                $item->brand == 1 && DB::connection('old')->table('features_list')
                                    ->where('item_id', $item->id)
                                    ->where('value', 'pc and laptops')->count() > 1
                            ) {

                                $lowercaseName = strtolower($product->name['en']);
                                if (str_contains($lowercaseName, 'macbook')) {
                                    $product->categories()->sync([$this->categoriesModels[44]['categoryId']]);
                                } elseif (str_contains($lowercaseName, 'imac')) {
                                    $product->categories()->sync([$this->categoriesModels[45]['categoryId']]);
                                }


                            } elseif (
                                DB::connection('old')->table('features_list')
                                    ->where('item_id', $item->id)
                                    ->where('value', 'mouse')->count()
                            ) {
                                $product->categories()->sync([$this->categoriesModels[49]['categoryId']]);
                            } elseif (
                                DB::connection('old')->table('features_list')
                                    ->where('item_id', $item->id)
                                    ->where('value', 'keyboard')->count()
                            ) {
                                $product->categories()->sync([$this->categoriesModels[51]['categoryId']]);
                            } else {

                                $product->categories()->sync([$this->categoriesModels[40]['categoryId']]);

                            }

                        } elseif ($categoryId == 12) {    // 12 is homeAppliances 
    
                            $product->categories()->sync([$this->categoriesModels[99]['categoryId']]);

                        } elseif ($categoryId == 16) {    // 16 is smartHomeProduct 
    
                            $product->categories()->sync([$this->categoriesModels[40]['categoryId']]);


                        } elseif ($categoryId == 15) {    // 15 is entertainmentAndToys 
    
                            $product->categories()->sync([$this->categoriesModels[109]['categoryId']]);

                        } elseif ($categoryId == 11) {    // 11 is cameras 
    
                            $product->categories()->sync([$this->categoriesModels[57]['categoryId']]);

                        }


                        // add shipping to product
                        $product->shippingCarriers()->sync(['shippingCarrierId' => $this->shippingCarrier->shippingCarrierId]);
                        $product->translatable = [];
                        $this->info("insert product " . $product->name['en'] . " complete with category  successfully");

                        if (in_array($categoryId, [1, 9])) {
                            //TODO add  attributes   'Mobile Features'
                            $this->importAttributeModel($product, $item, ['type']);

                        }

                        $img = "https://s3.eu-west-1.amazonaws.com/media.action.jo/images/$item->main_image";
                        $this->saveMedia($product, $img, "cover");



                        if ($categoryId !== 6) {
                            if ($itemColorsGroup->count() == 1) {
                                $varianceModel = $this->makeVarianceModel($product, $item, $product->name, $product->slug);

                                // if (in_array($categoryId, [1, 9])) {
                                //TODO add  attributes   'Mobile Features'    
                                //     $this->importAttributeModel($varianceModel, $item, ['type']);
                                // }
    
                                $this->makeStock($varianceModel, $item, $this->supplier);



                                $itemColors = DB::connection('old')->table('items_images')->select('*')->where('item_id', $item->id)->get();


                                foreach ($itemColors as $itemColor) {
                                    $img = "https://s3.eu-west-1.amazonaws.com/media.action.jo/images/$itemColor->image";
                                    $this->saveMedia($varianceModel, $img, "gallery");

                                }




                            } else {

                                foreach ($itemColorsGroup as $itemColor) {

                                    $this->attributeColor['options'][$itemColor->color_id]->translatable = [];

                                    $name = ['en' => "$item->name-" . $this->attributeColor['options'][$itemColor->color_id]->name['en'], 'ar' => "$item->name_ar-" . $this->attributeColor['options'][$itemColor->color_id]->name['ar']];


                                    $slug = SlugHelper::makeSlug($name);

                                    $this->info("make slug " . $slug['en']);

                                    $attributeOptionId = $this->attributeColor['options'][$itemColor->color_id]->attributeOptionId;


                                    $varianceModel = $this->makeVarianceModel($product, $item, $name, $slug);


                                    $this->attributeValueModel($varianceModel, $attributeOptionId);


                                    // if (in_array($categoryId, [1, 9])) {
                                    //     //TODO add  attributes   'Mobile Features'    
                                    //     $this->importAttributeModel($varianceModel, $item, ['type']);
                                    // }
    




                                    $itemColors = DB::connection('old')->table('items_images')
                                        ->select('*')
                                        ->where('color_id', $itemColor->color_id)
                                        ->where('item_id', $item->id)
                                        ->get();

                                    foreach ($itemColors as $color) {
                                        $img = "https://s3.eu-west-1.amazonaws.com/media.action.jo/images/$color->image";
                                        $this->saveMedia($varianceModel, $img, "gallery");
                                    }

                                    $this->makeStock($varianceModel, $item, $this->supplier);

                                }






                            }

                        } else {


                            $varianceModel = $this->makeVarianceModel($product, $item, $product->name, $product->slug);

                            $this->makeStock($varianceModel, $item, $this->supplier);

                        }


                    } catch (Exception $e) {

                        $this->error($e->getMessage());

                    }

                });

            });



    }



    public function importAttributeModel($model, $item, $expedited = [])
    {

        try {
            $featuresList = DB::connection('old')->table('features_list')->select('*')
                ->join('features', 'features.id', '=', 'features_list.feature_id')
                ->where('features_list.item_id', $item->id)
                ->whereNotIn('features.feature_name', $expedited)->get();

            $this->info("start import featuresList ");
            $localization = new \ArPHP\I18N\Arabic();

            $featuresList->each(function ($feature) use ($model, $item, $localization) {

                $featuresAttributeOptions = DB::table('features_attribute_options')
                    ->where('featureId', $feature->feature_id)
                    ->where('value', trim($feature->value))
                    ->first();

                $this->info("import feature $feature->value for item $item->id");

                if (!is_null($featuresAttributeOptions)) {
                    $this->info("featuresAttributeOptions not null");
                    // for product attribute
                    if ($model::class == Product::class) {

                        $attributeValues = AttributesValue::create([
                            'attributeId' => $featuresAttributeOptions->attributeId,
                            'attributeOptionId' => $featuresAttributeOptions->attributeOptionId,
                            'productId' => $model->productId
                        ]);


                        ProductsAttribute::create([
                            'productId' => $model->productId,
                            'attributeId' => $featuresAttributeOptions->attributeId,
                            "attributeValuesId" => $attributeValues->attributeValuesId, // id row in attribute value  for primraray key 

                        ]);

                        $this->info("featuresAttributeOptions create ");

                    } else {

                        $attributeValues = AttributesValue::create([
                            'attributeId' => $featuresAttributeOptions->attributeId,
                            'attributeOptionId' => $featuresAttributeOptions->attributeOptionId,
                            'varianceId' => $model->varianceId
                        ]);

                        VariancesAttribute::create([
                            'varianceId' => $model->varianceId,
                            'attributeId' => $featuresAttributeOptions->attributeId,
                            "attributeValuesId" => $attributeValues->attributeValuesId, // id row in attribute value  for primraray key 

                        ]);

                        $this->info("featuresAttributeOptions create ");
                    }

                } else {

                    $this->info("featuresAttributeOptions  null");
                    $this->info("create feature $feature->value as attribute for item $item->id");
                    try {
                        //code...
                        $attribute = DB::table('features_attribute_options')
                            ->where('featureId', $feature->feature_id)->first();



                        $option = trim($feature->value);

                        $name = [
                            'ar' => $localization->en2ar($option),
                            'en' => $option,
                        ];

                        $slug = [
                            'ar' => SlugHelper::makeSlugFromString($option),
                            'en' => SlugHelper::makeSlugFromString($option),
                        ];
                        $this->info("make slug option " . $slug['en'] . " for item $item->id");
                        $optionModel = AttributesOption::updateOrCreate([
                            'slug->en' => $slug['en'],
                            'attributeId' => $attribute->attributeId
                        ], [

                            "name" => $name,
                            "slug" => $slug,
                            "number" => is_numeric($option) ? $option : null,
                            'attributeId' => $attribute->attributeId,
                            'skipSlugGeneration' => true
                        ]);

                        DB::table('features_attribute_options')->insert([
                            'attributeId' => $attribute->attributeId,
                            'attributeOptionId' => $optionModel->attributeOptionId,
                            'featureId' => $feature->feature_id,
                            'value' => trim($feature->value)
                        ]);

                        if ($model::class == Product::class) {

                            $attributeValues = AttributesValue::create([
                                'attributeId' => $attribute->attributeId,
                                'attributeOptionId' => $optionModel->attributeOptionId,
                                'productId' => $model->productId
                            ]);


                            ProductsAttribute::create([
                                'productId' => $model->productId,
                                'attributeId' => $attribute->attributeId,
                                "attributeValuesId" => $attributeValues->attributeValuesId, // id row in attribute value  for primraray key 

                            ]);



                        } else {

                            $attributeValues = AttributesValue::create([
                                'attributeId' => $attribute->attributeId,
                                'attributeOptionId' => $optionModel->attributeOptionId,
                                'varianceId' => $model->varianceId
                            ]);

                            VariancesAttribute::create([
                                'varianceId' => $model->varianceId,
                                'attributeId' => $attribute->attributeId,
                                "attributeValuesId" => $attributeValues->attributeValuesId, // id row in attribute value  for primraray key 

                            ]);


                        }

                        $this->info("sussessfully create feature $feature->value as attribute for item $item->id");
                    } catch (\Exception $exception) {
                        $this->error($exception->getMessage());
                    }









                }




            });
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }


    }


    public function makeAttributesAndOptions($featuresIndex, $expedited = [])
    {

        $localization = new \ArPHP\I18N\Arabic();

        $features = DB::connection('old')->table('features')->whereIn('features_index', $featuresIndex)->whereNotIn('feature_name', $expedited)->select('*')->get();

        foreach ($features as $feature) {
            $feature_name = $feature->feature_name;
            $name = [
                'en' => $feature_name,
                'ar' => $localization->en2ar($feature_name)
            ];

            $slug = SlugHelper::makeSlug($name);


            if (!$this->isUnique(slug: $slug, tableName: 'attributes')) {
                $slug = [
                    'ar' => $slug['ar'] . '-' . rand(1, 100),
                    'en' => $slug['en'] . '-' . rand(1, 100),
                ];
            }

            $prefix = is_null($feature->prefix) ? null : [
                'en' => $feature->prefix,
                'ar' => $localization->en2ar($feature->prefix)
            ];
            $suffix = is_null($feature->suffix) ? [] : [
                'en' => $feature->suffix,
                'ar' => $localization->en2ar($feature->suffix)
            ];

            $key = strtolower(str_replace(' ', '', $feature_name));

            $options = explode(',', $feature->options);

            $attribute = Attribute::updateOrCreate(
                [
                    'slug->en' => $slug['en'],
                ],
                [
                    "name" => $name,
                    "slug" => $slug,
                    "key" => $key,
                    "suffix" => $suffix,
                    'prefix' => $prefix,
                    'hasFilter' => 1,
                    'isRequired' => 1,
                    'type' => $feature->options !== '' && is_array($options) && count($options) > 1 && is_numeric($options[0]) ? 'number' : 'checkbox',
                ]
            );



            if ($feature->features_index == 'Mobile Features') {
                $this->attributesMobile[] = $attribute;
            }
            if ($feature->features_index == 'Laptops') {
                $this->attributesLaptops[] = $attribute;
            }



            DB::table('features_attribute_options')->insert([
                'attributeId' => $attribute->attributeId,
                'attributeOptionId' => -1,
                'featureId' => $feature->id,
                'value' => "  "

            ]);



            if ($feature->options !== '') {
                foreach (explode(',', $feature->options) as $option) {
                    $option = trim($option);
                    $name = [
                        'en' => $option,
                        'ar' => $localization->en2ar($option)
                    ];
                    $slug = SlugHelper::makeSlug($name);

                    $optionModel = AttributesOption::updateOrCreate([
                        'slug->en' => $slug['en'],
                        'attributeId' => $attribute->attributeId
                    ], [

                        "name" => $name,
                        "slug" => $slug,
                        "number" => is_numeric($option) ? $option : null,
                        'attributeId' => $attribute->attributeId
                    ]);


                    DB::table('features_attribute_options')->insert([
                        'attributeId' => $attribute->attributeId,
                        'attributeOptionId' => $optionModel->attributeOptionId,
                        'featureId' => $feature->id,
                        'value' => $option

                    ]);




                }

            }






        }






    }




    public function makeCategories(): array
    {



        $filterGroup = FilterGroup::updateOrCreate(
            [

                'name->en' => 'global filters',

            ],
            [
                'name' => ["en" => "global filters", "ar" => "الفلترات"],
            ]
        );


        $filters = [
            [
                'filtersGroupsId' => $filterGroup->filtersGroupsId,
                'config' => ['max' => 10000, 'min' => 1],
                'componentType' => ComponentTypeEnum::price->value,
                'filterType' => FilterTypeEnum::price->value,
                'attributeId' => null,
                'name' => ['ar' => 'السعر', 'en' => "price"],
            ],
            [
                'filtersGroupsId' => $filterGroup->filtersGroupsId,
                'config' => ['max' => null, 'min' => null],
                'componentType' => ComponentTypeEnum::brand->value,
                'filterType' => FilterTypeEnum::brand->value,
                'attributeId' => null,
                'name' => ['ar' => 'الماركة', 'en' => "brand"],
            ]

        ];
        $filters = SortableHelper::sort($filters);

        foreach ($filters as $filter) {

            $filterModel = Filters::updateOrCreate([
                'filterType' => $filter['filterType'],
                'filtersGroupsId' => $filter['filtersGroupsId'],
            ], $filter);
        }



        $maintenance = Category::updateOrCreate(
            [
                'slug->en' => 'maintenance',
            ],
            [
                "name" => ["ar" => "صيانة", "en" => "maintenance"],
                "slug" => ["ar" => "صيانة", "en" => "maintenance"],
            ]
        );
        $this->info("make Category complete successfully!!!");

        $categories = [
            [
                "name" => ["ar" => "موبايل", "en" => "Mobile"],//0
                "slug" => ["ar" => "موبايل", "en" => "mobile"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "الأجهزة", "en" => "Devices"],//1
                        "slug" => ["ar" => "الأجهزة", "en" => "devices"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "ايفون", "en" => "iOS"],//2
                                "slug" => ["ar" => "ايفون", "en" => "ios"]
                            ],
                            [
                                "name" => ["ar" => "اندرويد", "en" => "Android"],//3
                                "slug" => ["ar" => "اندرويد", "en" => "android"]
                            ],
                        ]
                    ],
                    [
                        "name" => ["ar" => "الاكسسوارات", "en" => "accessories"], //4
                        "slug" => ["ar" => "الاكسسوارات", "en" => "accessories"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "شواحن", "en" => "Chargers"],//5
                                "slug" => ["ar" => "شواحن", "en" => "chargers"]
                            ],
                            [
                                "name" => ["ar" => "واقيات الشاشة", "en" => "Screen Protectors"],//6
                                "slug" => ["ar" => "واقيات-الشاشة", "en" => "screen-protectors"]
                            ],
                            [
                                "name" => ["ar" => "اغطية", "en" => "Cover"],//7
                                "slug" => ["ar" => "اغطية", "en" => "cover"]
                            ],
                            [
                                "name" => ["ar" => "بنوك الطاقة", "en" => "Power Banks"],//8
                                "slug" => ["ar" => "بنوك-الطاقة", "en" => "power-banks"]
                            ],
                        ]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الأجهزة القابلة للارتداء", "en" => "Wearables"],//9
                "slug" => ["ar" => "الأجهزة-القابلة-للارتداء", "en" => "wearables"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "الأجهزة", "en" => "Devices"],//10
                        "slug" => ["ar" => "الأجهزة", "en" => "devices"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "سماعات", "en" => "Earbuds"],//11
                                "slug" => ["ar" => "سماعات", "en" => "earbuds"]
                            ],
                            [
                                "name" => ["ar" => "الساعة الذكية", "en" => "Smart Watch"],//12
                                "slug" => ["ar" => "الساعة-الذكية", "en" => "smart-watch"]
                            ],
                        ]
                    ],
                    [
                        "name" => ["ar" => "الاكسسوارات", "en" => "accessories"],//13
                        "slug" => ["ar" => "الاكسسوارات", "en" => "accessories"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "اكسسوارات السماعات", "en" => "Earbuds Acc"],//14
                                "slug" => ["ar" => "اكسسوارات-السماعات", "en" => "earbuds-acc"]
                            ],
                            [
                                "name" => ["ar" => "اكسسوارات الساعة الذكية", "en" => "SW Acc"],//15
                                "slug" => ["ar" => "اكسسوارات-الساعة-الذكية", "en" => "sw-acc"]
                            ],
                        ]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الأجهزة اللوحية", "en" => "Tablets"],//16
                "slug" => ["ar" => "الأجهزة-اللوحية", "en" => "tablets"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "اكسسوارات اللوحية", "en" => "Tablet Acc"],//17
                        "slug" => ["ar" => "اكسسوارات-اللوحية", "en" => "tablet-acc"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "لوحات المفاتيح", "en" => "Keyboards"],//18
                                "slug" => ["ar" => "لوحات-المفاتيح", "en" => "keyboards"]
                            ],
                            [
                                "name" => ["ar" => "واقيات الشاشة", "en" => "Screen Protectors"],//19
                                "slug" => ["ar" => "واقيات-الشاشة", "en" => "screen-protectors"]
                            ],
                            [
                                "name" => ["ar" => "حاملات", "en" => "Holders"],//20
                                "slug" => ["ar" => "حاملات", "en" => "holders"]
                            ],
                            [
                                "name" => ["ar" => "أغطية", "en" => "Covers"],//21
                                "slug" => ["ar" => "أغطية", "en" => "covers"]
                            ],
                        ]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الاكسسوارات", "en" => "Accessories"],//22
                "slug" => ["ar" => "الاكسسوارات", "en" => "accessories"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "الشواحن", "en" => "Chargers"],//23
                        "slug" => ["ar" => "الشواحن", "en" => "chargers"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "الشاحن", "en" => "Charger"],//24
                                "slug" => ["ar" => "الشاحن", "en" => "charger"]
                            ],
                            [
                                "name" => ["ar" => "الشاحن اللاسلكي", "en" => "Wireless Charger"],//25
                                "slug" => ["ar" => "الشاحن-اللاسلكي", "en" => "wireless-charger"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "بنوك الطاقة", "en" => "Power Banks"],//26
                        "slug" => ["ar" => "بنوك-الطاقة", "en" => "power-banks"]
                    ],
                    [
                        "name" => ["ar" => "الكابلات", "en" => "Cables"],//27
                        "slug" => ["ar" => "الكابلات", "en" => "cables"]
                    ],
                    [
                        "name" => ["ar" => "مكبر الصوت", "en" => "Speaker"],// 28
                        "slug" => ["ar" => "مكبر-الصوت", "en" => "speaker"]
                    ],
                    [
                        "name" => ["ar" => "حاملات وستاندات", "en" => "Holders and Stands"],//29
                        "slug" => ["ar" => "حاملات-وستاندات", "en" => "holders-and-stands"]
                    ],
                    [
                        "name" => ["ar" => "حقائب الظهر", "en" => "Backpack"],//30
                        "slug" => ["ar" => "حقائب-الظهر", "en" => "backpack"]
                    ],
                    [
                        "name" => ["ar" => "محفظة", "en" => "Pouch"],//31
                        "slug" => ["ar" => "محفظة", "en" => "pouch"]
                    ],
                    [
                        "name" => ["ar" => "سماعات الرأس", "en" => "Headphones"],//32
                        "slug" => ["ar" => "سماعات-الرأس", "en" => "headphones"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "سماعات الألعاب", "en" => "Gaming Headphones"],//33
                                "slug" => ["ar" => "سماعات-الألعاب", "en" => "gaming-headphones"]
                            ],
                            [
                                "name" => ["ar" => "سماعات", "en" => "Headphones"],//34
                                "slug" => ["ar" => "سماعات", "en" => "headphones"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "التخزين", "en" => "Storage"],//35
                        "slug" => ["ar" => "التخزين", "en" => "storage"]
                    ],
                    [
                        "name" => ["ar" => "الميكروفونات", "en" => "Microphones"],//36
                        "slug" => ["ar" => "الميكروفونات", "en" => "microphones"]
                    ],
                    [
                        "name" => ["ar" => "البروجكتور", "en" => "Projectors"],//37
                        "slug" => ["ar" => "البروجكتور", "en" => "projectors"]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الكمبيوتر", "en" => "Computer"],//38
                "slug" => ["ar" => "الكمبيوتر", "en" => "computer"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "اللابتوب", "en" => "Laptop"],//39
                        "slug" => ["ar" => "اللابتوب", "en" => "laptop"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "لابتوب مكتبي", "en" => "Office Laptop"],//40
                                "slug" => ["ar" => "لابتوب-مكتبي", "en" => "office-laptop"]
                            ],
                            [
                                "name" => ["ar" => "لابتوب الألعاب", "en" => "Gaming Laptop"],//41
                                "slug" => ["ar" => "لابتوب-الألعاب", "en" => "gaming-laptop"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "الحاسوب", "en" => "PC"],//42
                        "slug" => ["ar" => "الحاسوب", "en" => "pc"]
                    ],
                    [
                        "name" => ["ar" => "ماك بوك", "en" => "MacBook"],//43
                        "slug" => ["ar" => "ماك-بوك", "en" => "macbook"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "ماك بوك", "en" => "MacBook"],//44
                                "slug" => ["ar" => "ماك-بوك", "en" => "macbook"]
                            ],
                            [
                                "name" => ["ar" => "آي ماك", "en" => "iMac"],//45
                                "slug" => ["ar" => "آي-ماك", "en" => "imac"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "المحيطات", "en" => "Peripherals"],//46
                        "slug" => ["ar" => "المحيطات", "en" => "peripherals"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "الفلاشات", "en" => "Flashes"],//47
                                "slug" => ["ar" => "الفلاشات", "en" => "flashes"]
                            ],
                            [
                                "name" => ["ar" => "الكابلات", "en" => "Cables"],//48
                                "slug" => ["ar" => "الكابلات", "en" => "cables"]
                            ],
                            [
                                "name" => ["ar" => "الفأرة", "en" => "Mouse"],//49
                                "slug" => ["ar" => "الفأرة", "en" => "mouse"]
                            ],
                            [
                                "name" => ["ar" => "الميكروفونات", "en" => "Mic"],//50
                                "slug" => ["ar" => "الميكروفونات", "en" => "mic"]
                            ],
                            [
                                "name" => ["ar" => "لوحات المفاتيح", "en" => "Keyboards"],//51
                                "slug" => ["ar" => "لوحات-المفاتيح", "en" => "keyboards"]
                            ],
                            [
                                "name" => ["ar" => "مكبرات الصوت", "en" => "Speakers"],//52
                                "slug" => ["ar" => "مكبرات-الصوت", "en" => "speakers"]
                            ],
                            [
                                "name" => ["ar" => "لوحة الماوس", "en" => "Mousepad"],//53
                                "slug" => ["ar" => "لوحة-الماوس", "en" => "mousepad"]
                            ],
                            [
                                "name" => ["ar" => "حقيبة ظهر اللابتوب", "en" => "Backpack Laptop"],//54
                                "slug" => ["ar" => "حقيبة-ظهر-اللابتوب", "en" => "backpack-laptop"]
                            ],
                            [
                                "name" => ["ar" => "الحامل", "en" => "Holder"],//55
                                "slug" => ["ar" => "الحامل", "en" => "holder"]
                            ],
                            [
                                "name" => ["ar" => "القرص الصلب", "en" => "SSD"],//56
                                "slug" => ["ar" => "القرص-الصلب", "en" => "ssd"]
                            ]
                        ]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الكاميرات", "en" => "Camera"],//57
                "slug" => ["ar" => "الكاميرات", "en" => "camera"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "كاميرات المراقبة", "en" => "Security Cam"],//58
                        "slug" => ["ar" => "كاميرات-المراقبة", "en" => "security-cam"]
                    ],
                    [
                        "name" => ["ar" => "التصوير الفوتوغرافي", "en" => "Photography"],//59
                        "slug" => ["ar" => "التصوير-الفوتوغرافي", "en" => "photography"]
                    ],
                    [
                        "name" => ["ar" => "اكسسوارات الكاميرات", "en" => "Camera Acc"],//60
                        "slug" => ["ar" => "اكسسوارات-الكاميرات", "en" => "camera-acc"]
                    ],
                    [
                        "name" => ["ar" => "كاميرات لوحة القيادة", "en" => "Dashboard Cams"],//61
                        "slug" => ["ar" => "كاميرات-لوحة-القيادة", "en" => "dashboard-cams"]
                    ],
                    [
                        "name" => ["ar" => "الطائرات بدون طيار", "en" => "Drones"],//62
                        "slug" => ["ar" => "الطائرات-بدون-طيار", "en" => "drones"]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الأجهزة المنزلية", "en" => "Home Appliances"],//63
                "slug" => ["ar" => "الأجهزة-المنزلية", "en" => "home-appliances"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "التلفاز", "en" => "TV"],//64
                        "slug" => ["ar" => "التلفاز", "en" => "tv"]
                    ],
                    [
                        "name" => ["ar" => "المستقبلات", "en" => "Receivers"],//65
                        "slug" => ["ar" => "المستقبلات", "en" => "receivers"]
                    ],
                    [
                        "name" => ["ar" => "معدات المطبخ", "en" => "Kitchen Equipments"],//66
                        "slug" => ["ar" => "معدات-المطبخ", "en" => "kitchen-equipments"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "الثلاجات", "en" => "Refrigerators"],//67
                                "slug" => ["ar" => "الثلاجات", "en" => "refrigerators"]
                            ],
                            [
                                "name" => ["ar" => "غسالات الصحون", "en" => "Dishwashers"],//68
                                "slug" => ["ar" => "غسالات-الصحون", "en" => "dishwashers"]
                            ],
                            [
                                "name" => ["ar" => "المواقد", "en" => "Stoves"],//69
                                "slug" => ["ar" => "المواقد", "en" => "stoves"]
                            ],
                            [
                                "name" => ["ar" => "الغسالات", "en" => "Washing Machine"],//70
                                "slug" => ["ar" => "الغسالات", "en" => "washing-machine"]
                            ],
                            [
                                "name" => ["ar" => "الأفران", "en" => "Ovens"],//71
                                "slug" => ["ar" => "الأفران", "en" => "ovens"]
                            ],
                            [
                                "name" => ["ar" => "الميكروويف", "en" => "Microwave"],//72
                                "slug" => ["ar" => "الميكروويف", "en" => "microwave"]
                            ],
                            [
                                "name" => ["ar" => "صانعة القهوة", "en" => "Coffee Maker"],//73
                                "slug" => ["ar" => "صانعة-القهوة", "en" => "coffee-maker"]
                            ],
                            [
                                "name" => ["ar" => "الغلاية", "en" => "Kettle"],//74
                                "slug" => ["ar" => "الغلاية", "en" => "kettle"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "معدات الطهي", "en" => "Cooking Equipments"],//75
                        "slug" => ["ar" => "معدات-الطهي", "en" => "cooking-equipments"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "الخلاط", "en" => "Blender"],//76
                                "slug" => ["ar" => "الخلاط", "en" => "blender"]
                            ],
                            [
                                "name" => ["ar" => "الخلاط الكهربائي", "en" => "Mixer"],//77
                                "slug" => ["ar" => "الخلاط-الكهربائي", "en" => "mixer"]
                            ],
                            [
                                "name" => ["ar" => "السكاكين", "en" => "Knives"],//78
                                "slug" => ["ar" => "السكاكين", "en" => "knives"]
                            ],
                            [
                                "name" => ["ar" => "ألواح التقطيع", "en" => "Cutting Board"],//79
                                "slug" => ["ar" => "ألواح-التقطيع", "en" => "cutting-board"]
                            ],
                            [
                                "name" => ["ar" => "القشارة", "en" => "Peeler"],//80
                                "slug" => ["ar" => "القشارة", "en" => "peeler"]
                            ],
                            [
                                "name" => ["ar" => "الخفاقات", "en" => "Whisks"],//81
                                "slug" => ["ar" => "الخفاقات", "en" => "whisks"]
                            ],
                            [
                                "name" => ["ar" => "الملاعق", "en" => "Spatulas"],//82
                                "slug" => ["ar" => "الملاعق", "en" => "spatulas"]
                            ],
                            [
                                "name" => ["ar" => "المقلاة الهوائية", "en" => "Air Fryer"],//83
                                "slug" => ["ar" => "المقلاة-الهوائية", "en" => "air-fryer"]
                            ],
                            [
                                "name" => ["ar" => "الشواية الكهربائية", "en" => "E-Grill"],//84
                                "slug" => ["ar" => "الشواية-الكهربائية", "en" => "e-grill"]
                            ],
                            [
                                "name" => ["ar" => "قدر الطبخ", "en" => "Cooker"],//85
                                "slug" => ["ar" => "قدر-الطبخ", "en" => "cooker"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "الأمن المنزلي", "en" => "Home Security"],//86
                        "slug" => ["ar" => "الأمن-المنزلي", "en" => "home-security"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "الكاميرات", "en" => "Camera"],//87
                                "slug" => ["ar" => "الكاميرات", "en" => "camera"]
                            ],
                            [
                                "name" => ["ar" => "الأمن", "en" => "Security"],//88
                                "slug" => ["ar" => "الأمن", "en" => "security"]
                            ],
                            [
                                "name" => ["ar" => "الأقفال", "en" => "Locks"],//89
                                "slug" => ["ar" => "الأقفال", "en" => "locks"]
                            ],
                            [
                                "name" => ["ar" => "الإنذار", "en" => "Alarm"],//90
                                "slug" => ["ar" => "الإنذار", "en" => "alarm"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "إكسسوارات المنزل", "en" => "Home Accessories"],//91
                        "slug" => ["ar" => "إكسسوارات-المنزل", "en" => "home-accessories"]
                    ],
                    [
                        "name" => ["ar" => "الكوي", "en" => "Ironing"],//92
                        "slug" => ["ar" => "الكوي", "en" => "ironing"]
                    ],
                    [
                        "name" => ["ar" => "مكيف الهواء", "en" => "AC"],//93
                        "slug" => ["ar" => "مكيف-الهواء", "en" => "ac"]
                    ],
                    [
                        "name" => ["ar" => "مكنسة كهربائية", "en" => "Vacuum Cleaner"],//94
                        "slug" => ["ar" => "مكنسة-كهربائية", "en" => "vacuum-cleaner"]
                    ],
                    [
                        "name" => ["ar" => "المراوح", "en" => "Fans"],//95
                        "slug" => ["ar" => "المراوح", "en" => "fans"]
                    ],
                    [
                        "name" => ["ar" => "السخانات", "en" => "Heater"],//96
                        "slug" => ["ar" => "السخانات", "en" => "heater"]
                    ],
                    [
                        "name" => ["ar" => "مجفف", "en" => "Dryer"],//97
                        "slug" => ["ar" => "مجفف", "en" => "dryer"]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الأجهزة الذكية المنزلية", "en" => "Smart HA"],//98
                "slug" => ["ar" => "الأجهزة-الذكية-المنزلية", "en" => "smart-ha"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "الأمن", "en" => "Security"],//99
                        "slug" => ["ar" => "الأمن", "en" => "security"]
                    ],
                    [
                        "name" => ["ar" => "الإنذارات", "en" => "Alarms"],//100
                        "slug" => ["ar" => "الإنذارات", "en" => "alarms"]
                    ],
                    [
                        "name" => ["ar" => "معدات التنظيف", "en" => "Cleaning Equipments"],//101
                        "slug" => ["ar" => "معدات-التنظيف", "en" => "cleaning-equipments"]
                    ],
                    [
                        "name" => ["ar" => "الأقفال الذكية", "en" => "Smart Lock"],//102
                        "slug" => ["ar" => "الأقفال-الذكية", "en" => "smart-lock"]
                    ],
                    [
                        "name" => ["ar" => "الإضاءة الذكية", "en" => "Smart Lights"],//103
                        "slug" => ["ar" => "الإضاءة-الذكية", "en" => "smart-lights"]
                    ],
                    [
                        "name" => ["ar" => "متحكمات المنزل الذكي", "en" => "Smart Home Controller"],//104
                        "slug" => ["ar" => "متحكمات-المنزل-الذكي", "en" => "smart-home-controller"]
                    ],
                    [
                        "name" => ["ar" => "الثرموستات الذكية", "en" => "Smart Thermostat"],//105
                        "slug" => ["ar" => "الثرموستات-الذكية", "en" => "smart-thermostat"]
                    ],
                    [
                        "name" => ["ar" => "القابس الذكي", "en" => "Smart Plug"],//106
                        "slug" => ["ar" => "القابس-الذكي", "en" => "smart-plug"]
                    ],
                    [
                        "name" => ["ar" => "محور المنزل الذكي", "en" => "Smart Home Hub"],//107
                        "slug" => ["ar" => "محور-المنزل-الذكي", "en" => "smart-home-hub"]
                    ],
                    [
                        "name" => ["ar" => "منقي الهواء", "en" => "Air Purifier"],//108
                        "slug" => ["ar" => "منقي-الهواء", "en" => "air-purifier"]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الترفيه والألعاب", "en" => "Entertainment & Toys"],//109
                "slug" => ["ar" => "الترفيه-والألعاب", "en" => "entertainment-toys"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "الوحدات", "en" => "Consoles"],//110
                        "slug" => ["ar" => "الوحدات", "en" => "consoles"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "PS5 / Xbox", "en" => "PS5 / Xbox"],//111
                                "slug" => ["ar" => "PS5-Xbox", "en" => "ps5-xbox"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "اكسسوارات الوحدات", "en" => "Consoles Acc"],//112
                        "slug" => ["ar" => "اكسسوارات-الوحدات", "en" => "consoles-acc"],
                        "subcategories" => [
                            [
                                "name" => ["ar" => "التحكم", "en" => "Controllers"],//113
                                "slug" => ["ar" => "التحكم", "en" => "controllers"]
                            ],
                            [
                                "name" => ["ar" => "محطة الشحن", "en" => "Charging Station"],//114
                                "slug" => ["ar" => "محطة-الشحن", "en" => "charging-station"]
                            ]
                        ]
                    ],
                    [
                        "name" => ["ar" => "التحكم", "en" => "Controllers"],//115
                        "slug" => ["ar" => "التحكم", "en" => "controllers"]
                    ],
                    [
                        "name" => ["ar" => "محطة الشحن", "en" => "Charging Station"],//116
                        "slug" => ["ar" => "محطة-الشحن", "en" => "charging-station"]
                    ]
                ]
            ],
            [
                "name" => ["ar" => "الدراجات الكهربائية", "en" => "E-Scooters"],//117
                "slug" => ["ar" => "الدراجات-الكهربائية", "en" => "e-scooters"]
            ],
            [
                "name" => ["ar" => "الواقع الافتراضي", "en" => "VR"],//118
                "slug" => ["ar" => "الواقع-الافتراضي", "en" => "vr"],
                "subcategories" => [
                    [
                        "name" => ["ar" => "Oculus", "en" => "Oculus"],//119
                        "slug" => ["ar" => "أوكولوس", "en" => "oculus"]
                    ],
                    [
                        "name" => ["ar" => "Vision", "en" => "Vision"],//120
                        "slug" => ["ar" => "فيجن", "en" => "vision"]
                    ]
                ]
            ]
        ];




        $categoryArray = [];
        foreach ($categories as $categoryData) {
            $mainCategory = Category::updateOrCreate(
                ['slug->en' => $categoryData['slug']['en']],
                [
                    "name" => $categoryData['name'],
                    "slug" => $categoryData['slug']
                ]
            );


            $mainCategory->categoriesBrands()->sync(collect($this->brands)->map(function ($brand) {
                return ['brandId' => $brand->brandId];
            }));



            if ($categoryData['slug']['en'] == 'mobile') {
                $mainCategory->categoriesAttributes()->sync(collect($this->attributesMobile)->map(function ($attribute) {
                    return ['attributeId' => $attribute->attributeId];
                }));
            }

            if ($categoryData['slug']['en'] == 'computer') {
                $mainCategory->categoriesAttributes()->sync(collect($this->attributesLaptops)->map(function ($attribute) {
                    return ['attributeId' => $attribute->attributeId];
                }));
            }


            $this->info("make Category  complete successfully!!!");


            $categoryArray[] = $mainCategory->toArray();


            if (!empty($categoryData['subcategories'])) {
                foreach ($categoryData['subcategories'] as $subCategoryData) {
                    $subCategoryData['slug'] = [
                        'ar' => $categoryData['slug']['ar'] . "-" . $subCategoryData['slug']['ar'],
                        'en' => $categoryData['slug']['en'] . "-" . $subCategoryData['slug']['en'],
                    ];
                    $subCategory = Category::updateOrCreate(
                        ['slug->en' => $subCategoryData['slug']['en']],
                        [
                            "name" => $subCategoryData['name'],
                            "slug" => $subCategoryData['slug'],
                            'parentId' => $mainCategory->categoryId,
                            'filtersGroupsId' => $filterGroup->filtersGroupsId,
                        ]
                    );



                    if ($categoryData['slug']['en'] == 'mobile' && $subCategoryData['slug']['en'] == 'devices') {
                        $subCategory->categoriesAttributes()->sync(collect($this->attributesLaptops)->map(function ($attribute) {
                            return ['attributeId' => $attribute->attributeId];
                        }));
                    }



                    if ($categoryData['slug']['en'] == 'computer' && $subCategoryData['slug']['en'] != 'peripherals') {
                        $mainCategory->categoriesAttributes()->sync(collect($this->attributesLaptops)->map(function ($attribute) {
                            return ['attributeId' => $attribute->attributeId];
                        }));
                    }



                    $subCategory->categoriesBrands()->sync(collect($this->brands)->map(function ($brand) {
                        return ['brandId' => $brand->brandId];
                    }));





                    $this->info("make Category complete successfully!!!");
                    $categoryArray[] = $subCategory->toArray();
                    if (!empty($subCategoryData['subcategories'])) {
                        foreach ($subCategoryData['subcategories'] as $nestedSubCategoryData) {

                            $nestedSubCategoryData['slug'] = [
                                'ar' => $categoryData['slug']['ar'] . "-" . $subCategoryData['slug']['ar'] . "-" . $nestedSubCategoryData['slug']['ar'],
                                'en' => $categoryData['slug']['en'] . "-" . $subCategoryData['slug']['en'] . "-" . $nestedSubCategoryData['slug']['en'],
                            ];
                            $nestedSubCategory = Category::updateOrCreate(
                                ['slug->en' => $nestedSubCategoryData['slug']['en']],
                                [
                                    "name" => $nestedSubCategoryData['name'],
                                    "slug" => $nestedSubCategoryData['slug'],
                                    'parentId' => $subCategory->categoryId,
                                    'filtersGroupsId' => $filterGroup->filtersGroupsId,
                                ]
                            );

                            $nestedSubCategory->categoriesBrands()->sync(collect($this->brands)->map(function ($brand) {
                                return ['brandId' => $brand->brandId];
                            }));


                            if ($categoryData['slug']['en'] == 'computer' && $subCategoryData['slug']['en'] != 'peripherals') {
                                $nestedSubCategory->categoriesAttributes()->sync(collect($this->attributesLaptops)->map(function ($attribute) {
                                    return ['attributeId' => $attribute->attributeId];
                                }));
                            }



                            if ($categoryData['slug']['en'] == 'mobile' && ($nestedSubCategory['slug']['en'] == 'ios' || $nestedSubCategory['slug']['en'] == 'android')) {
                                $nestedSubCategory->categoriesAttributes()->sync(collect($this->attributesLaptops)->map(function ($attribute) {
                                    return ['attributeId' => $attribute->attributeId];
                                }));

                            }

                            $this->info("make Category complete successfully!!!");
                            $categoryArray[] = $nestedSubCategory->toArray();
                        }
                    }
                }
            }
        }



        return [
            'maintenance' => $maintenance,
            'categories' => $categoryArray
        ];





    }



















}