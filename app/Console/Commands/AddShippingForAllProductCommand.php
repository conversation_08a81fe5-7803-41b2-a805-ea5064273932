<?php

namespace App\Console\Commands;

use App\Models\Product;
use Illuminate\Console\Command;

class AddShippingForAllProductCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:shipping-for-all-product  {shippingId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $shippingId = $this->argument('shippingId');
        $this->info(string: $shippingId);

        $products = Product::select('productId')->get();
        foreach ($products as $product) {
            // need add not sync
            $product->productShippings()->attach(['shippingCarrierId' => $shippingId]);
        }

    }
}