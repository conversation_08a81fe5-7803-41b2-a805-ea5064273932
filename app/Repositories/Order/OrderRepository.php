<?php

namespace App\Repositories\Order;

use App\Enums\DeliveryStatusEnum;
use App\Enums\StatusActivation;
use App\Enums\TransactionStatusEnum;
use App\Helper\Calculator;
use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;
use App\Enums\OrderStatusEnum;
use App\Enums\InvoicePaymentStatusEnum;
use App\Enums\InvoiceStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\PromotionTypeEnum;
use App\Enums\OrderItemStatusEnum;
use App\Models\OrderNote;
use App\Models\ShippingCarrierPrice;
use App\Models\User;
use App\Repositories\Cart\CartRepository;
use App\Repositories\Cart\CartRepositoryInterface;
use Illuminate\Support\Facades\DB;
use App\Repositories\BaseRepository;
use App\Enums\OrderPaymentStatusEnum;
use App\Enums\ProductTypeEnum;
use App\Helper\Currency;
use App\Models\OrderRating;
use App\Models\Price;
use Illuminate\Database\Eloquent\Model;
use App\PaymentMethod\PaymentGatewayFactory;
use App\Repositories\Order\OrderRepositoryInterface;
use App\Repositories\Promotion\PromotionRepositoryInterface;
use App\Repositories\PaymentMethod\PaymentMethodRepositoryInterface;
use App\Repositories\Variance\VarianceRepositoryInterface;
use Illuminate\Support\Carbon;
use App\Repositories\Address\AddressRepositoryInterface;
use App\Repositories\Transaction\TransactionRepositoryInterface;
use App\Repositories\ShippingCarrier\ShippingCarrierRepositoryInterface;
use App\Models\ProductOrderItem;
use App\Repositories\Product\ProductRepositoryInterface;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;

class OrderRepository extends BaseRepository implements OrderRepositoryInterface
{

    public $modelClass = Order::class;


    public $relationsWithOne = [
        'orderItems',
        'orderItems.model',
        'orderItems.model',
        'orderItems.model',
        'orderItems.model.product',
        'orderItems.model.product.media',
        'orderItems.model.product.variance',
        'invoices',
        'invoices.invoiceItems',
        'invoices.invoiceItems.orderItem',
        'invoices.invoiceItems.orderItem.model',
        'editors',
        'shippingCarrier',
        'user',
        'visitor',
        'address',
        'address.city',
        'paymentMethod',
        'deliveries',
        'notes',
    ];

    // public $editableOrderStatus = [
    //     OrderStatusEnum::draft->value,
    //     OrderStatusEnum::received->value,
    // ];
    public function __construct()
    {
        $this->editableOrderStatus = [
            OrderStatusEnum::draft->value,
            OrderStatusEnum::received->value,
        ];
    }

    private function checkEditing($order)
    {

        if ($order->status != OrderStatusEnum::editing->value) {
            throw new \Exception(__('order status must be editing'), Response::HTTP_LOCKED);
        } elseif (!$order->editors()->whereNull('endAt')->count()) {
            throw new \Exception(__('cannot add product no one is editing'), Response::HTTP_LOCKED);
        } elseif ($order->editors()->whereNull('endAt')->first()->userId != auth(GUARD_API)->user()->userId) {
            throw new \Exception(__('cannot add product some one is editing this order'), Response::HTTP_LOCKED);
        }

    }



    public function storeOrder(array $data): Model
    {

        $order = null;

        $shippingCarrierRepo = resolve(ShippingCarrierRepositoryInterface::class);

        if ($data['shippingCarrierId']) {
            $shippingCarrier = $shippingCarrierRepo->findById($data['shippingCarrierId']);
        } else {
            $shippingCarrier = $shippingCarrierRepo->defaultShippingCarrier();
        }


        $subTotal = 0;
        $items = collect($data['orderItems']);
        $varianceRep = resolve(VarianceRepositoryInterface::class);
        $items = collect($data['orderItems'])->map(function ($item) use ($varianceRep, &$subTotal) {
            $varianceModel = $varianceRep->query(['product', 'activeStock'])->findOrFail($item['varianceId']);
            $item['stockId'] = $varianceModel?->activeStock->stockId;
            $item['product'] = $varianceModel->product->toArray();
            $item['price'] = $varianceModel?->activeStock?->price ?? 0;
            $item['originalPrice'] = $varianceModel?->activeStock?->price ?? null;
            $price = (float) $varianceModel?->activeStock->price ?? 0;
            $item['variance'] = $varianceModel->toArray();
            $subTotal += $price * (int) $item['quantity'];
            return $item;
        });



        $address = resolve(AddressRepositoryInterface::class)->findById($data['addressId']);


        $shippingCarrierPrice = ShippingCarrierPrice::where('cityId', $address->cityId)
            ->where('shippingCarrierId', $shippingCarrier->shippingCarrierId)
            ->firstOrFail();



        $total = $subTotal + $shippingCarrierPrice->price ?? 0;

        $orderItemsSave = [];

        DB::transaction(function () use ($data, $items, &$order, &$orderItemsSave, $shippingCarrierPrice, &$subTotal, &$total) {


            $order = $this->store([
                'status' => OrderStatusEnum::received->value,
                'paymentStatus' => OrderPaymentStatusEnum::unPaid->value,
                'paymentMethodId' => $data['paymentMethodId'],
                'userId' => $data['userId'],
                'visitorId' => null,
                'total' => $total,
                'addressId' => $data['addressId'],
                'shippingPrice' => $shippingCarrierPrice->price,
                'shippingCarrierId' => $data['shippingCarrierId'],
                'tax' => null,
                "subTotal" => $subTotal,
            ]);



            $order = apply_filters('order.created', $order);

            $orderItems = $items->map(function ($item) use (&$order, &$orderItemsSave) {
                $item = apply_filters('order.before-prepare-order-item', $item);


                $snapshot['product'] = $item->product?->toArray() ?? [];
                $snapshot['variance'] = $item->variance?->toArray() ?? [];
                $snapshot['activeStock'] = $item->variance?->activeStock?->toArray() ?? [];
                $snapshot['media'] = $item->variance?->media?->toArray() ?? [];
                $snapshot['attributesWithValue'] = $item->variance?->attributesWithValue?->toArray() ?? [];

                $item = [
                    'productId' => $item['productId'],
                    'stockId' => $item['stockId'],
                    'varianceId' => $item['varianceId'],
                    'snapshot' => $snapshot,
                    'price' => $item['price'],
                    'originalPrice' => $item['originalPrice'],
                    'discount' => 0,
                    'status' => OrderItemStatusEnum::reseed->value,
                    'quantity' => $item['quantity'],

                ];


                $item = apply_filters('order.after-prepare-order-item', $item);


                $model = ProductOrderItem::create($item);

                $orderItemsSave[] = [
                    'orderid' => $order->orderId,
                    "model_type" => ProductOrderItem::class,
                    "model_id" => $model->productOrderItemId,
                    'cost' => $item['cost'] ?? 0,
                ];


                return $item;

            });


            OrderItem::insert($orderItemsSave);

            apply_filters('order.after-inserted-order-items', $orderItems);

        });


        return $order;


    }



    public function updateOrderAdmin(int|string $id, array $data): Model
    {

        $order = $this->findById($id);

        $this->checkEditing($order);


        $shippingCarrierRepo = resolve(ShippingCarrierRepositoryInterface::class);

        // if ($data['shippingCarrierId']) {
        //     $shippingCarrier = $shippingCarrierRepo->findById($data['shippingCarrierId']);
        // } else {
        //     $shippingCarrier = $shippingCarrierRepo->defaultShippingCarrier();
        // }
        $address = resolve(AddressRepositoryInterface::class)->findById($data['addressId']);
        $shippingCarrierPrice = ShippingCarrierPrice::where('cityId', $address->cityId)
            ->where('shippingCarrierId', $data['shippingCarrierId'])
            ->firstOrFail();



        $subTotal = $order->subTotal;


        $items = collect($data['orderItems']);
        $varianceRep = resolve(VarianceRepositoryInterface::class);
        $items = collect($data['orderItems'])->map(function ($item) use ($varianceRep, &$subTotal) {
            $varianceModel = $varianceRep->query(['product', 'activeStock'])->findOrFail($item['varianceId']);
            $item['stockId'] = $varianceModel?->activeStock->stockId;
            $item['product'] = $varianceModel->product->toArray();
            $item['price'] = $varianceModel?->activeStock?->price ?? 0;
            $item['originalPrice'] = $varianceModel?->activeStock->price ?? 0;
            $price = (float) $varianceModel?->activeStock->price ?? 0;
            $item['variance'] = $varianceModel->toArray();
            $item['activeStock'] = $varianceModel?->activeStock ?? [];
            $item['attributesWithValue'] = $varianceModel?->attributesWithValue?->toArray() ?? [];

            $subTotal += $price * (int) $item['quantity'];
            return $item;
        });



        $total = $subTotal + $shippingCarrierPrice->price ?? 0;



        $productOrderItemIds = $order->orderItems->map(function ($orderItem) {
            return $orderItem->model->productOrderItemId;
        })->toArray();



        DB::transaction(function () use ($id, $data, $items, &$order, $shippingCarrierPrice, &$subTotal, &$total, $productOrderItemIds) {




            $order = $this->update($id, [
                'status' => OrderStatusEnum::received->value,
                'paymentStatus' => OrderPaymentStatusEnum::unPaid->value,
                'paymentMethodId' => $data['paymentMethodId'],
                'userId' => $data['userId'],
                'visitorId' => null,
                'total' => $total,
                'addressId' => $data['addressId'],
                'shippingPrice' => $shippingCarrierPrice->price,
                'shippingCarrierId' => $data['shippingCarrierId'],
                'tax' => null,
                "subTotal" => $subTotal,
            ]);


            // !is_null($data['note']) ? $order->notes()->create(['note' => $data['note'], 'userId' => auth(GUARD_API)->user()->userId]) : '';


            $order = apply_filters('order.created', $order);

            $orderItems = $items->map(function ($item) use (&$order) {
                $item = apply_filters('order.before-prepare-order-item', $item);
                // $productOrderItemId = $item['productOrderItemId'] ?? null;

                $snapshot['product'] = $item['product'];
                $snapshot['variance'] = $item['variance'];
                $snapshot['activeStock'] = $item['activeStock'];
                $snapshot['media'] = $item['media'];
                $snapshot['attributesWithValue'] = $item['attributesWithValue'];

                $item = [

                    'productId' => $item['productId'],
                    'stockId' => $item['stockId'],
                    'varianceId' => $item['varianceId'],
                    'snapshot' => $snapshot,
                    'price' => $item['price'],
                    'originalPrice' => $item['originalPrice'],
                    'discount' => 0,
                    'status' => OrderItemStatusEnum::reseed->value,
                    'quantity' => $item['quantity'],

                ];


                $item = apply_filters('order.after-prepare-order-item', $item);

                $productOrderItem = ProductOrderItem::create($item);


                $productOrderItem->orderItem()->updateOrCreate(['orderId' => $order->orderId]);

                return $item;

            });

            ProductOrderItem::whereIn('productOrderItemId', $productOrderItemIds)->delete();
            OrderItem::whereIn('model_id', $productOrderItemIds)->where('model_type', ProductOrderItem::class)->delete();

            apply_filters('order.after-inserted-order-items', $orderItems);

        });


        return $order;



    }



    public function updateOrder(int|string $id, array $data): Model
    {

        $total = 0;
        $cost = 0;
        foreach ($data["orderItems"] as $item) {
            $total += (float) $item->stock->price->price * $item->quantity;
        }
        $subTotal = $total;
        $order = $this->update($id, [
            'status' => $data['status'],
            'paymentMethodId' => $data['paymentMethodId'],
            'userId' => $data['userId'],
            'addressId' => $data['addressId'],
            'shippingPrice' => $data['shippingPrice'],
            'shippingId' => $data['shippingId'],
            'subTotal' => $subTotal,
            'total' => $total,
            'cost' => $cost,
        ]);


        !is_null($data['note']) ? $order->notes()->create(['note' => $data['note'], 'userId' => auth(GUARD_API)->user()->userId]) : '';

        if ($data["orderItems"]) {
            foreach ($data["orderItems"] as $item) {
                $orderItem = OrderItem::where('orderId', $order->orderId)->first();
                $orderItem->update([
                    'orderId' => $order->orderId,
                    'snapshot' => $item->snapshot,
                    'discount' => $item->discount,
                    'tax' => $item->tax,
                    'status' => $item->status,
                    'quantity' => $item->quantity,
                    'price' => $item->price,
                    'originalPrice' => $item->originalPrice,
                ]);



            }
        }

        return $order;

    }
    public function getAllOrders()
    {

        return $this->getAllLatestPaginatedFiltered(with: [
            'user',
            'address',
            'orderItems',
            'orderItems.model',
            'orderItems.model.product',
            'orderItems.model.product.media',
            'shippingCarrier',
            'paymentMethod',
        ]);
    }

    public function findByIdOrder(int|string $id): mixed
    {
        $order = $this->findById(id: $id, with: [

            'orderItems',
            'orderItems.model',
            'orderItems.model',
            'orderItems.model',
            'orderItems.model.product' => function ($query) {
                $query->withTrashed();
            },
            'orderItems.model.product.media',
            'orderItems.model.product.variance' => function ($query) {
                $query->withTrashed();
            },
            'orderItems.model.variance' => function ($query) {
                $query->withTrashed();
            },
            'invoices',
            'invoices.invoiceItems',
            'invoices.invoiceItems.orderItem',
            'invoices.invoiceItems.orderItem.model',
            'editors',
            'shippingCarrier',
            'user',
            'visitor',
            'address',
            'address.city',
            'paymentMethod',
            'deliveries',
            'notes',
        ]);
        return $order;
    }

    public function deleteOrder(int|string $id): mixed
    {
        return $this->destroy($id);
    }


    public function getUserOrders()
    {

        return $this->query()->where('userId', auth(GUARD_API)->user()->userId)->with([
            'user',
            'address',
            'orderItems',
            'orderItems.model',
            'orderItems.model.product',
            'orderItems.model.product.media',
            'shippingCarrier',
            'paymentMethod',
        ])->filters()->customPaginate();

    }

    public function findByIdUserOrder(int|string $id): mixed
    {

        return $this->query()->where('userId', auth(GUARD_API)->user()->userId)->with([
            'orderItems',
            'orderItems.model',
            'invoices',
            'orderItems.model.product',
            'orderItems.model.product.media',
            'shippingCarrier',
            'user',
            'address',
            'deliveries',
            'paymentMethod',
        ])->findOrFail($id);
    }



    public function findOrderById(int|string $id): mixed
    {

        return $this->query()->with([
            'orderItems',
            'orderItems.model',
            'invoices',
            'orderItems.model.product',
            'orderItems.model.product.media',
            'orderItems.model.product.activeStock',
            'orderItems.model.variance.activeStock',
            'orderItems.model.variance.gallery',
            'shippingCarrier',
            'user',
            'address',
            'paymentMethod',
        ])->findOrFail($id);

    }




    public function storeOrderFormCart()
    {
        $flagQueryCart = false;

        $query = Cart::query();

        if (isVisitor()) {
            $query->where('visitorId', auth(GUARD_API)->user()->visitorId);
        } else {
            $query->where('userId', auth(GUARD_API)->user()->userId);
        }

        $cartItems = $query->with([
            'variance',
            'variance.activeStock'
        ])->get();


        if ($flagQueryCart) {
            $cartItems = $query->with([
                'variance',
                'variance.activeStock'
            ])->get();
        }

        $cartItems->map(function ($item) use (&$flagQueryCart) {
            if ($item->product->type == ProductTypeEnum::bundle->value) {
                if (is_null($item?->product?->activeStock)) {
                    $item->delete();
                }
            } else {
                if (is_null($item?->variance?->activeStock)) {
                    $item->delete();
                    $flagQueryCart = true;
                }
            }
        });

        if ($flagQueryCart) {
            $cartItems = $query->with([
                'variance',
                'variance.activeStock'
            ])->get();
        }

        if ($cartItems->count() == 0) {

            throw new \Exception(__('There is no items'), Response::HTTP_LOCKED);

        }

        $cartItems = apply_filters('order.after-prepare-cart-items', $cartItems);
        $total = 0;
        $cost = 0;

        $cartItems = apply_filters('cart.after-prepare-cart-items', $cartItems);


        $total = apply_filters('order.total-calculated-items', [$total, $cartItems]);
        $cost = apply_filters('order.cost-calculated-items', [$cost, $cartItems]);

        $order = null;
        DB::transaction(function () use ($cartItems, $total, &$order, $query) {

            $order = $this->store([
                'status' => OrderStatusEnum::draft->value,
                'paymentStatus' => OrderPaymentStatusEnum::unPaid->value,
                'paymentMethodId' => null,
                'userId' => !isVisitor() ? auth(GUARD_API)->user()->userId : null,
                'visitorId' => isVisitor() ? auth(GUARD_API)->user()->visitorId : null,
                'total' => $total,
                'addressId' => null,
                'shippingCarrierId' => null,
                "subTotal" => $total,

            ]);


            $order = apply_filters('order.created', $order);
            $orderItemsSave = [];
            $orderItems = $cartItems->map(function ($item) use ($order, &$orderItemsSave) {
                $item = apply_filters('order.before-prepare-order-item', $item);

                $snapshot['product'] = $item->product?->toArray() ?? [];
                $snapshot['variance'] = $item?->variance?->toArray() ?? [];
                $snapshot['activeStock'] = $item->product->type == ProductTypeEnum::bundle->value ? $item->product?->activeStock?->toArray() ?? [] : $item->variance?->activeStock?->toArray() ?? [];
                $snapshot['media'] = $item->product->type == ProductTypeEnum::bundle->value ? $item->product?->media?->toArray() ?? [] : $item->variance?->media?->toArray() ?? [];
                $snapshot['attributesWithValue'] = $item?->variance?->attributesWithValue?->toArray() ?? [];



                $costItem = $item->product->type === ProductTypeEnum::bundle->value ? $item->product->activeStock->cost * $item->quantity : $item->variance->activeStock->cost * $item->quantity;



                $item = [
                    //'orderId' => $order->orderId,
                    'productId' => $item->productId,
                    'snapshot' => $snapshot,
                    'price' => $item->product->type === ProductTypeEnum::bundle->value ? $item->product->activeStock->price : $item->variance->activeStock?->price,
                    'originalPrice' => $item->product->type === ProductTypeEnum::bundle->value ? $item->product->activeStock->price : $item->variance->activeStock?->price ?? null,
                    'discount' => 0,
                    'status' => OrderItemStatusEnum::reseed->value,
                    'quantity' => $item->quantity,
                    'stockId' => $item->product->type === ProductTypeEnum::bundle->value ? $item->product->activeStock->stockId : $item->variance->activeStock->stockId,
                    'varianceId' => $item->product->type === ProductTypeEnum::bundle->value ? null : $item->variance->varianceId
                ];


                $item = apply_filters('order.after-prepare-order-item', $item);
                $model = ProductOrderItem::create($item);

                $orderItemsSave[] = [
                    "model_type" => ProductOrderItem::class,
                    "model_id" => $model->productOrderItemId,
                    'cost' => $costItem,
                ];


                return $item;
            });

            $order->orderItems()->sync($orderItemsSave);

            // $order->orderItems()->sync($orderItems);
            // OrderItem::insert($orderItems->toArray());
            apply_filters('order.after-inserted-order-items', $orderItems);
            // $query->delete();
            $order->load(['orderItems', 'orderItems.model', 'orderItems.model.variance', 'orderItems.model.product']);
            $order->touch();

        });


        return $order;

    }


    public function buyNow($data)
    {
        $data["productId"];
        $data['varianceId'];
        $variance = null;

        $order = null;
        $product = null;
        $cost = 0;
        if (!is_null($data['varianceId'])) {
            $variance = resolve(VarianceRepositoryInterface::class)->findById($data['varianceId']);
            $product = $variance->product;
            $priceItem = $variance?->activeStock?->price ?? 0;
            $costItem = $variance?->activeStock?->cost ?? 0;
        } else {
            $product = resolve(ProductRepositoryInterface::class)->findByIdProduct($data['productId']);
            $priceItem = $product?->activeStock?->price ?? 0;
            $costItem = $product?->activeStock?->cost ?? 0;
        }



        $total = $priceItem * $data['quantity'];
        $cost = $costItem * $data['quantity'];

        DB::transaction(function () use ($variance, $product, $priceItem, $total, $cost, &$order, $data) {

            $order = $this->store([
                'total' => $total,
                'subTotal' => $total,
                'status' => OrderStatusEnum::draft->value,
                'paymentStatus' => OrderPaymentStatusEnum::unPaid->value,
                'paymentMethodId' => null,
                'userId' => !isVisitor() ? auth(GUARD_API)->user()->userId : null,
                'visitorId' => isVisitor() ? auth(GUARD_API)->user()->visitorId : null,
                'addressId' => null,
                'shippingCarrierId' => null,
                'cost' => $cost,

            ]);


            $order = apply_filters('order.created', $order);

            $item = apply_filters('order.before-prepare-order-item', $product);

            $snapshot['product'] = $product?->toArray() ?? [];
            $snapshot['variance'] = is_null($variance) ? [] : $variance?->toArray() ?? [];
            $snapshot['activeStock'] = $product->type == ProductTypeEnum::bundle->value ? $product?->activeStock?->toArray() ?? [] : $variance?->activeStock?->toArray() ?? [];
            $snapshot['media'] = $product->type == ProductTypeEnum::bundle->value ? $product?->media?->toArray() ?? [] : $variance?->media?->toArray() ?? [];
            $snapshot['attributesWithValue'] = $item?->attributesWithValue?->toArray() ?? [];


            $item = [
                // 'orderId' => $order->orderId,
                'productId' => $product->productId,
                'snapshot' => $snapshot,
                'price' => (float) $priceItem,
                'originalPrice' => (float) $priceItem,
                'discount' => 0,
                'status' => OrderItemStatusEnum::reseed->value,
                'quantity' => $data['quantity'],
                'stockId' => $product->type === ProductTypeEnum::bundle->value ? $product->activeStock?->stockId ?? null : $variance->activeStock?->stockId ?? null,
                'varianceId' => $variance?->varianceId ?? null
            ];

            $item = apply_filters('order.after-prepare-order-item', $item);

            $model = ProductOrderItem::create($item);

            $orderItems = [
                "model_type" => ProductOrderItem::class,
                "model_id" => $model->productOrderItemId,
                'cost' => $cost * (int) $data['quantity'],
            ];
            $order->orderItems()->sync([$orderItems]);
            //$order->orderItems()->model->associate($item);
            // OrderItem::insert($item);
            $order = $this->update($order->orderId, [
                'total' => $total,
                'subTotal' => $total,

            ]);
            apply_filters('order.after-inserted-order-items', [$item]);

            return $order->load($this->relationsWithOne);

        });

        return $order;


    }



    public function address($id, $data)
    {

        $order = $this->update($id, [
            "addressId" => $data['addressId'],

        ]);


        if ($order->orderItems->count() > 1) {
            $order->orderItems->map(function ($orderItem) {
                if ($orderItem->model?->product->type == ProductTypeEnum::bundle->value && is_null($orderItem->model?->product?->activeStock)) {
                    $orderItem->delete();
                } elseif (is_null($orderItem->model?->variance?->activeStock)) {
                    $orderItem->delete();
                }
            });
            $order->load(['orderItems', 'orderItems.model', 'orderItems.model.variance', 'orderItems.model.product']);
            $order->touch();

        }

        return $order;
    }


    public function setShippingCarrier(int|string $id, array $data)
    {

        $order = $this->findById($id);

        $shippingCarrierPrice = ShippingCarrierPrice::where('shippingCarrierId', $data['shippingCarrierId'])
            ->where('shipping_carrier_prices.cityId', $order->address->cityId)
            ->with(['shippingCarrier'])
            ->first();

        if (!is_null($shippingCarrierPrice->shippingCarrier->config)) {
            if (isset($shippingCarrierPrice->shippingCarrier->config['minItems']) && $shippingCarrierPrice->shippingCarrier->config['minItems'] <= $order->orderItems->count()) {
                $shippingCarrierPrice->price = 0;
            } elseif (isset($shippingCarrierPrice->shippingCarrier->config['minAmount']) && $shippingCarrierPrice->config['minAmount'] <= $order->totalPrice) {
                $shippingCarrierPrice->price = 0;
            }
        }




        $order = $this->update($id, [
            "shippingCarrierId" => $data['shippingCarrierId'],
            "shippingPrice" => $shippingCarrierPrice->price
        ]);


        if ($order->orderItems->count() > 1) {
            $order->orderItems->map(function ($orderItem) {
                if ($orderItem->model?->product->type == ProductTypeEnum::bundle->value && is_null($orderItem->model?->product?->activeStock)) {
                    $orderItem->delete();
                } elseif (is_null($orderItem->model?->variance?->activeStock)) {
                    $orderItem->delete();
                }
            });
            $order->load(['orderItems', 'orderItems.model', 'orderItems.model.variance', 'orderItems.model.product']);
            $order->touch();

        }

        return $order;


    }



    public function setPromotion(int|string $id, array $data)
    {

        $promotion = resolve(PromotionRepositoryInterface::class)->query()->where('name', $data['name'])->firstOrFail();


        $rule = $promotion->rule;
        $discount = 0;

        $order = $this->findById($id);

        $subTotal = $order->subTotal;

        foreach ($order->orderItems as $item) {
            $discountItem = 0;

            if (isset($rule['items']["ids"]) && count($rule['items']["ids"])) {


                if (in_array($item->model->productId, $rule['items']["ids"])) {

                    if ($rule['type'] == PromotionTypeEnum::fixed->value) {
                        $discountItem = (int) $rule['items']['value'];

                    } else {

                        $discountItem = ((float) $item->model->originalPrice->price * (float) $rule['items']['value']);
                    }


                    $item->model->discount = $discountItem;

                    $item->model->save();



                    $item->model->order()->update([
                        'price' => $item->model->originalPrice - $discountItem,
                    ]);


                }
            } elseif (isset($rule['brands']) && count($rule['brands']["ids"])) {

                if (in_array($item->model->product->brandId, $rule['brands']["ids"])) {

                    if ($rule['type'] == PromotionTypeEnum::fixed->value) {
                        $discountItem = $rule['brands']['value'];
                    } else {
                        $discountItem = ((float) $item->model->originalPrice * (float) $rule['brands']['value']);
                    }

                    $item->model->discount = $discountItem;
                    $item->model->save();
                    $item->update([
                        'price' => (float) $item->model->originalPrice - $discountItem
                    ]);
                }
            } elseif (isset($rule['categories']) && count($rule['categories']["ids"]) > 0) {
                $categoriesIds = $item->model->product->categories->pluck('categoryId')->toArray();
                if ($rule['type'] == PromotionTypeEnum::fixed->value) {
                    $discountItem = $rule['categories']['value'];
                } else {
                    $discountItem = ((float) $item->model->price * (float) $rule['categories']['value']);
                }
                foreach ($categoriesIds as $categoriesId) {

                    if (in_array($categoriesId, $rule['categories']["ids"])) {
                        $item->model->discount = $discountItem;
                        $item->model->save();
                        $item->model->update([
                            'price' => (float) $item->model->originalPrice->price - $discountItem
                        ]);
                        break; // No need to continue once a common element is found
                    }

                }


            }

            $discount += $discountItem;

        }


        $order->orderItems->map(function ($item) use (&$subTotal) {
            $subTotal += (float) $item->model->variance->activeStock->price;
        });


        if ($discount > 0) {

            $subTotal -= $discount;

            $total = $subTotal + (float) $order->shippingPrice ?? 0;

            $order = $this->update($id, [
                'subTotal' => $subTotal,
                'total' => $total,

            ]);

        }

        return $order;
    }



    public function setPaymentMethod(int|string $id, array $data)
    {

        $order = $this->update($id, [
            "paymentMethodId" => $data['paymentMethodId'],
        ]);
        if (is_null($order->addressId)) {
            throw new \Exception(__('no address select'), Response::HTTP_LOCKED);
        }

        $order->save();

        $gatewayType = $order->paymentsMethod->module;
        if (isVisitor() && $gatewayType == PaymentMethodEnum::wallet->value) {
            throw new \Exception(__('if want pay must be login'), Response::HTTP_LOCKED);
        }



        $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);
        $amount = (float) $order->total;
        $paymentGateway->setAmount($amount);
        $paymentGateway->setOrderDetails($order);

        if (!isVisitor()) {
            $paymentGateway->setUser(auth(GUARD_API)->user());
        }



        return $order;
    }


    public function setPaymentParams($id, $data)
    {

        $order = $this->findById($id);
        if (is_null($order->addressId)) {
            throw new \Exception(__('no address select'), Response::HTTP_LOCKED);
        }


        // TODO: check stock  if have one item out of stock
        if ($order->orderItems->count() == 1) {
            $product = $order->orderItems->first()->model->product;
            $variance = $order->orderItems->first()->model?->variance;
            if ($product->type == ProductTypeEnum::bundle->value && is_null($product?->activeStock)) {
                throw new \Exception(__('this product out of stock'), Response::HTTP_LOCKED);
            } elseif (is_null($variance?->activeStock)) {
                throw new \Exception(__('this product out of stock'), Response::HTTP_LOCKED);
            }
        }

        $paymentGateway = PaymentGatewayFactory::createPaymentGateway($order->paymentsMethod->module);

        if ($order->paymentsMethod->module == PaymentMethodEnum::hyperpay->value) {
            $order->address->update([
                'email' => $data['email'],
                'firstName' => $data['firstName'],
                'lastName' => $data['lastName'],
            ]);
        }

        $amount = (float) $order->total;
        $paymentGateway->setAmount($amount);
        $paymentGateway->setOrderDetails($order);
        if (!isVisitor()) {
            $paymentGateway->setUser(auth(GUARD_API)->user());
        }

        $transactionId = $paymentGateway->generateTransactionId($this->modelClass);
        $order->transactionId = $transactionId;
        $paymentGateway->setParamFillForm();
        $order->hasFillForm = $paymentGateway->hasFillForm();
        $order->formSign = $paymentGateway->sign();
        return $order;
    }


    public function orderComplete(int|string $id, array $data)
    {

        dd('orderComplete');
        // $order = $this->findById($id);
        // $paymentMethod = resolve(PaymentMethodRepositoryInterface::class)->findById($order->paymentMethodId);
        // if ($paymentMethod->module === PaymentMethodEnum::cod->value) {
        //     $order = $this->update($id, [
        //         "paymentMethodId" => $data['paymentMethodId'],
        //         'status' => OrderStatusEnum::unPaid->value,
        //     ]);
        // } elseif ($paymentMethod->module === PaymentMethodEnum::arabbank->value) {
        //     $order = $this->update($id, [
        //         "paymentMethodId" => $data['paymentMethodId'],
        //         'status' => $order->total == $data['amount'] ? OrderStatusEnum::unPaid->value : OrderStatusEnum::partialPaid->value,
        //     ]);
        // }

        // $order->status = OrderStatusEnum::received->value;
        // $order->save();

        // apply_filters('order.notify-admin-new-order', $order);

        // return $order;
    }



    public function renderPay(int|string $id, array $data)
    {

        dd("renderPay");

        // $order = $this->findById($id);
        // if (is_null($order->paymentMethodId)) {
        //     throw new \Exception(__('Please Select a payment method'), Response::HTTP_LOCKED);
        // }
        // $gatewayType = $order->paymentsMethod->module;
        // $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);

        // $order = $this->findById($id);
        // if (is_null($order->paymentMethodId)) {
        //     throw new \Exception(__('Please Select a payment method'), Response::HTTP_LOCKED);
        // }
        // $gatewayType = $order->paymentsMethod->module;
        // $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);


        // $amount = (float) $order->total;

        // $paymentGateway->setAmount($amount);
        // $paymentGateway->setUser(auth(GUARD_API)->user());
        // $transactionId = $paymentGateway->generateTransactionId($this->modelClass);

        // $paymentGateway->setOrderDetails($order);
        // $paymentGateway->setParamFillForm();
        // $order->hasFillForm = $paymentGateway->hasFillForm();
        // $order->formSign = $paymentGateway->sign();
        // $order->transactionId = $transactionId;
        // return $order;
    }


    public function verify(int|string $id, array $data)
    {
        dd('verify');
        // $order = $this->findById($id);
        // $order->status = OrderStatusEnum::received->value;
        // $order->save();
        // if (is_null($order->paymentMethodId)) {
        //     throw new \Exception("Please Select a payment method");
        // }
        // $gatewayType = $order->paymentsMethod->module;
        // $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);
        // $amount = (float) $order->total;
        // $paymentGateway->setAmount($amount);
        // $paymentGateway->setUser(auth(GUARD_API)->user());
        // $paymentGateway->setOrderDetails($order);
        // $order->transactionId = $data['transactionId'];
        // $paymentGateway->makePay();

        // return $order;



    }


    public function finalize(int|string $id, array $data)
    {



        $order = $this->findById($id, with: ['user', 'address', 'address.city']);

        if ($order->status != OrderStatusEnum::draft->value) {
            throw new \Exception(__('order is Locked'), Response::HTTP_LOCKED);
        }

        if (is_null($order->paymentMethodId)) {
            throw new \Exception(__('no select  payment method'), Response::HTTP_LOCKED);
        }

        if (is_null($order->addressId)) {
            throw new \Exception(__('no address select'), Response::HTTP_LOCKED);
        }
        if ($order->paymentStatus === OrderPaymentStatusEnum::paid->value) {
            throw new \Exception(__('the order is piad'), Response::HTTP_LOCKED);
        }

        // TODO: check stock  if have one item out of stock
        if ($order->orderItems->count() == 1) {
            $product = $order->orderItems->first()->model->product;
            $variance = $order->orderItems->first()->model?->variance;
            if ($product->type == ProductTypeEnum::bundle->value && is_null($product?->activeStock)) {
                throw new \Exception(__('this product out of stock'), Response::HTTP_LOCKED);
            } elseif (is_null($variance?->activeStock)) {
                throw new \Exception(__('this product out of stock'), Response::HTTP_LOCKED);
            }
        }


        if ($order->orderItems->count() > 1) {
            $order->orderItems->map(function ($orderItem) {
                if ($orderItem->model?->product->type == ProductTypeEnum::bundle->value && is_null($orderItem->model?->product?->activeStock)) {
                    $orderItem->delete();
                } elseif (is_null($orderItem->model?->variance?->activeStock)) {
                    $orderItem->delete();
                }
            });
            $order->load(['orderItems', 'orderItems.model', 'orderItems.model.variance', 'orderItems.model.product']);
            $order->touch();

        }


        // foreach ($order->orderItems as $orderItem) {
        //     if ($orderItem->product->activeStock->quantity < $orderItem->quantity) {
        //         throw new \Exception("stoke not enough", 422);
        //     }
        // }

        $gatewayType = $order->paymentsMethod->module;
        $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);
        $amount = (float) $order->total;
        $paymentGateway->getPaymentStatus();
        $paymentGateway->setAmount($amount);
        $paymentGateway->setOrderDetails($order);
        $paymentGateway->setUser(auth(GUARD_API)->user());
        $order->transactionId = $paymentGateway->getTransactionId();
        $paymentGateway->setParamFillForm();
        $paymentGateway->makePay();
        $order = $this->findById($id);
        resolve(CartRepositoryInterface::class)->removeUserCart();
        apply_filters('order.completed', $order);


        // apply_filters('order.deliveries.received', $order);

        // apply_filters('order.invoices.received', $order);


        return $order;

    }




    public function getShippings($id)
    {
        $order = $this->findById($id);

        if (is_null($order->addressId)) {
            throw new \Exception(__('please select address'), Response::HTTP_LOCKED);
        }

        $address = resolve(AddressRepositoryInterface::class)->findById($order->addressId);
        $productIds = $order->orderItems->map(function ($orderItem) {
            return $orderItem->model->productId;
        })->toArray();
        $shippingCarriersCollection = collect();
        // Get regular shipping carriers for the products
        $shippingCarriers = ShippingCarrierPrice::with(['shippingCarrier'])
            ->whereIn('shippingCarrierId', function ($query) use ($productIds) {
                $query->select('shippingCarrierId')
                    ->from('product_shippings')
                    ->whereIn('productId', $productIds);
            })
            ->where('shipping_carrier_prices.cityId', $address->cityId)
            ->get();

        foreach ($shippingCarriers as $shippingCarrier) {
            if (is_null(value: $shippingCarrier->shippingCarrier->config)) {
                if (isset($shippingCarrier->shippingCarrier->config['minItems']) && $shippingCarrier->shippingCarrier->config['minItems'] <= $order->orderItems->count()) {
                    $shippingCarrier->price = 0;
                    $shippingCarriersCollection->push($shippingCarrier);
                } elseif (isset($shippingCarrier->shippingCarrier->config['minAmount']) && $shippingCarrier->config['minAmount'] <= $order->totalPrice) {
                    $shippingCarrier->price = 0;
                    $shippingCarriersCollection->push($shippingCarrier);
                } else {
                    $shippingCarriersCollection->push($shippingCarrier);
                }
            } else {
                $shippingCarriersCollection->push($shippingCarrier);
            }
        }

        return $shippingCarriersCollection;
    }


    public function getShippingCarriers($id)
    {
        $order = $this->findById($id);

        if (is_null($order->addressId)) {
            throw new \Exception(__('please select address'), Response::HTTP_LOCKED);
        }
        $address = resolve(AddressRepositoryInterface::class)->findById($order->addressId);
        $productIds = $order->orderItems->map(function ($orderItem) {
            return $orderItem->model->productId;
        })->toArray();
        $shippingCarriersCollection = collect();
        // Get regular shipping carriers for the products
        $shippingCarriers = ShippingCarrierPrice::with(['shippingCarrier'])
            ->whereIn('shippingCarrierId', function ($query) use ($productIds) {
                $query->select('shippingCarrierId')
                    ->from('product_shippings')
                    ->whereIn('productId', $productIds);
            })
            ->where('shipping_carrier_prices.cityId', $address->cityId)
            ->get();

        foreach ($shippingCarriers as $shippingCarrier) {
            if (!is_null(value: $shippingCarrier->shippingCarrier->config)) {
                if (isset($shippingCarrier->shippingCarrier->config['minItems']) && $shippingCarrier->shippingCarrier->config['minItems'] <= $order->orderItems->count()) {
                    $shippingCarrier->price = 0;
                    $shippingCarriersCollection->push($shippingCarrier);
                } elseif (isset($shippingCarrier->shippingCarrier->config['minAmount']) && $shippingCarrier->config['minAmount'] <= $order->totalPrice) {
                    $shippingCarrier->price = 0;
                    $shippingCarriersCollection->push($shippingCarrier);
                } else {
                    $shippingCarriersCollection->push($shippingCarrier);
                }
            } else {
                $shippingCarriersCollection->push($shippingCarrier);
            }
        }

        return $shippingCarriersCollection;

    }



    public function getAllowPaymentMethods($id)
    {
        $order = $this->findById($id);
        $productIds = [];
        foreach ($order->orderItems as $orderItem) {
            $productIds[] = $orderItem->model->productId;
        }

        $date = Carbon::now();

        return resolve(PaymentMethodRepositoryInterface::class)->query()
            ->whereNotIn('paymentMethodId', function ($query) use ($productIds, $date) {
                $query->select('paymentMethodId')
                    ->from('product_payments')
                    ->whereIn('productId', $productIds);
                // ->where('product_payments.isPublished', PUBLISHED)
                // ->where('product_payments.publishedAt', '<=', $date)
                // ->where('product_payments.unPublishedAt', '>=', $date);
            })->where('status', StatusActivation::active->value)
            ->with('media')
            ->get();

    }




    public function processing($id, $data)
    {
        /*
        1- check if order is can be moved to processing
            a- check if all
        */
        dd('processing api');
        // $order = $this->findById(id: $id, with: $this->relationsWithOne);

        // $amountPaid = $order->transactions->where('status', TransactionStatusEnum::success->value)->sum('amount');
        // $amountUnPaid = $order->total - $amountPaid;


        // if (in_array($order->paymentMethod->module, [PaymentMethodEnum::arabbank->value, PaymentMethodEnum::hyperpay->value, PaymentMethodEnum::wallet->value, PaymentMethodEnum::efawateercom->value])) {

        //     if ($amountPaid == $order->total) {
        //         $order->paymentStatus = OrderPaymentStatusEnum::paid->value;
        //     } elseif ($amountPaid == 0) {
        //         // generate invoices unPaid 
        //         $order->invoices()->update([
        //             'status' => InvoiceStatusEnum::canceled->value,
        //         ]);

        //         $order->generateInvoice($amountPaid, OrderPaymentStatusEnum::unPaid->value);
        //         $order->paymentStatus = OrderPaymentStatusEnum::unPaid->value;

        //     } elseif ($amountPaid > $order->total) {
        //         // generate invoices unPaid 
        //         $order->invoices()->update([
        //             'status' => InvoiceStatusEnum::canceled->value,
        //         ]);
        //         $amount = $amountPaid - $order->total;
        //         $order->generateInvoice($order->total, OrderPaymentStatusEnum::paid->value);
        //         resolve(TransactionRepositoryInterface::class)->fill($order->userId, $amount, $order->orderId, Order::class);
        //     } else {

        //         if (User::find($order->userId)->wallet >= $amountUnPaid) {
        //             resolve(TransactionRepositoryInterface::class)->withdraw($order->userId, $amountUnPaid, $order->orderId, Order::class);
        //             $order->generateInvoice($amountUnPaid, OrderPaymentStatusEnum::paid->value);
        //             $order->paymentStatus = OrderPaymentStatusEnum::paid->value;
        //         } else {
        //             $order->generateInvoice($amountUnPaid, OrderPaymentStatusEnum::unPaid->value);
        //             $order->paymentStatus = OrderPaymentStatusEnum::partialPaid->value;

        //         }
        //     }


        // } else {// for cod 
        //     // generate new invoices
        //     $order->generateInvoice($order->total, OrderPaymentStatusEnum::unPaid->value);
        // }


        // /******************************************************************************/
        // $paymentGateway = PaymentGatewayFactory::createPaymentGateway($order->paymentsMethod->module);
        // $paymentGateway->setAmount((float) $order->total);
        // $paymentGateway->setOrderDetails($order);


        // if ($order->total > $amountPaid) {
        //     $paymentGateway->payRestOfAmount($amountUnPaid);
        // } elseif ($order->total < $amountPaid) {
        //     $paymentGateway->refundRestOfAmount($amountUnPaid);
        // }

        // $amountPaid = $order->transactions->where('status', TransactionStatusEnum::success->value)->sum('amount');

        // if ($amountPaid == $order->total) {
        //     $order->paymentStatus = OrderPaymentStatusEnum::paid->value;
        // } elseif ($amountPaid == 0) {
        //     $order->paymentStatus = OrderPaymentStatusEnum::unPaid->value;
        // } elseif ($amountPaid > $order->total) {

        // } else {
        //     $order->paymentStatus = OrderPaymentStatusEnum::partialPaid->value;
        // }

        // $order->status = OrderStatusEnum::processing->value;
        // $order->save();

        // return $order;
    }





    public function canceled($id)
    {

        throw new \Exception(__("response-messages.cant-cancel-order"), Response::HTTP_LOCKED);
        // $order = $this->findById($id);
        // $order->status = OrderStatusEnum::canceled->value;

        // if (!is_null($order->paymentMethod) && $order->paymentMethod->module === PaymentMethodEnum::wallet->value) {
        //     // refuand to wallet
        //     foreach ($order->invoices as $invoice) {
        //         if ($invoice->paymentMethod->module ?? '' === PaymentMethodEnum::wallet->value) {
        //             $refund = resolve(TransactionRepositoryInterface::class)->fill(auth(GUARD_API)->user()->userId, $invoice->amount, $invoice->invoiceId, 'invoice');
        //         }
        //     }
        // }

        // $order->save();
        // return $order;
    }



    public function cancel($id)
    {

        $order = $this->findById($id);
        $order->status = OrderStatusEnum::canceled->value;

        if (!is_null($order->paymentMethod) && $order->paymentMethod->module === PaymentMethodEnum::wallet->value) {
            // refuand to wallet
            foreach ($order->invoices as $invoice) {
                if ($invoice->paymentMethod->module ?? '' === PaymentMethodEnum::wallet->value) {
                    $refund = resolve(TransactionRepositoryInterface::class)->fill(auth(GUARD_API)->user()->userId, $invoice->amount, $invoice->invoiceId, 'invoice');
                }
            }

        }

        $order->save();
        return $order;
    }





    public function completed($id)
    {
        $order = $this->findById($id);
        // refuand to wallet
        foreach ($order->invoices as $invoice) {
            if (
                !(
                    (
                        $invoice->paymentMethod->module ?? '' === PaymentMethodEnum::wallet->value ||

                        $invoice->paymentMethod->module ?? '' === PaymentMethodEnum::efawateercom->value ||
                        $invoice->paymentMethod->module ?? '' === PaymentMethodEnum::arabbank->value
                    )
                    &&
                    $invoice->status == InvoicePaymentStatusEnum::paid->value
                )
            ) {
                throw new \Exception("order not completed (not pay invoice number ($invoice->invoiceId)", Response::HTTP_LOCKED);
            }
        }

        foreach ($order->deliveries as $delivery) {
            if (!$delivery->status === DeliveryStatusEnum::delivered->value) {
                throw new \Exception(__('order not completed not delivered'), Response::HTTP_LOCKED);
            }
        }

        $order->status = OrderStatusEnum::completed->value;
        $order->save();
        return $order;
    }




    /**
     * @param   $orderId
     * @param  $data
     */
    public function applyPromoCode($id, $data)
    {

        $promotion = resolve(PromotionRepositoryInterface::class)->query()->where('name', $data['name'])->firstOrFail();
        $rule = $promotion->rule;
        $discount = 0;

        $order = $this->findById($id);

        $subTotal = $order->subTotal;

        foreach ($order->orderItems as $item) {
            $discountItem = 0;

            if (isset($rule['items']["ids"]) && count($rule['items']["ids"])) {


                if (in_array($item->model->productId, $rule['items']["ids"])) {

                    if ($rule['type'] == PromotionTypeEnum::fixed->value) {
                        $discountItem = (int) $rule['items']['value'];

                    } else {

                        $discountItem = ((float) $item->model->originalPrice->price * (float) $rule['items']['value']);
                    }


                    $item->model->discount = $discountItem;

                    $item->model->save();



                    $item->model->order()->update([
                        'price' => $item->model->originalPrice - $discountItem,
                    ]);


                }
            } elseif (isset($rule['brands']) && count($rule['brands']["ids"])) {

                if (in_array($item->model->product->brandId, $rule['brands']["ids"])) {

                    if ($rule['type'] == PromotionTypeEnum::fixed->value) {
                        $discountItem = $rule['brands']['value'];
                    } else {
                        $discountItem = ((float) $item->model->originalPrice * (float) $rule['brands']['value']);
                    }

                    $item->model->discount = $discountItem;
                    $item->model->save();
                    $item->update([
                        'price' => (float) $item->model->originalPrice - $discountItem
                    ]);
                }
            } elseif (isset($rule['categories']) && count($rule['categories']["ids"]) > 0) {
                $categoriesIds = $item->model->product->categories->pluck('categoryId')->toArray();
                if ($rule['type'] == PromotionTypeEnum::fixed->value) {
                    $discountItem = $rule['categories']['value'];
                } else {
                    $discountItem = ((float) $item->model->price * (float) $rule['categories']['value']);
                }
                foreach ($categoriesIds as $categoriesId) {

                    if (in_array($categoriesId, $rule['categories']["ids"])) {
                        $item->model->discount = $discountItem;
                        $item->model->save();
                        $item->model->update([
                            'price' => (float) $item->model->originalPrice->price - $discountItem
                        ]);
                        break; // No need to continue once a common element is found
                    }

                }


            }

            $discount += $discountItem;

        }


        $order->orderItems->map(function ($item) use (&$subTotal) {
            $subTotal += (float) $item->model->variance->activeStock->price;
        });


        if ($discount > 0) {

            $subTotal -= $discount;

            $total = $subTotal + (float) $order->shippingPrice ?? 0;

            $order = $this->update($id, [
                'subTotal' => $subTotal,
                'total' => $total,

            ]);

        }

        return $order;
    }



    /**
     * @param   $orderId
     * @param  $data
     */
    public function deliverMoney($id, $data)
    {


        DB::beginTransaction();
        try {

            $order = $this->findById($id);
            $order->invoices()->where('invoiceId', $data['invoiceId'])->update([
                'paymentStatus' => InvoicePaymentStatusEnum::paid->value
            ]);


            $allDeliverMoney = true;
            $allDeliveryProduct = true;
            foreach ($order->deliveries as $key => $invoice) {
                if ($invoice->status !== DeliveryStatusEnum::delivered->value) {
                    $allDeliveryProduct = false;
                }
            }
            foreach ($order->invoices() as $key => $invoice) {
                if ($invoice->paymentStatus !== InvoicePaymentStatusEnum::paid->value) {
                    $allDeliverMoney = false;
                }
            }

            if ($allDeliverMoney && $allDeliveryProduct) {
                $this->update($order->orderId, ['status' => OrderStatusEnum::completed->value]);
            }



            DB::commit();

            return $order->load($this->relationsWithOne);

        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();
            // Re-throw the exception to manage it according to your app’s needs.
            throw $e;
        }

    }


    /**
     * @param   $orderId
     * @param int|string $id
     * @param array $data
     * @return \App\Models\Order
     * @param  $data
     */
    public function changeDeliveryStatus($id, $data)
    {

        DB::beginTransaction();
        try {

            $order = $this->findById($id);
            /** @var \App\Models\Order $order */
            $order->deliveries()->update([
                'status' => $data['status']
            ]);

            if ($data['status'] == DeliveryStatusEnum::delivered->value) {

                foreach ($order->orderItems as $orderItem) {
                    if ($orderItem->model->status != OrderItemStatusEnum::cancelled->value) {
                        $orderItem->model->stock->decrement('quantity', $orderItem->model->quantity);
                        $product = $orderItem->model->product;
                        $product->load('variances.stocks', 'stocks');
                        $product->searchable();
                    }
                }
            }


            $allDeliverMoney = true;
            $allDeliveryProduct = true;
            foreach ($order->deliveries as $key => $invoice) {
                if ($invoice->status !== DeliveryStatusEnum::delivered->value) {
                    $allDeliveryProduct = false;
                }
            }
            foreach ($order->invoices() as $key => $invoice) {
                if ($invoice->paymentStatus !== InvoicePaymentStatusEnum::paid->value) {
                    $allDeliverMoney = false;
                }
            }

            if ($allDeliverMoney && $allDeliveryProduct) {
                $this->update($order->orderId, ['status' => OrderStatusEnum::completed->value]);
            }



            DB::commit();

            return $order->load($this->relationsWithOne);

        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();
            // Re-throw the exception to manage it according to your app’s needs.
            throw $e;
        }



    }
    /**
     * @param   $orderId
     * @param  $data
     */
    public function addNote($id, $data)
    {

        $order = $this->findById($id);
        $order->notes()->create([
            'note' => $data['note'],
            'userId' => !isVisitor() ? auth(GUARD_API)->user()->userId : null,
            'visitorId' => isVisitor() ? auth(GUARD_API)->user()->visitorId : null,
        ]);
        return $order->load($this->relationsWithOne);

    }





    public function quantityProduct($id, $data)
    {

        DB::beginTransaction();


        try {

            $order = $this->findById($id);

            $orderItem = OrderItem::where('model_id', $data['model_id'])
                ->where('model_type', ProductOrderItem::class)
                ->where('orderId', $id)
                ->first();
            $orderItem->model->update([
                "quantity" => $data['quantity']
            ]);

            $order->save();

            DB::commit();

            return $order->load($this->relationsWithOne);

        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();

            // Re-throw the exception to manage it according to your app’s needs.
            throw $e;
        }





    }




    public function addProduct($id, $data)
    {
        $order = $this->findById($id);

        DB::beginTransaction();

        try {

            $this->checkEditing($order);

            $varianceRepo = resolve(VarianceRepositoryInterface::class);
            $variance = $varianceRepo->findById($data['varianceId']);

            $snapshot['product'] = $variance->product?->toArray() ?? [];
            $snapshot['variance'] = $variance->variance?->toArray() ?? [];
            $snapshot['activeStock'] = $variance->variance?->activeStock?->toArray() ?? [];
            $snapshot['media'] = $variance->variance?->media?->toArray() ?? [];
            $snapshot['attributesWithValue'] = $variance->variance?->attributesWithValue?->toArray() ?? [];


            $item = [
                'productId' => $data['productId'],
                'snapshot' => $snapshot,
                'price' => $variance->activeStock->price ?? 0,
                'originalPrice' => (float) $variance->activeStock->price ?? 0,
                'discount' => 0,
                'status' => OrderItemStatusEnum::reseed->value,
                'quantity' => $data['quantity'],
                'stockId' => $variance->activeStock?->stockId ?? null,
                'varianceId' => $variance->varianceId
            ];

            $item = apply_filters('order.after-prepare-order-item', $item);

            $model = ProductOrderItem::create($item);

            $orderItem = new OrderItem([
                "model_type" => ProductOrderItem::class,
                "model_id" => $model->productOrderItemId,
            ]);

            $order->orderItems()->save($orderItem);

            $order->touch(); // update total and subtotal

            apply_filters('order.after-inserted-order-items', [$item]);

            // Commit the transaction
            DB::commit();

            return $order->load($this->relationsWithOne);
        } catch (\Exception $e) {
            // Rollback the transaction if there's an error
            DB::rollBack();

            // Handle the exception (log it, display error message, etc.)
            throw $e;
        }
    }


    /**
     * @param   $orderId
     * @param  $data
     */
    public function cancelOrderItem(string|int $orderId, string|int $orderItemId)
    {
        // Start new database transaction.
        DB::beginTransaction();

        try {
            $order = Order::findOrFail($orderId);

            $order->orderItems()->where('orderItemsId', $orderItemId)->firstOrFail()->model()->update([
                'status' => OrderItemStatusEnum::cancelled->value
            ]);

            $order->touch(); // update total and subtotal

            // Commit the transaction if everything's ok.
            DB::commit();

            return $order->load($this->relationsWithOne);
        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();

            // Re-throw the exception to manage it according to your app’s needs.
            throw $e;
        }
    }

    /**
     * @param   $orderId
     * @param  $data
     */
    public function changeShippingCarrier($id, $data)
    {

        DB::beginTransaction();

        try {

            $order = $this->findById($id);
            $this->checkEditing($order);
            $shippingCarrierPrice = ShippingCarrierPrice::where('shippingCarrierId', $data['shippingCarrierId'])
                ->where('shipping_carrier_prices.cityId', $order->address->cityId)
                ->first();

            if (is_null($shippingCarrierPrice)) {
                throw new \Exception(__('shipping carrier not support in this city'), Response::HTTP_LOCKED);
            }

            $order->shippingPrice = $shippingCarrierPrice->price;
            $order->save();

            return $order->load($this->relationsWithOne);

        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();

            // Re-throw the exception to manage it according to your app’s needs.
            throw $e;
        }

    }



    /**
     * @param   $orderId
     * @param  $data
     */
    public function deleteProduct($id, $data)
    {
        $order = $this->findById($id);
        $this->checkEditing($order);
        $orderItem = OrderItem::findOrFail($data['orderItemsId']);
        OrderItem::where('orderItemsId', $data['orderItemsId'])->delete();
        ProductOrderItem::where('productOrderItemId', $orderItem->model_id)->delete();
        $orderItem->delete();
        return $order->load($this->relationsWithOne);



        // DB::beginTransaction();
        // try {
        //     $order = $this->findById($id);
        //     $this->checkEditing($order);


        //     $orderItem = OrderItem::findOrFail($data['orderItemsId']);

        //     OrderItem::where('orderItemsId', $data['orderItemsId'])->delete();
        //     ProductOrderItem::where('productOrderItemId', $orderItem->model_id)->delete();

        //     $orderItem->delete();

        //     return $order->load($this->relationsWithOne);

        // } catch (\Exception $e) {
        //     // Rollback the transaction in case of any error.
        //     DB::rollBack();

        //     // Re-throw the exception to manage it according to your app’s needs.
        //     throw $e;
        // }

    }




    public function startEditing($id, $data)
    {


        DB::beginTransaction();
        try {

            $order = $this->findById($id);

            if ($order->editors()->whereNull('endAt')->count()) {
                throw new \Exception(__('some one is editing this order'), Response::HTTP_LOCKED);
            }


            $order->update([
                'status' => OrderStatusEnum::editing->value
            ]);
            $order->editors()->create([
                'userId' => auth(GUARD_API)->user()->userId,
                'startAt' => Carbon::now(),
            ]);

            DB::commit();

            return $order->load($this->relationsWithOne);
        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();
            throw $e;
        }



    }

    public function endEditing($id, $data)
    {

        DB::beginTransaction();
        try {

            $order = $this->findById($id);

            $order->update([
                'status' => OrderStatusEnum::received->value
            ]);

            $order->editors()->whereNull('endAt')->update([
                'endAt' => Carbon::now(),
            ]);


            // apply_filters('order.invoices.end.editing.order.invoices.end.editing', $order);


            DB::commit();

            return $order->load($this->relationsWithOne);
        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();
            throw $e;
        }




    }




    public function changeStatus($id, $status, $data)
    {


        DB::beginTransaction();

        try {


            if (!in_array($status, OrderStatusEnum::getAll())) {
                throw new \Exception(__('invalid status'), Response::HTTP_LOCKED);
            }

            $order = $this->findById($id);

            $amountPaid = 0;
            $amountUnPaid = $order->total;

            if ($order->paymentMethod) {
                $isPaymentMethodPaid = in_array($order->paymentMethod->module, [PaymentMethodEnum::arabbank->value, PaymentMethodEnum::hyperpay->value, PaymentMethodEnum::wallet->value, PaymentMethodEnum::efawateercom->value]);
                if ($isPaymentMethodPaid) {
                    $amountPaid = $order->transactions->where('status', TransactionStatusEnum::success->value)->where('amount', '>', 0)->sum('amount');
                    $amountUnPaid = $order->total - $amountPaid;
                }

                if ($status == OrderStatusEnum::processing->value) {
                    if (!$isPaymentMethodPaid) {
                        $order->invoices()->update([
                            'status' => InvoiceStatusEnum::canceled->value,
                        ]);
                        $order->generateInvoice($order->total, OrderPaymentStatusEnum::unPaid->value);
                    } else {
                        $amountInvoicesActive = $order->invoices()
                            ->where('status', InvoiceStatusEnum::active->value)
                            ->sum('total');
                        if ($amountPaid != $order->total && $amountInvoicesActive != $order->total) {
                            $order->generateInvoice($amountUnPaid, OrderPaymentStatusEnum::unPaid->value);
                            $order->paymentStatus = OrderPaymentStatusEnum::partialPaid->value;
                        }

                    }

                }
            }



            if ($status == OrderStatusEnum::canceled->value) {
                foreach ($order->orderItems as $orderItem) {
                    $orderItem->model?->variance?->activeStock?->increment('quantity', $orderItem->model->quantity);
                }
            } elseif ($status == OrderStatusEnum::forRefund->value) {

            } elseif ($status == OrderStatusEnum::refunded->value) {
                foreach ($order->orderItems as $orderItem) {
                    $orderItem->model?->variance?->activeStock?->increment('quantity', $orderItem->model->quantity);
                }
            }

            $order->update([
                'status' => $status
            ]);

            DB::commit();

            return $order->load($this->relationsWithOne);
        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();
            throw $e;
        }

    }



    public function refundOrder($id)
    {

        DB::beginTransaction();

        try {

            $order = $this->findById($id);
            if (in_array($order->paymentMethod->module, [PaymentMethodEnum::arabbank->value, PaymentMethodEnum::hyperpay->value, PaymentMethodEnum::wallet->value, PaymentMethodEnum::efawateercom->value])) {
                $refund = resolve(TransactionRepositoryInterface::class)->refundTransactionModel(Order::class, $order->orderId, $order->user);
                $order->invoices()->update(['paymentStatus' => InvoicePaymentStatusEnum::unPaid->value]);
                $order->update(['paymentStatus' => OrderPaymentStatusEnum::unPaid->value]);
            }

            DB::commit();

            return $order->load($this->relationsWithOne);
        } catch (\Exception $e) {
            // Rollback the transaction in case of any error.
            DB::rollBack();
            throw $e;
        }
    }

    public function verifyIsPaid($id)
    {


        $order = $this->findById($id);

        $gatewayType = $order->paymentsMethod->module;

        $paymentGateway = PaymentGatewayFactory::createPaymentGateway($gatewayType);

        return $paymentGateway->verify();
    }




    public function verifyOrderPaymentHyperpay($id, $data)
    {


        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . env('HYPERPAY_ACCESS_TOKEN'),
        ])->get(env('HYPERPAY_URL') . '/v1/checkouts/' . $data['checkoutId'] . '/payment', [
                    'entityId' => env('HYPERPAY_ENTITY_ID'),
                ]);



        $responseBody = $response->json();

        $successCodes = ['000.000.000', '000.100.110', '000.100.111', '000.100.112', '200.300.404'];
        $transactionRepo = resolve(TransactionRepositoryInterface::class);


        $transactionModel = $transactionRepo->query()->where('checkoutId', $data['checkoutId'])->first();

        $order = $this->findById($transactionModel->model_id);

        if (in_array($responseBody['result']['code'], $successCodes)) {

            $transactionRepo->update($transactionModel->transactionId, [
                'status' => TransactionStatusEnum::success->value
            ]);

            $order->invoices()->where('status', InvoiceStatusEnum::active->value)->update(['paymentStatus' => InvoicePaymentStatusEnum::paid->value]);

            $order->update(['paymentStatus' => OrderPaymentStatusEnum::paid->value, 'status' => OrderStatusEnum::received->value]);


        } else {

            $transactionRepo->update($transactionModel->transactionId, [
                'status' => TransactionStatusEnum::failed->value
            ]);

            $order->invoices()->where('status', InvoiceStatusEnum::active->value)->update(['paymentStatus' => InvoicePaymentStatusEnum::unPaid->value]);

            $order->update(['paymentStatus' => OrderPaymentStatusEnum::unPaid->value]);

        }


        $order = $this->findById($transactionModel->model_id);

        apply_filters('order.completed', $order);


        return $responseBody;
    }






    public function updateCost($id, $data)
    {
        $order = $this->findById($id);
        // update order cost without make boot method
        $order->updateQuietly([
            'cost' => Currency::convert($data['cost']['value'])->from($data['cost']['currencyId'])->toBase()->get(),
        ]);

        return $order->load($this->relationsWithOne);
    }



    public function updateCostItem($id, $data)
    {
        $order = $this->findById($id);
        // update order cost without make boot method
        $order->orderItems()
            ->where('orderItemsId', $data['orderItemsId'])
            ->update([
                'cost' => Currency::convert($data['cost']['value'])
                    ->from($data['cost']['currencyId'])
                    ->toBase()
                    ->get(),
            ]);

        $cost = $order->orderItems()->sum('cost') ?? 0;

        $order->updateQuietly([
            'cost' => $cost,
        ]);

        return $order->load($this->relationsWithOne);
    }



    public function storeRatingOnOrder(int|string $orderId, array $data)
    {

        $rating = OrderRating::create([
            'review' => $data['review'],
            'rating' => $data['rating'],
            'userId' => auth(GUARD_API)->user()->userId,
            'orderId' => $orderId,
        ]);

        return $rating;
    }


    public function updateRatingOnOrder(int|string $orderId, int|string $orderRatingId, array $data)
    {

        $rating = tap(OrderRating::find($orderRatingId), function ($rating) use ($data) {
            $rating->update([
                'review' => $data['review'],
                'rating' => $data['rating'],
            ]);
        })->refresh();

        return $rating;
    }


}