<?php

namespace App\Repositories\Complaint;

use Illuminate\Database\Eloquent\Model;
use App\Repositories\BaseRepositoryInterface;

/**
 * @mixin BaseRepositoryInterface
 */
interface ComplaintRepositoryInterface
{
    /**
     * Store Page  .
     *
     * @param array $data
     * @return Model
     */

    public function storeComplaint(array $data);
    /**
     * Update Page
     *
     * @param int|string $id
     * @param array $data
     * @return Model
     */
    public function updateComplaint(int|string $id, array $data): Model;


    /**
     * Store Rating
     *
     * @param array $data
     */
    public function storeRating(array $data);

    /**
     * Update Rating
     *
     * @param int|string $id
     * @param array $data
     * @return Model
     */
    public function updateRating(int|string $id, array $data): Model;


}