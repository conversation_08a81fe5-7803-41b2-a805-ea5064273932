<?php

namespace App\Repositories\Complaint;

use App\Repositories\BaseRepository;
use App\Models\Complaint;
use App\Models\Order;
use App\Models\RatingComplaint;
use App\Models\User;
use App\Models\Visitor;
use App\Repositories\Complaint\ComplaintRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class ComplaintRepository extends BaseRepository implements ComplaintRepositoryInterface
{

    public $modelClass = Complaint::class;

    public function storeComplaint(array $data): Model
    {

        $complaint = $this->store([
            'text' => $data['text'],
            'groupKey' => $data['groupKey'],

        ]);
        return $complaint;
    }

    public function updateComplaint(int|string $id, array $data): Model
    {
        $complaint = $this->update($id, [
            'text' => $data['text'],
            'groupKey' => $data['groupKey'],
        ]);

        return $complaint;
    }


    public function storeRating(array $data)
    {

        $user = auth(GUARD_API)->user();
        $modelName = $user->getMorphClass();
        $rating = collect([]);
        foreach ($data as $key => $value) {
            $modelType = $value['model_type'] === 'order' || $value['model_type'] === Order::class ? Order::class : Order::class;
            $rating->add(RatingComplaint::create([
                'review' => $value['review'],
                'rating' => $value['rating'],
                'userId' => $modelName === User::class ? auth(GUARD_API)->user()->userId : null,
                'visitorId' => $modelName === Visitor::class ? auth(GUARD_API)->user()->visitorId : null,
                'complaintId' => $value['complaintId'],
                'model_type' => $modelType,
                'model_id' => $value['model_id'],

            ]));
        }
        return $rating;
    }
    public function updateRating(int|string $id, array $data): Model
    {
        $rating = RatingComplaint::findOrFail($id);

        $rating->update([
            'review' => $data['review'],
            'rating' => $data['rating'],

        ]);

        return $rating;
    }


}