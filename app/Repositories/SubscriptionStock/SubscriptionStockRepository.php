<?php

namespace App\Repositories\SubscriptionStock;

use App\Models\SubscriptionStock;
use App\Models\User;
use App\Models\Visitor;
use App\Repositories\BaseRepository;
use Illuminate\Database\Eloquent\Model;
use App\Repositories\SubscriptionStock\SubscriptionStockRepositoryInterface;


class SubscriptionStockRepository extends BaseRepository implements SubscriptionStockRepositoryInterface
{

    public $modelClass = SubscriptionStock::class;

    public function store(array $data): Model
    {

        $user = auth(GUARD_API)->user();
        $modelName = $user->getMorphClass();

        $fcmToken = parent::store([
            'userId' => $modelName === User::class ? auth(GUARD_API)->user()->userId : null,
            'visitorId' => $modelName === Visitor::class ? auth(GUARD_API)->user()->visitorId : null,
            'varianceId' => $data['varianceId'],
            'productId' => $data['productId'],
            'email' => $data['email'] ?? null,
            'phone' => $data['phone'] ?? null,
        ]);

        return $fcmToken;

    }




}