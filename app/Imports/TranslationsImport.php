<?php

namespace App\Imports;

use App\Repositories\Language\LanguageRepositoryInterface;
use App\Repositories\TranslationKey\TranslationKeyRepositoryInterface;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Events\BeforeSheet;

class TranslationsImport implements ToCollection, WithValidation, WithStartRow, WithEvents
{
    /**
     * @var string
     */
    protected static string $targetLanguage;

    /**
     * @var string
     */
    protected static string $sourceLanguage;

    /**
     * @param TranslationKeyRepositoryInterface $keyRepository
     * @param LanguageRepositoryInterface $languageRepository
     */
    public function __construct(
        protected TranslationKeyRepositoryInterface $keyRepository,
        protected LanguageRepositoryInterface $languageRepository
    ) {
    }

    /**
     * @param Collection $rows
     * @return void
     */
    public function collection(Collection $rows)
    {
        foreach ($rows->lazy() as $row) {
            if ($row[2]) {
                $values = collect();

                $translationKey = $this->keyRepository->findByDottedKey($row[0]);

                $languages = $this->keyRepository->findByIdWithLanguages($translationKey->id)->languages->map(
                    function ($language) use ($row) {
                        $value = $language->pivot?->value?->value;

                        if ($language->abbreviation == static::$targetLanguage) {
                            $value = $row[2];
                        }

                        return [
                            'language_id' => $language->pivot?->language?->id,
                            'abbreviation' => $language->abbreviation,
                            'value' => $value,
                        ];
                    }
                );

                $values->put('group', $translationKey->group);
                $values->put('key', $translationKey->key);
                $values->put('publishedAt', $translationKey->publishedAt);
                $values->put('placeholders', $translationKey->placeholders);
                $values->put('languages', $languages->toArray());

                $this->keyRepository->updateFromMultiLanguages($translationKey->id, $values->toArray(), false);
            }
        }
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            // 0 => function ($attribute, $value, $fail) {
            //     $exists = $this->keyRepository->findByDottedKey($value);
            //     if (!$exists) {
            //         $fail("The passed key $value not exists");
            //     }
            // }
        ];
    }

    /**
     * @return array[]
     */
    public function registerEvents(): array
    {
        return [
            BeforeSheet::class => [self::class, 'beforeSheet'],
        ];
    }

    /**
     * @param BeforeSheet $event
     * @return void
     */
    public static function beforeSheet(BeforeSheet $event)
    {
        $sheet = $event->getSheet();
        static::$targetLanguage = $sheet->getCell('B2')->getValue();
        static::$sourceLanguage = $sheet->getCell('B1')->getValue();
    }

    /**
     * @return int
     */
    public function startRow(): int
    {
        return 4;
    }

}