<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Traits\Models\CustomInteractsWithMedia;
use Carbon\Carbon;
use App\Traits\Models\Lookable;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use Spatie\MediaLibrary\HasMedia;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;
use Spatie\MediaLibrary\InteractsWithMedia;
// use Laravel\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

/**
 * Class User
 *
 * @property int $userId
 * @property string $name
 * @property string $email
 * @property Carbon|null $emailVerifiedAt
 * @property string $password
 * @property string|null $two_factor_secret
 * @property string|null $two_factor_recovery_codes
 * @property Carbon|null $two_factor_confirmed_at
 * @property string|null $remember_token
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Collection|Cart[] $carts
 * @property Collection|Rating[] $ratings
 * @property Collection|Transaction[] $transactions
 *
 * @package App\Models
 */
class User extends Authenticatable implements HasMedia
{

	use HasApiTokens, HasFactory, CustomInteractsWithMedia, Lookable, Notifiable, SoftDeletes, HasFilters, Searchable, HasRoles;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'users';
	protected $primaryKey = 'userId';
	protected $perPage = 24;
	public static $snakeAttributes = false;
	public $timestamps = false;

	public $imagesCollection = 'avatar';

	protected $appends = ['fullName'];

	protected $dates = [
		'emailVerifiedAt',
		'otpVerifiedAt',
		'two_factor_confirmed_at',
		'deleted_at',
		'date'
	];

	protected $casts = [
		'phone' => 'json',
		'birthday' => 'date',

	];

	protected $hidden = [
		'password',
		'two_factor_secret',
		'remember_token'
	];


	protected $fillable = [
		'firstName',
		'lastName',
		'email',
		'phone',
		'mediaId',
		'gender',
		'birthday',
		'status',
		'emailVerifiedAt',
		'password',
		'two_factor_secret',
		'two_factor_recovery_codes',
		'two_factor_confirmed_at',
		'remember_token',
		'wallet',
		'numberOfOrders',
	];
	public function getFullNameAttribute()
	{
		return ucfirst($this->firstName) . ' ' . ucfirst($this->lastName);
	}

	public function carts(): HasMany
	{
		return $this->hasMany(Cart::class, 'userId');
	}

	public function addresses(): HasMany
	{
		return $this->hasMany(Address::class, 'userId');
	}

	public function ratings(): HasMany
	{
		return $this->hasMany(Rating::class, 'userId');
	}

	public function transactions(): HasMany
	{
		return $this->hasMany(Transaction::class, 'userId');
	}


	public function wishlistsIds()
	{
		return $this->belongsToMany(Product::class, 'wishlists', 'userId', 'productId')->pluck('productId');
	}
	public function wishlists()
	{
		return $this->belongsToMany(Product::class, 'wishlists', 'userId', 'productId')
			->withPivot('wishlistId')->withTimestamps();
	}

	public function allowedFilters(): array
	{
		return [];

	}

	public function getLookupResourceConfig(): array
	{
		return [
			'text_column' => 'fullName',
			'value_column' => 'userId',

		];
	}



	public function allowedSearchAttributes(): array
	{
		return [
			'firstName',
			'lastName',
			'email',
			'phone',
			'createdAt',
			'updatedAt',

		];
	}

}