<?php

namespace App\Models;

use App\Traits\HasTranslations;
use App\Traits\Models\Searchable;
use App\Traits\Models\HasFilters;
use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin Builder
 */
class Page extends BaseModel
{
    use HasTranslations, Searchable, HasFilters;
    const UPDATED_AT = 'updatedAt';
    const CREATED_AT = 'createdAt';
    protected $table = 'pages';
    protected $primaryKey = 'pageId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $translatable = ['name', 'body', 'metaTitle', 'metaDescription'];

    protected $casts = [
        'name' => 'json',
        'slug' => 'string',
        'body' => 'json',
        'metaTitle' => 'json',
        'metaDescription' => 'json',
        'source' => 'json'
    ];

    protected $fillable = [
        'name',
        'slug',
        'body',
        'metaTitle',
        'metaDescription',
        'source'
    ];


    public function allowedFilters(): array
    {
        return [];

    }


    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',
            'createdAt',
            'updatedAt',

        ];
    }

}