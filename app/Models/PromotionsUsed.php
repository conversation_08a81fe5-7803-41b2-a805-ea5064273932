<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class PromotionsUsed
 * 
 * @property int $promotionsUsedId
 * @property int $orderId
 * @property int $promotionId
 * @property array $snapshot
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 * 
 * @property Order $order
 * @property Promotion $promotion
 *
 * @package App\Models
 */
class PromotionsUsed extends BaseModel
{
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'promotions_used';
	protected $primaryKey = 'promotionsUsedId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'orderId' => 'int',
		'promotionId' => 'int',
		'snapshot' => 'json'
	];

	protected $fillable = [
		'orderId',
		'promotionId',
		'snapshot'
	];

	public function order(): BelongsTo
	{
		return $this->belongsTo(Order::class, 'orderId');
	}

	public function promotion(): BelongsTo
	{
		return $this->belongsTo(Promotion::class, 'promotionId');
	}
}