<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Traits\Models\CustomInteractsWithMedia;
use Carbon\Carbon;
use App\Traits\HasTranslations;
use App\Traits\Models\Lookable;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use Spatie\MediaLibrary\HasMedia;
use App\Models\Filters\StatusFilter;
use App\Models\Filters\OrderByFilter;
use App\Models\Scopes\GlobalParamQuery;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class PaymentsMethod
 *
 * @property int $paymentMethodId
 * @property array $name
 * @property int $model
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Collection|Order[] $orders
 * @property Collection|Transaction[] $transactions
 *
 * @package App\Models
 */
class PaymentsMethod extends BaseModel implements HasMedia
{
	use HasFactory, HasTranslations, HasFilters, CustomInteractsWithMedia, Searchable, Lookable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'payments_methods';
	protected $primaryKey = 'paymentMethodId';
	protected $perPage = 24;
	public static $snakeAttributes = false;
	public $allowPluckPrimaryKey = true;
	public $translatable = ['name'];

	public $imagesCollection = 'logo';

	protected $casts = [
		'name' => 'json',
	];

	protected $fillable = [
		'name',
		'module',
		'status',
		'allowFillWallet',
	];

	public static function boot()
	{
		parent::boot();
		static::addGlobalScope(new GlobalParamQuery());
	}

	public function orders(): HasMany
	{
		return $this->hasMany(Order::class, 'paymentMethodId');
	}

	public function transactions(): HasMany
	{
		return $this->hasMany(Transaction::class, 'paymentMethodId');
	}

	public function allowedFilters(): array
	{
		return [
			'status' => StatusFilter::class,
		];
	}

	public function allowedSearchAttributes(): array
	{
		return [
			'name->ar',
			'name->en',
			'module',
			'createdAt',
			'updatedAt',

		];
	}


	public function getLookupResourceConfig(): array
	{
		return [
			'text_column' => 'name',
			'value_column' => 'paymentMethodId',
			'meta' => [
				'model',

			]
		];
	}

}