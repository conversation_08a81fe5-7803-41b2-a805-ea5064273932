<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Traits\HasTranslations;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Stock
 *
 * @property int $stockId
 * @property float $price
 * @property float|null $cost
 * @property int $quantity
 * @property int $maxPerUser
 * @property Carbon|null $publishedAt
 * @property Carbon|null $unPublishedAt
 * @property int $sort
 * @property bool $isPublished
 * @property int $supplierId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Supplier $supplier
 * @property Collection|Bundle[] $bundles
 * @property Collection|OrderItem[] $orderItems
 * @property Collection|Variance[] $variances
 *
 * @package App\Models
 */
class Stock extends BaseModel
{
	use HasFactory, HasTranslations;

	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'stocks';
	protected $primaryKey = 'stockId';
	protected $perPage = 24;
	public static $snakeAttributes = false;
	public $translatable = ['note'];




	protected $casts = [
		'cost' => 'float',
		'price' => 'float',
		'quantity' => 'int',
		'maxPerUser' => 'int',
		'sort' => 'int',
		'isPublished' => 'bool',
		'supplierId' => 'int',
		'isOffer' => 'bool',
		'priceBeforeOffer' => 'float',
		'sold' => 'int',
		'isPreOrder' => 'bool',
		'note' => 'json',
	];

	protected $dates = [
		'publishedAt',
		'unPublishedAt'
	];

	protected $fillable = [
		'price',
		'cost',
		'quantity',
		'maxPerUser',
		'publishedAt',
		'unPublishedAt',
		'sort',
		'isPublished',
		'supplierId',
		'isOffer',
		'sold',
		'priceBeforeOffer',
		'isPreOrder',
		'note'
	];



	protected static function booted()
	{

		parent::boot();
		// static::saved(function ($stock) {
		// 	foreach ($stock->variances as $variance) {
		// 		if (!is_null($variance->product)) {
		// 			$variance->product->load('variances', 'variances.stocks', 'variances.activeStock', 'stocks', 'activeStock');
		// 			$variance->product->saveQuietly();
		// 			$variance->product->touch();
		// 		}

		// 	}
		// });

		// static::created(function ($stock) {
		// 	foreach ($stock->variances as $variance) {
		// 		if (!is_null($variance->product)) {
		// 			$variance->product->load('variances', 'variances.stocks', 'variances.activeStock', 'stocks', 'activeStock');
		// 			$variance->product->saveQuietly();
		// 			$variance->product->touch();
		// 		}
		// 	}
		// });

		// static::updated(function ($stock) {
		// 	foreach ($stock->variances as $variance) {
		// 		if (!is_null($variance->product)) {
		// 			$variance->product->load('variances', 'variances.stocks', 'variances.activeStock', 'stocks', 'activeStock');
		// 			$variance->product->saveQuietly();
		// 			$variance->product->touch();
		// 		}
		// 	}
		// });

	}



	public function supplier(): BelongsTo
	{
		return $this->belongsTo(Supplier::class, 'supplierId');
	}

	public function bundles(): BelongsToMany
	{
		return $this->belongsToMany(Bundle::class, 'bundles_stocks', 'stockId', 'bundleId')
			->withPivot('bundleStockId')
			->withTimestamps();
	}

	public function orderItems(): HasMany
	{
		return $this->hasMany(OrderItem::class, 'stockId');
	}

	public function variances(): BelongsToMany
	{
		return $this->belongsToMany(Variance::class, 'variances_stocks', 'stockId', 'varianceId')
			->withPivot('varianceStockId')
			->withTimestamps();
	}





}