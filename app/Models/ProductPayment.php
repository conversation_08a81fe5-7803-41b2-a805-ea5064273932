<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasOne;


/**
 * Class PaymentsMethod
 *
 * @property int $productPaymentId
 * @property int $productId
 * @property int $paymentMethodId
 * @property int $isPublished
 * @property int $publishedAt
 * @property int $unPublishedAt
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 *
 * @package App\Models
 */
class ProductPayment extends BaseModel
{

    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'product_payments';
    protected $primaryKey = 'productPaymentId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $allowPluckPrimaryKey = true;




    protected $fillable = [
        'productId',
        'paymentMethodId',
        'isPublished',
        'publishedAt',
        'unPublishedAt'

    ];



    public function product(): HasOne
    {
        return $this->hasOne(Product::class, 'productId');
    }

    public function paymentMethod(): HasOne
    {
        return $this->hasOne(PaymentsMethod::class, 'paymentMethodId');
    }


}