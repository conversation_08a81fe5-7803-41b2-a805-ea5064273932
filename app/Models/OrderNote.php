<?php

/**
 * Created by Reliese Model.
 */




namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


/**
 * Class OrderDelivery
 * 
 * @property int $orderItemsId
 * @property int $orderId
 * @property int $shippingCarrierId
 * @property int $priceId
 * @property string $note
 * @property Carbon $createdAt
 * @property Carbon $updatedAt

 *
 * @package App\Models
 */
class OrderNote extends BaseModel
{
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	const DELETED_AT = 'deletedAt';
	protected $table = 'order_nots';
	protected $primaryKey = 'orderNoteId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'orderId' => 'int',
		'note' => 'string',
		'userId' => 'int',
		'visitorId' => 'int'
	];

	protected $fillable = [
		'orderId',
		'note',
		'userId',
		'visitorId',
	];

	public function order(): BelongsTo
	{
		return $this->belongsTo(Order::class, 'orderId');
	}


	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'userId');
	}

	public function visitor(): BelongsTo
	{
		return $this->belongsTo(Visitor::class, 'visitorId');
	}






}