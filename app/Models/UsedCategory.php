<?php
/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use App\Filters\SearchFilter;
use App\Traits\HasTranslations;
use App\Traits\Models\Lookable;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use Spatie\MediaLibrary\HasMedia;
use App\Models\Filters\UsedCategoriesBrandsFilter;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * Class Category
 *
 * @property int $usedCategoryId
 * @property String $name
 * @property int|null $parentId
 * @property string $type
 * @property Carbon $HasMedia
 * @property Carbon $updatedAt
 *
 * @property Category|null $category
 * @property Collection|Category[] $categories
 *
 * @package App\Models
 */
class UsedCategory extends BaseModel implements HasMedia
{
    use HasTranslations, HasFactory, Lookable, HasTranslations, InteractsWithMedia, HasFilters, Searchable;

    const UPDATED_AT = 'updatedAt';
    const CREATED_AT = 'createdAt';
    protected $table = 'used_categories';
    protected $primaryKey = 'usedCategoryId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $translatable = ['name', 'slug', 'metaTitle', 'metaDescription'];
    public $imagesCollection = 'cover';

    protected $casts = [
        'parentId' => 'int',
        'name' => 'json',
        'slug' => 'json',
        'metaTitle' => 'json',
        'metaDescription' => 'json',
    ];

    protected $fillable = [
        'name',
        'parentId',
        'slug',
        'metaTitle',
        'metaDescription',
    ];


    public function usedProducts(): HasMany
    {
        return $this->hasMany(UsedProduct::class, 'usedCategoryId');
    }


    public function usedCategoriesAttributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'used_categories_attributes', 'usedCategoryId', 'attributeId')
            ->withPivot('usedCategoryAttributeId')
            ->withTimestamps();
    }


    public function usedCategoriesBrands(): BelongsToMany
    {
        return $this->belongsToMany(Brand::class, 'used_categories_brands', 'usedCategoryId', 'brandId')
            ->withPivot('usedCategoryBrandId')
            ->withTimestamps();

    }



    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'name',
            'value_column' => 'usedCategoryId',
        ];
    }


    public function allowedFilters(): array
    {
        return [

        ];
    }

    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',
            'usedCategoryId',
            'createdAt',
            'updatedAt',

        ];
    }

    public function parent()
    {
        return $this->belongsTo(UsedCategory::class, 'parentId');
    }

    public function children(): HasMany
    {
        return $this->hasMany(UsedCategory::class, 'parentId');
    }

}
