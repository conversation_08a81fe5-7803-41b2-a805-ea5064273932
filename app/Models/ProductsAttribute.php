<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Traits\HasTranslations;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class ProductsAttribute
 *
 * @property int $productsAttributesId
 * @property int $productId
 * @property int $attributeId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Attribute $attribute
 * @property Product $product
 *
 * @package App\Models
 */
class ProductsAttribute extends BaseModel
{
	use HasFactory, HasTranslations;

	public $translatable = ['name'];
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'products_attributes';
	protected $primaryKey = 'productsAttributesId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'productId' => 'int',
		'attributeId' => 'int',
		"attributeValuesId" => "int", // id row in attribute value  for primraray key 
		"usedProductId" => "int"
	];

	protected $fillable = [
		'productId',
		'attributeId',
		'usedProductId'
	];

	public function attribute(): BelongsTo
	{
		return $this->belongsTo(Attribute::class, 'attributeId');
	}

	public function product(): BelongsTo
	{
		return $this->belongsTo(Product::class, 'productId');
	}


	public function attributeValue(): BelongsTo
	{
		return $this->belongsTo(AttributesValue::class, 'attributeValuesId');
	}
}