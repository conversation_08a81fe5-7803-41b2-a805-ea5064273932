<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin Builder
 */
class ProductVisit extends BaseModel
{
    protected $table = 'product_visits';
    protected $primaryKey = 'id';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $fillable = [
        'userId',
        'visitorId',
        'productId',
        'ipAddress',
        'userAgent',
    ];

    protected $casts = [
        'userId' => 'int',
        'visitorId' => 'int',
        'productId' => 'int',
        'ipAddress' => 'string',
        'userAgent' => 'string',
    ];
}