<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use App\Models\Filters\StatusFilter;

/**
 * Class Rating
 *
 * @property int $ratingId
 * @property string $review
 * @property int $rating
 * @property int $userId
 * @property int $model_id
 * @property string $model_type
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property User $user
 *
 * @package App\Models
 */
class ModelRating extends BaseModel
{
	use HasFactory, HasFilters, Searchable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'rating_model';
	protected $primaryKey = 'ratingModelId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'rating' => 'int',
		'userId' => 'int',
		'model_type' => 'string',
		'model_id' => 'int',
		'status' => 'string',
		'review' => 'string',
		'createdAt' => 'datetime',
		'updatedAt' => 'datetime',
	];

	protected $fillable = [
		'review',
		'rating',
		'userId',
		'model_id',
		'model_type',
		'status',
	];

	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'userId');
	}

	public function model()
	{
		return $this->morphTo(null, 'model_type', 'model_id');
	}


	public function allowedFilters(): array
	{
		return [
			'status' => StatusFilter::class,
		];
	}

	public function allowedSearchAttributes(): array
	{
		return [
			'ratingModelId',
			'review',
			'rating',
			'status',
			'createdAt',
			'updatedAt',

		];
	}


}