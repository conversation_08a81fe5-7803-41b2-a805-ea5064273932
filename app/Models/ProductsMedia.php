<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class ProductsMedia
 *
 * @property int $productMediaId
 * @property int $productId
 * @property int $mediaId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Medium $medium
 * @property Product $product
 *
 * @package App\Models
 */
class ProductsMedia extends BaseModel
{

	use HasFactory;

	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'products_media';
	protected $primaryKey = 'productMediaId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'productId' => 'int',
		'mediaId' => 'int'
	];

	protected $fillable = [
		'productId',
		'mediaId'
	];

	public function media(): BelongsTo
	{
		return $this->belongsTo(Media::class, 'mediaId');
	}

	public function product(): BelongsTo
	{
		return $this->belongsTo(Product::class, 'productId');
	}
}