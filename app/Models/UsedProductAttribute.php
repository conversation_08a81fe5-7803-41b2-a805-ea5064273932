<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class ProductsAttribute
 *
 * @property int $usedProductAttributeId
 * @property int $usedProductId
 * @property int $attributeId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Attribute $attribute
 * @property usedProduct $product
 *
 * @package App\Models
 */
/**
 * @mixin Builder
 */
class UsedProductAttribute extends BaseModel
{

    use HasFactory;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'used_products_attributes';
    protected $primaryKey = 'usedProductAttributeId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'usedProductId' => 'int',
        'attributeId' => 'int'
    ];

    protected $fillable = [
        'usedProductId',
        'attributeId'
    ];


    public function attribute(): BelongsTo
    {
        return $this->belongsTo(Attribute::class, 'attributeId');
    }

    public function usedProduct(): BelongsTo
    {
        return $this->belongsTo(UsedProduct::class, 'usedProductId');
    }
}