<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class VariancesStock
 *
 * @property int $productStockId
 * @property int $productId
 * @property int $stockId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Stock $stock
 * @property Product $product
 *
 * @package App\Models
 */
class ProductStock extends BaseModel
{

	use HasFactory;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'product_stocks';
	protected $primaryKey = 'productStockId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'productId' => 'int',
		'stockId' => 'int'
	];

	protected $fillable = [
		'productId',
		'stockId'
	];


	protected static function booted()
	{

		parent::boot();

		// static::deleted(function ($productStock) {
		// 	if (!is_null($productStock->product)) {
		// 		// Forcibly deleting the variance, remove from Meilisearch
		// 		$productStock->product->searchable();
		// 	}
		// });
		// static::updated(function ($productStock) {
		// 	if (!is_null($productStock->product)) {
		// 		// Forcibly deleting the variance, remove from Meilisearch
		// 		$productStock->product->searchable();
		// 	}
		// });
		// static::saved(function ($productStock) {
		// 	if (!is_null($productStock->product)) {
		// 		// Forcibly deleting the variance, remove from Meilisearch
		// 		$productStock->product->searchable();
		// 	}
		// });
	}



	public function stock(): BelongsTo
	{
		return $this->belongsTo(Stock::class, 'stockId');
	}

	public function product(): BelongsTo
	{
		return $this->belongsTo(Product::class, 'productId');
	}
}