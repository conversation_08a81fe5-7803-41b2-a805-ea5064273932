<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class SuggestedProduct
 *
 * @property int $suggestedProductId
 * @property int $sourceProductId
 * @property int $targetProductId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Product $product
 *
 * @package App\Models
 */
class SuggestedProduct extends BaseModel
{
	use HasFactory;

	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'suggested_products';
	protected $primaryKey = 'suggestedProductId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'sourceProductId' => 'int',
		'targetProductId' => 'int'
	];

	protected $fillable = [
		'sourceProductId',
		'targetProductId'
	];

	public function product(): BelongsTo
	{
		return $this->belongsTo(Product::class, 'targetProductId');
	}
}