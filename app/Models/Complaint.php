<?php

namespace App\Models;

use App\Traits\HasTranslations;
use App\Traits\Models\Searchable;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin Builder
 */
class Complaint extends BaseModel
{
    use HasTranslations, Searchable, HasFilters, Lookable;
    const UPDATED_AT = 'updatedAt';
    const CREATED_AT = 'createdAt';
    protected $table = 'complaints';
    protected $primaryKey = 'complaintId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $translatable = ['text'];

    protected $casts = [
        'text' => 'json',
        'groupKey' => 'string',
    ];

    protected $fillable = [
        'text',
        'groupKey',
    ];


    public function allowedFilters(): array
    {
        return [];

    }


    public function allowedSearchAttributes(): array
    {
        return [
            'text->ar',
            'text->en',
            'createdAt',
            'updatedAt',

        ];
    }

    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'text',
            'value_column' => 'complaintId',
        ];
    }

}