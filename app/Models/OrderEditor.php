<?php

/**
 * Created by Reliese Model.
 */




namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class OrderDelivery
 * 
 * @property int $orderItemsId
 * @property int $orderId
 * @property int $shippingCarrierId
 * @property int $priceId
 * @property string $note
 * @property Carbon $createdAt
 * @property Carbon $updatedAt

 *
 * @package App\Models
 */
class OrderEditor extends BaseModel
{
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'order_editors';
	protected $primaryKey = 'orderEditorId';


	protected $casts = [
		'orderId' => 'int',
		'userId' => 'int',

	];

	protected $dates = [
		'startAt',
		'endAt',
	];
	protected $fillable = [
		'orderId',
		'userId',
		'startAt',
		'endAt',

	];

	public function order(): BelongsTo
	{
		return $this->belongsTo(Order::class, 'orderId');
	}


	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'userId');
	}




}