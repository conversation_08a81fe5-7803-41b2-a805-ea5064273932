<?php

namespace App\Models\Translations;

use App\Helper\CacheHelper;
use App\Models\Language;
use App\Models\BaseModel;
use App\Traits\Models\HasFilters;
use App\Filters\IsPublishedFilter;
use App\Filters\Translation\Group;
use App\Filters\Translation\IsMissing;
use Illuminate\Database\Eloquent\Builder;
use App\Filters\AbbreviationRelationFilter;
use App\Traits\Models\Searchable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

/**
 * @mixin Builder
 */
class TranslationKey extends BaseModel
{
    use HasFilters;
    use Searchable;
    // use CacheableModel;
    use SoftDeletes, HasFactory;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';

    protected $casts = [
        'placeholders' => 'array',
        'publishedAt' => 'datetime',
    ];

    public $keyCacheShared = 'shared.translations';
    public $patternCache = '*translations*';
    protected CacheHelper $cacheHelper;



    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        // Initialize the cacheHelper here
        $this->cacheHelper = app(CacheHelper::class); // Assuming CacheHelper is bound in the service container
    }


    public function resetCache()
    {
        $this->cacheHelper->deletePattern($this->patternCache);
        $this->cacheHelper->remember(
            key: $this->keyCacheShared,
            callback: function () {
                return $this->filters()
                    ->get();
            },
            tags: [],
            ttl: $this->minutes,
        );
    }

    protected static function boot()
    {
        parent::boot();

        static::deleting(function (self $key) {
            $key->languages()->detach();
            $key->values()->detach();
            $key->tags()->detach();
        });


        static::creating(function ($model) {
            $model->resetCache();
        });

        static::updating(function ($model) {
            $model->resetCache();
        });

        static::saved(function ($model) {
            $model->resetCache();
        });

        static::deleted(function ($model) {
            $model->resetCache();
        });


    }

    public function allowedFilters(): array
    {
        return [
            'isPublished' => IsPublishedFilter::class,
            'group' => Group::class,
            'abbreviation' => AbbreviationRelationFilter::class,
            'is_missing' => IsMissing::class,
        ];
    }

    public function allowedSearchAttributes(): array
    {

        return [
            'group',
            'key',
            // 'languages.abbreviation',
            'values.value',
        ];
    }

    public function languages(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                Language::class,
                'language_has_translations',
                'key_id',
                'language_id'
            )->withPivot(['value_id'])
            ->using(LanguageHasTranslation::class);
    }

    public function values(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                TranslationValue::class,
                'language_has_translations',
                'key_id',
                'value_id'
            )->withPivot(['language_id'])
            ->using(LanguageHasTranslation::class);
    }

    public function value(): BelongsToMany
    {
        // get translation value through the pivot LanguageHasTranslation::class where language_has_translations.key_id =


    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(
            TranslationTag::class,
            'tag_has_translation_keys',
            'key_id',
            'tag_id'
        );
    }

    public function scopePublished($query)
    {
        return $query->whereNotNull('publishedAt');
    }
    /* public function getLookupResourceConfig(): array
     {
         return [
            // 'text_column' => 'value',
             'value_column' => 'id',
         ];
     }
     */


}