<?php

namespace App\Models\Translations;

use AjCastro\EagerLoadPivotRelations\EagerLoadPivotTrait;
use App\Models\Language;
use Illuminate\Database\Eloquent\Builder;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @mixin Builder
 */
class TranslationValue extends BaseModel
{
    protected $casts = [
        'value' =>'json'
    ];

    use SoftDeletes, EagerLoadPivotTrait;

    use HasFactory;

    public function keys(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                TranslationKey::class,
                'language_has_translations',
                'value_id',
                'key_id'
            );
    }

    public function languages(): BelongsToMany
    {
        return $this
            ->belongsToMany(
                Language::class,
                'language_has_translations',
                'value_id',
                'language_id'
            );
    }
}