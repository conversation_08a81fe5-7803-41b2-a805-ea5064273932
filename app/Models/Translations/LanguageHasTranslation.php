<?php

namespace App\Models\Translations;

use App\Enums\DatabaseConnectionsEnum;
use App\Models\Language;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class LanguageHasTranslation extends Pivot
{

    use HasFactory;
    protected $with = ['language'];

    public function value(): BelongsTo
    {
        return $this->belongsTo(TranslationValue::class, 'value_id');
    }

    public function key(): BelongsTo
    {
        return $this->belongsTo(TranslationKey::class, 'key_id');
    }

    public function language(): BelongsTo
    {

        return  $this->belongsTo(Language::class, 'language_id');
    }
}