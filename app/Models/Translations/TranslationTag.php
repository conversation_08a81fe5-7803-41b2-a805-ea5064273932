<?php

namespace App\Models\Translations;

use App\Models\BaseModel;
use App\Traits\Models\Lookable;
use App\Traits\Models\Searchable;
use App\Traits\Models\HasFilters;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @mixin Builder
 */
class TranslationTag extends BaseModel
{

    use SoftDeletes, HasFactory, Lookable, Searchable, HasFilters;


    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';

    public function Translations(): BelongsToMany
    {
        return $this->belongsToMany(
            TranslationKey::class,
            'tags_has_translations',
            'tag_id',
            'translation_id'
        );
    }
    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'name',
            'value_column' => 'id',
        ];
    }
    public function allowedFilters(): array
    {
        return [];

    }

    public function allowedSearchAttributes(): array
    {
        return [
            'name',
            'createdAt',
            'updatedAt',

        ];
    }

}