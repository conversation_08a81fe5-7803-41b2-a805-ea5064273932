<?php

namespace App\Models;

use Carbon\Carbon;
use App\Helper\Currency as CurrencyHelper;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * Class Price
 *
 * @property int $priceId
 * @property double $price
 * @property double $basePrice
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Currency|null $currency
 *
 * @package App\Models
 */
class Price extends BaseModel
{

    use HasFactory;

    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'prices';
    protected $primaryKey = 'priceId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    public static function boot()
    {

        parent::boot();

        static::creating(function ($model) {
            // Logic to execute when the model is being created
            $model->basePrice = CurrencyHelper::convert((float) $model->price)->from($model->currencyId)->toBase()->get();
        });

        static::updating(function ($model) {
            // Logic to execute when the model is being updated
            $model->basePrice = CurrencyHelper::convert((float) $model->price)->from($model->currencyId)->toBase()->get();
        });

        static::saved(function ($model) {
            // Logic to execute when the model is being saved
            $model->basePrice = CurrencyHelper::convert((float) $model->price)->from($model->currencyId)->toBase()->get();

        });


    }

    protected $casts = [
        'price' => 'float',
        'basePrice' => 'float',
        'currencyId' => 'int',
    ];
    protected $fillable = [
        'price',
        'basePrice',
        'currencyId'
    ];
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currencyId');
    }


}