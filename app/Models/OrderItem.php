<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class OrderItem
 *
 * @property int $orderItemsId
 * @property int $orderId
 * @property int $productId
 * @property int $varianceId
 * @property int $stockId
 * @property array $snapshot
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Order $order
 * @property Product $product
 * @property Stock $stock
 * @property Variance $variance
 *
 * @package App\Models
 */
class OrderItem extends BaseModel
{
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    const DELETED_AT = 'deletedAt';
    protected $table = 'order_items';
    protected $primaryKey = 'orderItemsId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'orderId' => 'int',
        'model_type' => 'string',
        'model_id' => 'int',
        'status' => 'string',
        'cost' => 'float'
    ];

    protected $fillable = [
        'orderId',
        'model_type',
        'model_id',
        'status',
        'cost'
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'orderId');
    }


    public function model()
    {
        return $this->morphTo(null, 'model_type', 'model_id');
    }




}