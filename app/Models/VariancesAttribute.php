<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Traits\HasTranslations;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class VariancesAttribute
 *
 * @property int $varianceAttributeId
 * @property int $varianceId
 * @property int $attributeId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Attribute $attribute
 * @property Variance $variance
 *
 * @package App\Models
 */
class VariancesAttribute extends BaseModel
{

	use HasFactory, HasTranslations;

	public $translatable = ['name', 'prefix', 'suffix', 'option_name'];

	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'variances_attributes';
	protected $primaryKey = 'varianceAttributeId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'varianceId' => 'int',
		'attributeId' => 'int',
		"attributeValuesId" => "int", // id row in attribute value  for primraray key to get value without sub qeray
		'extra' => 'json',
	];

	protected $fillable = [
		'varianceId',
		'attributeId'
	];

	public function attribute(): BelongsTo
	{
		return $this->belongsTo(Attribute::class, 'attributeId');
	}

	public function variance(): BelongsTo
	{
		return $this->belongsTo(Variance::class, 'varianceId');
	}


	public function attributeValue(): BelongsTo
	{
		return $this->belongsTo(AttributesValue::class, 'attributeValuesId');
	}

	public function option(): BelongsTo
	{
		return $this->belongsTo(AttributesOption::class, 'attributeOptionId');
	}
}