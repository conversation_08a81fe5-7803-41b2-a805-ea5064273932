<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class VariancesStock
 *
 * @property int $varianceStockId
 * @property int $varianceId
 * @property int $stockId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Stock $stock
 * @property Variance $variance
 *
 * @package App\Models
 */
class VariancesStock extends BaseModel
{

	use HasFactory;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'variances_stocks';
	protected $primaryKey = 'varianceStockId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'varianceId' => 'int',
		'stockId' => 'int'
	];

	protected $fillable = [
		'varianceId',
		'stockId'
	];


	protected static function booted()
	{

		parent::boot();

		// static::deleted(function ($variancesStock) {
		// 	if (!is_null($variancesStock->variance->product)) {
		// 		// Forcibly deleting the variance, remove from Meilisearch
		// 		$variancesStock->variance->product->searchable();
		// 	}
		// });
		// static::updated(function ($variancesStock) {
		// 	if (!is_null($variancesStock->variance->product)) {
		// 		// Forcibly deleting the variance, remove from Meilisearch
		// 		$variancesStock->variance->product->searchable();
		// 	}
		// });
		// static::saved(function ($variancesStock) {
		// 	if (!is_null($variancesStock->variance->product)) {
		// 		// Forcibly deleting the variance, remove from Meilisearch
		// 		$variancesStock->variance->product->searchable();
		// 	}
		// });
	}



	public function stock(): BelongsTo
	{
		return $this->belongsTo(Stock::class, 'stockId');
	}

	public function variance(): BelongsTo
	{
		return $this->belongsTo(Variance::class, 'varianceId');
	}
}