<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class VariancesStock
 *
 * @property int $productLabelId
 * @property int $productId
 * @property int $labelId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Label $label
 * @property Product $product
 *
 * @package App\Models
 */
class ProductLabel extends BaseModel
{

	use HasFactory;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'product_labels';
	protected $primaryKey = 'productLabelId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'productId' => 'int',
		'labelId' => 'int'
	];

	protected $fillable = [
		'productId',
		'labelId'
	];


	public function label(): BelongsTo
	{
		return $this->belongsTo(Label::class, 'labelId');
	}

	public function product(): BelongsTo
	{
		return $this->belongsTo(Product::class, 'productId');
	}
}