<?php



namespace App\Models;

use Carbon\Carbon;
use App\Traits\HasTranslations;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class Shipping
 *
 * @property int $shippingOrderId
 * @property int $shippingId 
 * @property int $orderId 
 * @property array $note
 * @property string $status
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 
 * @package App\Models
 */
class ShippingOrder extends BaseModel
{

    use HasFactory, HasTranslations, Lookable;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'shipping_orders';
    protected $primaryKey = 'shippingOrderId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    public $allowPluckPrimaryKey = true;

    public $translatable = ['note'];

    protected $casts = [
        'note' => 'json',

    ];

    protected $fillable = [
        'orderId',
        'note',
        'shippingId',
        'status'

    ];
    public function order(): HasOne
    {
        return $this->hasOne(Order::class, 'orderId');
    }

    // public function shipping(): HasOne
    // {
    //     return $this->hasOne(Shipping::class,'shippingId');
    // }



}