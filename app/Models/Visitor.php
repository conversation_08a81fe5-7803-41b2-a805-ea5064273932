<?php

namespace App\Models;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Lara<PERSON>\Passport\Client;
use Laravel\Passport\HasApiTokens;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Log;

/**
 * Class Wishlist
 *
 * @property int $visitorId
 * @property string $visitorKey
 * @property array $data
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 *
 * @package App\Models
 */

class Visitor extends Authenticatable //BaseModel
{

    use HasApiTokens;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'visitors';
    protected $primaryKey = 'visitorId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $guarded = ['visitorKey'];
    protected $fillable = [
        'visitorKey',
        'data',
    ];

    protected $casts = [
        'visitorKey' => 'string',
        'data' => 'json',
    ];



    public static function makeAuthClient()
    {


        $visitor = null;
        $retryCount = 5;
        $retryDelay = 100; // Milliseconds

        for ($attempt = 0; $attempt < $retryCount; $attempt++) {
            try {
                $visitor = self::where('visitorKey', request()->header('visitor-id'))->first();

                if (is_null($visitor)) {
                    $visitor = self::create(
                        [
                            'visitorKey' => request()->header('visitor-id'),
                            'data' => ['updated_data_key' => 'updated_data_value']
                        ]
                    );
                }

                break; // Success, no need to retry
            } catch (\Illuminate\Database\QueryException $ex) {
                if ($ex->getCode() == 23000 && $attempt < $retryCount - 1) {
                    usleep($retryDelay * 1000); // Wait before retrying
                    continue; // Retry
                }

                throw new \Exception($ex->getMessage(), 1);
            } catch (\Exception $ex) {
                Log::error("Attempt $attempt failed with error: " . $ex->getMessage());
                throw new \Exception($ex->getMessage(), 1);
            }
        }








        if (!is_null($visitor)) {

            if ($visitor->tokens->count()) {
                $existingToken = $visitor->tokens->first();
            } else {
                $visitor->createToken(TOKEN_VISITOR)->accessToken;
                $existingToken = $visitor->load('tokens')->first();
            }

            auth(GUARD_API)->setUser($visitor);
        }



        return $visitor;

    }
}