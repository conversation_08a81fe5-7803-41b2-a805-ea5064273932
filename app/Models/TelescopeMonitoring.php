<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class TelescopeMonitoring
 * 
 * @property string $tag
 *
 * @package App\Models
 */
class TelescopeMonitoring extends BaseModel
{
	protected $table = 'telescope_monitoring';
	public $incrementing = false;
	protected $perPage = 24;
	public $timestamps = false;
	public static $snakeAttributes = false;

	protected $fillable = [
		'tag'
	];
}