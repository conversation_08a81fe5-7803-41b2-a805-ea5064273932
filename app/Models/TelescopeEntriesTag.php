<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class TelescopeEntriesTag
 * 
 * @property string $entry_uuid
 * @property string $tag
 * 
 * @property TelescopeEntry $telescopeEntry
 *
 * @package App\Models
 */
class TelescopeEntriesTag extends BaseModel
{
	protected $table = 'telescope_entries_tags';
	public $incrementing = false;
	protected $perPage = 24;
	public $timestamps = false;
	public static $snakeAttributes = false;

	protected $fillable = [
		'entry_uuid',
		'tag'
	];

	public function telescopeEntry(): BelongsTo
	{
		return $this->belongsTo(TelescopeEntry::class, 'entry_uuid', 'uuid');
	}
}