<?php


namespace App\Models;

use App\Helper\Currency;
use Carbon\Carbon;
use App\Traits\HasTranslations;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class ShippingCarrierPrice
 *
 * @property int $shippingCarrierPriceId
 * @property int $price
 * @property int $cityId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * 
 * @property Collection|Address[] $addresses
 * @package App\Models
 */
class ShippingCarrierPrice extends BaseModel
{

    use HasFactory, Lookable;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'shipping_carrier_prices';
    protected $primaryKey = 'shippingCarrierPriceId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    public $allowPluckPrimaryKey = true;

    protected $casts = [
        'price' => 'float',
        'cityId' => 'int',
        'shippingCarrierId' => 'int'

    ];



    protected $fillable = [
        'price',
        'cityId',
        'shippingCarrierId'
    ];



    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'cityId');
    }

    public function shippingCarrier(): BelongsTo
    {
        return $this->belongsTo(ShippingCarriers::class, 'shippingCarrierId');
    }


    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'name',
            'value_column' => 'shippingCarrierId',
            'meta' => ['slug', 'config']
        ];
    }


}