<?php

/**
 * Created by Reliese Model.
 */




namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class OrderDelivery
 * 
 * @property int $orderItemsId
 * @property int $orderId
 * @property int $shippingCarrierId
 * @property int $priceId
 * @property string $note
 * @property Carbon $createdAt
 * @property Carbon $updatedAt

 *
 * @package App\Models
 */
class OrderDelivery extends BaseModel
{
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	const DELETED_AT = 'deletedAt';
	protected $table = 'order_delivery';
	protected $primaryKey = 'orderDeliveryId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'orderId' => 'int',
		'shippingCarrierId' => 'int',
		'price' => 'float',
		'status' => 'string',
		'note' => 'string',
	];

	protected $fillable = [
		'orderId',
		'shippingCarrierId',
		'float',
		'status',
		'note',
	];

	public function order(): BelongsTo
	{
		return $this->belongsTo(Order::class, 'orderId');
	}




	public function shippingCarrier(): BelongsTo
	{
		return $this->belongsTo(ShippingCarriers::class, 'shippingCarrierId');
	}



}