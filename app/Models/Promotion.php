<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use App\Traits\Models\HasFilters;
use App\Filters\IsPublishedFilter;
use App\Models\Filters\OrderByFilter;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * schema json for rule 
 * 	{
 * 		"type": "fixed", "percentage"
 * 	 	"items": { "ids":[1, 2, 3, 4],"value": 2 } ,"brand": {"ids":[1, 2, 3, 4],"value": 2 } , "category": {"ids":[1, 2, 3, 4],"value": 2 }
 *  }
 */

/**
 * Class Promotion
 * 
 * @property int $promotionId
 * @property array $rule
 * @property string $name
 * @property Carbon|null $startDate
 * @property Carbon|null $endTime
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 * 
 * @property Collection|PromotionsUsed[] $promotionsUseds
 *
 * @package App\Models
 */
class Promotion extends BaseModel
{
	use HasFilters;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'promotions';
	protected $primaryKey = 'promotionId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'rule' => 'json'
	];

	protected $dates = [
		'publishedAt',
		'unPublishedAt'
	];

	protected $fillable = [
		'rule',
		'name',
		'isPublished',
		'publishedAt',
		'unPublishedAt'
	];

	public function promotionsUseds(): HasMany
	{
		return $this->hasMany(PromotionsUsed::class, 'promotionId');
	}



	public function allowedFilters(): array
	{
		return [
			'isPublished' => IsPublishedFilter::class,
			'order_by' => OrderByFilter::class,
		];
	}
}



