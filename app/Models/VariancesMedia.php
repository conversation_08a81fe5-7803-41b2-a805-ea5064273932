<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class VariancesMedia
 *
 * @property int $varianceMediaId
 * @property int $varianceId
 * @property int $mediaId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Media $media
 * @property Variance $variance
 *
 * @package App\Models
 */

class VariancesMedia extends BaseModel
{
    use HasFactory;

    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'variances_media';
    protected $primaryKey = 'varianceMediaId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'varianceId' => 'int',
        'mediaId' => 'int'
    ];

    protected $fillable = [
        'varianceId',
        'mediaId'
    ];


    public function media(): BelongsTo
    {
        return $this->belongsTo(Media::class, 'mediaId');
    }

    public function variance(): BelongsTo
    {
        return $this->belongsTo(Variance::class, 'varianceId');
    }

}