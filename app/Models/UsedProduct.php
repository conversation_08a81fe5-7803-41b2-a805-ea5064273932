<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use App\Traits\HasTranslations;
use App\Traits\Models\Searchable;
use App\Models\Filters\CategoriesBrandsFilter;
use App\Models\Filters\StatusFilter;
use App\Traits\Models\HasFilters;
use App\Models\Scopes\Website\IsPublishedScope;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin Builder
 */
class UsedProduct extends BaseModel implements HasMedia
{

    use HasFactory, HasFilters, HasTranslations, InteractsWithMedia, HasFilters, Searchable;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'used_products';
    protected $primaryKey = 'usedProductId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $translatable = ['description', 'name'];
    protected $casts = [
        'description' => 'json',
        'name' => 'json',
        'slug' => 'string',

    ];
    protected $dates = [
        'publishedAt',
        'unPublishedAt'
    ];


    public $imagesCollection = [
        'gallery',
        'cover'
    ];




    protected $fillable = [
        'brandId',
        'cityId',
        'userId',
        'description',
        'name',
        'slug',
        'price',
        'status',
        'isPublished',
        'publishedAt',
        'unPublishedAt'

    ];

    protected static function booted(): void
    {
        // static::addGlobalScope(new IsPublishedScope);
    }

    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class, 'brandId');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'userId');
    }
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'cityId');
    }

    public function usedCategories(): BelongsToMany
    {
        return $this->belongsToMany(UsedCategory::class, 'categories_used_products', 'usedProductId', 'usedCategoryId')
            ->withPivot('categoriesUsedProductsId')
            ->withTimestamps();
    }





    public function attributesWithValue(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'products_attributes', 'usedProductId', 'attributeId')
            ->join('attributes_values', function ($join) {
                $join->on('attributes_values.usedProductId', '=', 'products_attributes.usedProductId');
                $join->on('attributes_values.attributeId', '=', 'attributes.attributeId');
            })
            ->join('attributes_options', 'attributes_options.attributeOptionId', '=', 'attributes_values.attributeOptionId')
            ->select(
                'attributes.*',
                'attributes_values.*',
                'attributes_options.name as option_name',
                'attributes_options.slug as option_slug',
                'attributes_options.hexCode',
                'attributes_options.number',
                'products_attributes.usedProductId as pivot_usedProductId',
                'products_attributes.attributeId as pivot_attributeId',
                'products_attributes.productsAttributesId as  pivot_productsAttributesId',
                'products_attributes.createdAt as pivot_createdAt',
                'products_attributes.updatedAt as pivot_updatedAt'
            );
    }


    public function categoryUsedProduct(): HasMany
    {
        return $this->hasMany(CategoryUsedProduct::class, 'usedProductId');
    }



    public function usedProductAttribute(): HasMany
    {
        return $this->hasMany(UsedProductAttribute::class, 'usedProductId');
    }

    public function productAttributes(): BelongsToMany
    {
        return $this->belongsToMany(ProductsAttribute::class, 'products_attributes', 'usedProductId', 'attributeId');
    }

    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'used_product_attributes', 'usedProductId', 'attributeId')
            ->withPivot('usedProductAttributeId')
            ->withTimestamps();
    }


    public function attributeValues()
    {
        return $this->hasMany(AttributesValue::class, 'usedProductId');
    }


    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',
            'status',
            'published',
            'publishedAt',
            'unPublishedAt',
            'createdAt',
            'updatedAt',

        ];
    }

    public function allowedFilters(): array
    {
        return [
            'status' => StatusFilter::class,
        ];
    }
}