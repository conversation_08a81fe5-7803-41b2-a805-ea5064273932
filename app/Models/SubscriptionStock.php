<?php
namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Supplier
 *
 * @property int $subscriptionStockId
 * @property int $productId
 * @property int $varianceId
 * @property int $userId
 * @property int $visitorId
 * @property string $email
 * @property array $phone
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @package App\Models
 */
class SubscriptionStock extends BaseModel
{


    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'subscription_stocks';
    protected $primaryKey = 'subscriptionStockId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'productId' => 'int',
        'varianceId' => 'int',
        'userId' => 'int',
        'visitorId' => 'int',
        'email' => 'string',
        'phone' => 'array',
    ];
    protected $fillable = [
        'productId',
        'varianceId',
        'userId',
        'visitorId',
        'email',
        'phone',
    ];


    public function product(): HasMany
    {
        return $this->hasMany(Product::class, 'productId');
    }

    public function variance(): HasMany
    {
        return $this->hasMany(Variance::class, 'varianceId');
    }


}