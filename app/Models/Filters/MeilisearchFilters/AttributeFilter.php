<?php

namespace App\Models\Filters\MeilisearchFilters;

use App\Filters\Interfaces\MeilisearchFiltersInterface;
use App\Filters\Interfaces\WithConfig;
use App\Filters\Interfaces\WithOptions;
use App\Filters\Interfaces\WithType;
use App\Http\Resources\OptionsResource;
use App\Models\Attribute;
use App\Repositories\Attribute\AttributeRepositoryInterface;
use App\Enums\SingleValueComponentTypeEnum;
use Illuminate\Database\Eloquent\Collection;

class AttributeFilter extends BaseFilter implements MeilisearchFiltersInterface, WithOptions, WithType, WithConfig
{

    public $facet = [];

    private $attribute;

    private string $key;
    private $filter;

    private Collection $options;

    private $isMultiple = false;

    function __construct($filter)
    {
        parent::__construct($filter);
        $this->filter = $filter;
        $this->key = $filter->key;
        $this->isMultiple = SingleValueComponentTypeEnum::has($this->filter->componentType);
        $this->attribute = $filter;
        $this->options = $filter->options;
    }


    public function getValue(): mixed
    {
        $key = $this->getKey();
        if (SingleValueComponentTypeEnum::has($this->filter->componentType)) {
            $value = request()->has($key) ? (int) request()->get($key) : null;
        } else {
            // check $this->filter->componentType included in MultiValueComponentTypeEnum (checkbox, radio, select)
            $value = request()->has($key) ? explode(",", request()->get($key)) : null;
            if ($value)
                $value = array_map(fn($v) => (int) $v, $value);
        }

        return $value;
    }

    public function getOptions(): OptionsResource
    {

        $meta = ['facet', 'type', 'number', 'text', 'hexCode'];
        $options = $this->options->map(function ($option, $name) {
            $option->facet = $this->facetsOptions[$option->attributeOptionId] ?? 0;
            $option->type = $this->filter->type;
            // if ($this->filter->key === 'displaytechnology') {
            //     dump($this->filter->key);
            // }

            if ($option->type === 'number') {
                $option->text = $option->number;
            } else if ($option->type === 'color') {
                $option->text = $option->name;
            } else {
                $option->text = $option->name;
            }

            return $option;
        });


        return new OptionsResource(resource: $options, textColumn: 'text', valueColumn: 'attributeOptionId', meta: $meta);
    }

    public static function parse($value): string
    {
        if ($value && is_array($value) && count($value)) {

            $value = implode(',', $value);

            return self::$key . ' IN [' . ($value) . ']';
        } else if ($value && is_numeric($value)) {
            return self::$key . 'IN [' . trim($value) . ']';
        }

        return '';
    }

    public function getFacetName(): string
    {
        return self::$key;
    }

    public function setFacet($value)
    {
        //convert keys to string

        $this->facet = collect($value)->mapWithKeys(function ($item, $key) {
            return [(string) "$key" => $item];
        })->toArray();
    }

    public function getConfig()
    {
        return [
            'prefix' => $this->attribute->prefix,
            'suffix' => $this->attribute->suffix,
        ];
    }

    /*************  ✨ Codeium Command ⭐  *************/
    /**
     * Return the type of this filter.
     * This is used to determine which type of input to display in the frontend.
     *
     * @return string
     */
    /******  b48cd284-b05a-4405-ac70-d3017cc15bde  *******/
    public function getType()
    {
        return $this->filter->type;
    }




}