<?php

namespace App\Models\Filters\MeilisearchFilters;

use App\Filters\Interfaces\MeilisearchFiltersInterface;
use App\Filters\Interfaces\WithType;
use App\Models\Category;
use App\Filters\Interfaces\WithOptions;
use App\Helper\CacheHelper;
use App\Http\Resources\OptionsResource;



class CategoriesFilter extends BaseFilter implements WithOptions, WithType
{
    public $facet = [];

    private $filter;


    private $minutes = 10000;

    function __construct($filter)
    {
        parent::__construct($filter);
        $this->filter = $filter;
    }


    public function getValue(): mixed
    {
        $value = request()->has('categories') ? explode(",", request()->get('categories')) : null;
        return collect($value)->map(function ($item) {
            return (int) trim($item);
        })->toArray();
    }

    public function getOptions(): OptionsResource
    {

        $cacheHelper = new CacheHelper;

        $categories = $cacheHelper->remember(
            key: "categories-filters",
            callback: function () {
                return Category::all();
            },
            tags: [],
            ttl: $this->minutes,
        );

        $matchedCategories = $categories->map(function ($option, $name) {
            // dd($name, $this->facet, $option->categoryId, isset($this->facet[$option->categoryId]));
            $option->facet = $this->facetsOptions[$option->categoryId] ?? 0;
            $option->type = $this->filter->type;

            return $option;
        });


        return new OptionsResource(resource: $matchedCategories, textColumn: 'name', valueColumn: 'categoryId', meta: ['facet', 'type']);
    }

    static function parse($value): string
    {
        if (is_array($value) && count($value)) {
            $q = [];
            foreach ($value as $v) {
                $q[] = 'categories = ' . $v;
            }
            return join(' OR ', $q);
            // return 'categories IN [' . join(',', $value) . ']';
        } else if (is_integer($value) || is_string($value)) {
            return 'categories IN [' . $value . ']';
        }

        return '';
    }

    public function getFacetName(): string
    {
        return 'categories';
    }

    public function setFacet($value)
    {
        $this->facet = $value;
    }

}