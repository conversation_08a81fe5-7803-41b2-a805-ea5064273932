<?php

namespace App\Models\Filters\MeilisearchFilters;

use App\Filters\Interfaces\MeilisearchFiltersInterface;
use App\Filters\Interfaces\WithConfig;
use App\Filters\Interfaces\WithType;
use App\Helper\Currency;



class AvgRateFilter extends BaseFilter implements MeilisearchFiltersInterface, WithType
{

    public $facet = [];
    private $filter;

    function __construct($filter)
    {
        //extend parent
        $this->filter = $filter;
        parent::__construct($filter);

    }

    public function getValue(): mixed
    {
        //make sure its matches number:number
        return request()->has('avgRate') ?? null;
    }

    public static function parse($value): string
    {

        return "avgRate = $value";


    }

    public function getFacetName(): string
    {
        return 'avgRate';
    }

    public function setFacet($value)
    {
        $this->facet = $value;

    }







}