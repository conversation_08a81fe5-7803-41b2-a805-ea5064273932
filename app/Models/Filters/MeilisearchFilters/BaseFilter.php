<?php

namespace App\Models\Filters\MeilisearchFilters;

use App\Filters\Interfaces\MeilisearchFiltersInterface;


class BaseFilter
{



    private $filter;
    private $key;

    protected $facetsOptions;

    private $categoryId;


    function __construct($filter)
    {
        $this->filter = $filter;
    }

    public function getName()
    {
        return $this->filter->name;
    }

    public function setKey($key)
    {
        $this->key = $key;
    }

    public function getKey()
    {
        return $this->key;
    }

    public function setCategory($categoryId)
    {
        $this->categoryId = $categoryId;
    }

    public function getCategory()
    {
        return $this->categoryId;
    }
    public function handle(): string|array
    {
        $key = $this->getKey();

        $value = $this->getValue($key);

        return $this->parse($value);

    }

    public function getType()
    {

        return $this->filter->componentType;
    }
    public function getConfig()
    {

        return $this->filter->config;
    }

    public function getFilter(): mixed
    {
        return $this->filter;
    }


    public function setFacetsOptions($facetsOptions): void
    {
        $this->facetsOptions = $facetsOptions;
    }


}