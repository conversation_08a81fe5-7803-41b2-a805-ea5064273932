<?php

namespace App\Models\Filters\MeilisearchFilters;

use App\Filters\Interfaces\MeilisearchFiltersInterface;
use App\Filters\Interfaces\WithConfig;
use App\Filters\Interfaces\WithType;
use App\Helper\Currency;
use App\Repositories\Filters\FiltersRepositoryInterface;



class PriceFilter extends BaseFilter implements MeilisearchFiltersInterface, WithType, WithConfig
{

    public $facet = [];
    private $filter;



    public function getValue(): mixed
    {
        //make sure its matches number:number
        return request()->has('price') && preg_match("/^\d+:\d+$/", request()->get('price')) ? request()->get('price') : null;
    }

    static function parse($value): string
    {
        if (!$value)
            return '';

        $value = explode(':', $value);
        [$min, $max] = $value;
        //convert min, max to basePrice
        $min = Currency::convert($min)->from(getCurrency())->toBase()->getBasePrice();
        $max = Currency::convert($max)->from(getCurrency())->toBase()->getBasePrice();

        // if (!is_null($min) && !is_null($max)) {
        //     return 'basePrice > ' . $min . ' AND basePrice < ' . $max;
        // } else if (!is_null($min)) {
        //     return 'basePrice > ' . $min;
        // } else if (!is_null($max)) {
        //     return 'basePrice < ' . $max;
        // } else {
        //     return '';
        // }


        if (!is_null($min) && !is_null($max)) {
            return 'minPrice >= ' . $min . ' AND maxPrice <= ' . $max;
        } else if (!is_null($min)) {
            return 'minPrice >= ' . $min;
        } else if (!is_null($max)) {
            return 'maxPrice <= ' . $max;
        } else {
            return '';
        }

    }

    public function getFacetName(): string
    {
        return 'minPrice';
    }

    public function setFacet($value)
    {
        $this->facet = $value;

    }

    public function getConfig()
    {
        $filter = $this->getFilter();

        return [
            'min' => Currency::convert($filter->config['min'])->fromBase()->to(getCurrency())->get(),
            'max' => Currency::convert($filter->config['max'])->fromBase()->to(getCurrency())->get(),

        ];
    }





}