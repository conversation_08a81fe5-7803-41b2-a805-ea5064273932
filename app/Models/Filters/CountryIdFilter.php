<?php

namespace App\Models\Filters;

use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use App\Models\Filters\Interfaces\FilterInterface;
use App\Repositories\Country\CountryRepositoryInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class CountryIdFilter implements FilterInterface, WithOptions
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $countryId = $this->getValue();
        return is_null($countryId) ? $next($query) : $next($query)->where('countryId', $countryId);
    }

    public function getValue(): mixed
    {

        return request()->has('countryId') ? request()->integer('countryId') : null;
    }

    public function getOptions(): OptionsResource
    {
        $options = resolve(CountryRepositoryInterface::class)
            ->getAll();

        return new OptionsResource(resource: $options, textColumn: 'name', valueColumn: 'countryId');
    }
}
