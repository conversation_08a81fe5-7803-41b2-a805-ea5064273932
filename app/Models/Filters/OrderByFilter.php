<?php

namespace App\Models\Filters;

use App\Exceptions\GeneralException;
use App\Models\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Schema;

class OrderByFilter implements FilterInterface
{
    /**
     * @throws GeneralException
     */
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('order_by') || !request()->filled('order_by')) {
            return $next($query);
        }

        $defaultDirection = 'asc';

        [$column, $direction] = request()->string('order_by')->whenContains(
            ',',
            fn($orderBy) => $orderBy->explode(','),
            fn($orderBy) => $orderBy->toString()
        );

        $direction ??= $defaultDirection;

        if (!in_array($direction, ['asc', 'desc'])) {
            throw new GeneralException(
                message: 'Order direction must be "asc" or "desc". !',
                code: Response::HTTP_BAD_REQUEST
            );
        }

        $columns = Schema::getColumnListing($query->getModel()->getTable());

        if (!in_array($column, $columns)) {
            throw new GeneralException(
                message: "The passed column ($column) to order by filter is not exists !",
                code: Response::HTTP_BAD_REQUEST
            );
        }

        return $next($query)->reorder($column, $direction);
    }

    public function getValue(): mixed
    {
        return request()->input('order_by');
    }
}
