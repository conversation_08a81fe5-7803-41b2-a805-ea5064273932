<?php

namespace App\Models\Filters;



use Closure;
use Carbon\Carbon;


use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;

class PublishedFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {

        $status = $this->getValue();
        return is_null($status) ? $next($query) : $next($query)->where('publishedAt', '>=',  Carbon::now());

       ///return request()->input('publushed') ? $next($query)->where('publishedAt', '>=',  Carbon::now() )  : $next() ;

    }

    public function getValue(): mixed
    {

        return request()->has('publishedAt') ? request()->integer('publishedAt') : null;
    }



}