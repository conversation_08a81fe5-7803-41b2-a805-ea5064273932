<?php

namespace App\Models\Filters;

use Closure;
use Illuminate\Support\Facades\Cache;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;
use App\Repositories\Brand\BrandRepositoryInterface;

class UsedCategoriesBrandsFilter implements FilterInterface, WithOptions
{
    public function handle(Builder $query, Closure $next): mixed
    {

        $usedCategories = $this->getValue();
        return is_null($usedCategories) ? $next($query) : $next($query)->whereHas('usedCategories', function ($q) use ($usedCategories) {
            $q->whereIn('used_categories_brands.usedCategoryId', $usedCategories);
        });


    }

    public function getValue(): mixed
    {

        return request()->has('used-categories') && request()->filled('used-categories') ? collect(request()->input('used-categories'))->map(
            function ($item) {
                return (int) $item;
            }
        ) : null;

        /*   $value = request()->has('used-categories') ? explode(",", request()->get('used-categories')) : null;

        return collect($value)->map(function ($item) {
            return (int) trim($item);
        })->toArray();

*/

    }

    public function getOptions(): OptionsResource
    {
        $options = resolve(BrandRepositoryInterface::class)
            ->getAll();

        return new OptionsResource(resource: $options, textColumn: 'name', valueColumn: 'brandId');
    }
}
