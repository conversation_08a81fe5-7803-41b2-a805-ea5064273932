<?php

namespace App\Models\Filters;
use Closure;
use Carbon\Carbon;

use App\Repositories\Rating\RatingRepositoryInterface;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;

class StatusFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $status = $this->getValue();
        if (is_null($status)) {
            return $next($query);
        }

        return is_array($status) ? $next($query)->whereIn('status', $status) : $next($query)->where('status', $status);

    }

    public function getValue(): mixed
    {
        if (!request()->has('status')) {
            return null;
        }

        $status = request()->input('status');

        if (is_array($status)) {
            return empty($status) ? null : $status;
        }

        if (is_string($status)) {
            return trim($status) === '' ? null : $status;
        }

        return null;
    }


}