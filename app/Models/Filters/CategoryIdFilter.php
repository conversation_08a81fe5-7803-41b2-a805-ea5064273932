<?php

namespace App\Models\Filters;

use Closure;
use App\Filters\Interfaces\WithType;
use Illuminate\Support\Facades\Cache;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;
use App\Repositories\Category\CategoryRepositoryInterface;

class CategoryIdFilter implements FilterInterface, WithOptions, WithType
{

    public function handle(Builder $query, Closure $next): mixed
    {
        $categoryId = $this->getValue();
        return is_null($categoryId) ? $next($query) : $next($query)->whereHas('categoriesAttributes', function ($query) use ($categoryId) {
            $query->where('categoryId', $categoryId); });
    }

    public function getValue(): mixed
    {
        return request()->has('categoryId') ? request()->integer('categoryId') : null;
    }

    public function getOptions(): OptionsResource
    {

        $cacheKey = 'categories';
        $options = [];
        if (Cache::has($cacheKey)) {
            $options = Cache::get($cacheKey);
        } else {
            $cacheDuration = 60; // Cache duration in minutes
            $options = resolve(CategoryRepositoryInterface::class)->getAll();
            Cache::put($cacheKey, $options, $cacheDuration);
        }
        return new OptionsResource(resource: $options, textColumn: 'name', valueColumn: 'categoryId');
    }


    public function getType()
    {
        return "select";
    }

}
