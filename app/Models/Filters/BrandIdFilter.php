<?php

namespace App\Models\Filters;

use Closure;
use App\Filters\Interfaces\WithType;
use Illuminate\Support\Facades\Cache;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;
use App\Repositories\Brand\BrandRepositoryInterface;

class BrandIdFilter implements FilterInterface, WithOptions, WithType
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $brandId = $this->getValue();
        return is_null($brandId) ? $next($query) : $next($query)->where('brandId', $brandId);
    }

    public function getValue(): mixed
    {

        return request()->has('brandId') ? request()->integer('brandId') : null;
    }

    public function getOptions($resource, $name): OptionsResource
    {

        $cacheKey = 'brands';
        $options = [];
        if (Cache::has($cacheKey)) {
            $options = Cache::get($cacheKey);
        } else {
            $cacheDuration = 60; // Cache duration in minutes
            $options = resolve(BrandRepositoryInterface::class)->getAll();
            Cache::put($cacheKey, $options, $cacheDuration);
        }
        return new OptionsResource(resource: $options, textColumn: 'name', valueColumn: 'brandId');
    }



    public function getType()
    {
        return "select";
    }
}
