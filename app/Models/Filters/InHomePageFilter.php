<?php

namespace App\Models\Filters;

use App\Models\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class InHomePageFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $inHomePage = $this->getValue();
        return is_null($inHomePage) ? $next($query) : $next($query)->where('inHomePage', $inHomePage);
    }

    public function getValue(): mixed
    {
        return request()->has('inHomePage') ? request()->integer('inHomePage') : null;
    }


}