<?php

namespace App\Models\Filters;

use App\Models\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class isFeaturedFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $isFeatured = $this->getValue();
        return is_null($isFeatured) ? $next($query) : $next($query)->where('isFeatured', $isFeatured);
    }

    public function getValue(): mixed
    {
        return request()->has('isFeatured') ? request()->integer('isFeatured') : null;
    }


}