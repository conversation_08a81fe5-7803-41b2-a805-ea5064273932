<?php

namespace App\Models\Filters;

use Closure;
use App\Filters\Interfaces\WithType;
use Illuminate\Support\Facades\Cache;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;
use App\Filters\Interfaces\SetAttributeOptionsIntreFace;
use App\Repositories\Attribute\AttributeRepositoryInterface;


class AttributesFilter implements FilterInterface, WithOptions, WithType, SetAttributeOptionsIntreFace
{

    public $attributeId;
    public $searchType;
    public $options;
    public $key;
    public $valueArray =[];

    public function handle(Builder $query, Closure $next): mixed
    {
   
        if(!is_null($this->valueArray) && count($this->valueArray) && is_array($this->valueArray) ){
            return $next($query)->whereHas('variance.attributesValues', function ($query)  {
                $query->where('attributeId' , $this->attributeId);
                $query->whereIn('attributeOptionId' ,$this->valueArray);
            });  
        }

        return $next($query);
    }

    public function getValue(): mixed
    {
        $this->valueArray= request()->has($this->key) ? explode(", ",request()->get($this->key)) : [];           
        return  request()->has($this->key) ? explode(", ",request()->get($this->key)) : null;  
    }

    public function getOptions($resource, $name): OptionsResource
    {

        $options = $this->options;
        return new OptionsResource(resource: $options, textColumn: 'name', valueColumn: 'attributeOptionId');
    }



    public function getType()
    {
        return $this->searchType;
    }



    public function setAttributeOptions($key)
    {

       
        $cacheKey = 'attributes.' . $key;
        $this->key = $key;
        $options = [];


        $attribute = resolve(AttributeRepositoryInterface::class)->findByKeyAttribute($key);
        $this->options = $attribute->options;
        $this->attributeId = $attribute->attributeId;
        $this->searchType = $attribute->searchType ;
  
    
        $this->searchType = $attribute->searchType;
        /*

        if (Cache::has($cacheKey)) {
            $attribute = Cache::get($cacheKey);
            $this->options = $attribute->options;
            $this->attributeId = $attribute->attributeId;
            $this->searchType = $attribute->searchType ;
        } else {
            $cacheDuration = 60; // Cache duration in minutes
            $attribute = resolve(AttributeRepositoryInterface::class)->findByKeyAttribute($key);
            $this->options = $attribute->options;
            $this->attributeId = $attribute->attributeId;
            $this->searchType = $attribute->searchType ;
            Cache::put($cacheKey, $attribute, $cacheDuration);
        } */


    }
}
