<?php

namespace App\Models\Filters;

use Closure;
use Illuminate\Support\Facades\Cache;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;
use App\Repositories\Brand\BrandRepositoryInterface;

class CategoriesBrandsFilter implements FilterInterface, WithOptions
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $categories = $this->getValue();
        return is_null($categories) ? $next($query) : $next($query)->whereHas('categories', function ($q) use ($categories) {
            $q->whereIn('categories.categoryId', $categories);
        });


    }

    public function getValue(): mixed
    {

        return request()->has('categories') && request()->filled('categories') ? collect(request()->input('categories'))->map(
            function ($item) {
                return (int) $item;
            }
        ) : null;

        /* $value = request()->has('categories') ? explode(",", request()->get('categories')) : null;
         return collect($value)->map(function ($item) {
             return (int) trim($item);
         })->toArray();
         */

    }


    public function getOptions(): OptionsResource
    {
        $options = resolve(BrandRepositoryInterface::class)
            ->getAll();

        return new OptionsResource(resource: $options, textColumn: 'name', valueColumn: 'brandId');
    }
}