<?php

namespace App\Models\Filters;

use App\Models\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class BrandLabelsFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $labels = $this->getValue();


        if (is_null($labels))
            return $next($query);

        return $next($query)
            ->whereHas('brandLabels', function ($query) use ($labels) {
                is_array($labels) ? $query->join('labels', 'labels.labelId', '=', 'brand_labels.labelId')->whereIn('labels.slug', $labels) : $query->join('labels', 'labels.labelId', '=', 'brand_labels.labelId')->where('labels.slug', $labels);
            });
    }


    public function getValue(): mixed
    {
        return request()->has('labels') ? request()->get('labels') : null;
    }


}