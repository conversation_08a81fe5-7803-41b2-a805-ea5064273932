<?php

namespace App\Models\Filters;

use Closure;
use App\Filters\Interfaces\WithType;
use Illuminate\Support\Facades\Cache;
use App\Filters\Interfaces\WithOptions;
use App\Http\Resources\OptionsResource;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;
use App\Repositories\Category\CategoryRepositoryInterface;

class ProductIdFilter implements FilterInterface, WithType
{

    public function handle(Builder $query, Closure $next): mixed
    {
        $productId = $this->getValue();
        return is_null($productId) ? $next($query) : $next($query)->where('products.productId', $productId);
    }

    public function getValue(): mixed
    {
        return request()->has('id') ? request()->integer('id') : null;
    }




    public function getType()
    {
        return "number";
    }

}