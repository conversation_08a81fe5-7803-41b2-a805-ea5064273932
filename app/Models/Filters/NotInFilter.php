<?php

namespace App\Models\Filters;

use App\Exceptions\GeneralException;
use App\Models\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Schema;

class NotInFilter implements FilterInterface
{
    /**
     * @throws GeneralException
     */
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('not_in') || !request()->filled('not_in')) {
            return $next($query);
        }

        $notId = request()->string('not_in');

        if ($notId->explode(',')->containsOneItem()) {
            throw new GeneralException(
                message: "You mast pass the column,value when use not in filter !",
                code: Response::HTTP_BAD_REQUEST
            );
        }

        [$column, $value] = request()->string('not_in')->explode(',');

        if (str($value)->contains('|')) {
            $value = str($value)->explode('|')->toArray();
        }

        $columns = Schema::getColumnListing($query->getModel()->getTable());

        if (!in_array($column, $columns)) {
            throw new GeneralException(
                message: "The passed column ($column) is not exists in the table !",
                code: Response::HTTP_BAD_REQUEST
            );
        }

        return $next($query)->whereNotIn($column, !is_array($value) ? [$value] : $value);
    }

    public function getValue(): mixed
    {
        return request()->input('not_in');
    }
}
