<?php

namespace App\Models\Filters;

use App\Exceptions\GeneralException;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Schema;

class OrderBySearchFilter
{


    public static function parse(): mixed
    {

        $direction = 'asc';
        $column = 'createdAt';

        if (!request()->has('orderBy') || !request()->filled('orderBy')) {
            return [$column . ":" . $direction];
        }


        [$column, $direction] = request()->string('orderBy')->whenContains(
            ',',
            fn($orderBy) => $orderBy->explode(','),
            fn($orderBy) => $orderBy->toString()
        );



        if (!in_array($column, \App\Models\Variance::$sortableAttributes)) {
            throw new GeneralException(
                message: 'cant sorting this comlumn !',
                code: Response::HTTP_BAD_REQUEST
            );
        }
        if (!in_array($direction, ['asc', 'desc'])) {
            throw new GeneralException(
                message: 'Order direction must be "asc" or "desc". !',
                code: Response::HTTP_BAD_REQUEST
            );
        }


        // ['basePrice:asc']; //$orderBy;
        return [$column . ":" . $direction];
    }
}
