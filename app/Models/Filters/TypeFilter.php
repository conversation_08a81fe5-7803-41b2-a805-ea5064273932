<?php

namespace App\Models\Filters;

use Closure;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;

class TypeFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        $type = $this->getValue();
        return is_null($type) ? $next($query) : $next($query)->where('type', $type);

    }

    public function getValue(): mixed
    {
        return request()->has('type') ? request()->string('type') : null;

    }


}