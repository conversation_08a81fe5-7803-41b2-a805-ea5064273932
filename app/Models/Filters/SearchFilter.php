<?php

namespace App\Models\Filters;

use App\Exceptions\GeneralException;
use App\Filters\Interfaces\FilterInterface;
use App\Traits\Models\Searchable;
use Closure;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class SearchFilter implements FilterInterface
{
    /**
     * @throws GeneralException
     */
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('search')) {
            return $next($query);
        }

        $searchTerm = request()->input('search');

        if (!has_trait($query->getModel(), Searchable::class)) {
            throw new GeneralException(
                message: __(
                    'response-messages.class_not_have_trait',
                    ['class' => $query->getModel()::class, 'trait' => Searchable::class]
                )
            );
        }

        $attributes = $query->getModel()->allowedSearchAttributes();

        if (blank($attributes)) {
            return $next($query);
        }
        return $next($query)->where(function (Builder $query) use ($attributes, $searchTerm) {
            foreach (Arr::wrap($attributes) as $attribute) {
                $query->when(
                    str_contains($attribute, '.'),
                    function (Builder $query) use ($attribute, $searchTerm) {
                        [$relationName, $relationAttribute] = explode('.', $attribute);
                        $query->orWhereHas(
                            $relationName,
                            function (Builder $builder) use ($relationAttribute, $searchTerm) {
                                // $table = $builder->getModel()->getTable();
                                // $database = $builder->getModel()->getConnection()->getDatabaseName();
                                return $builder->where($relationAttribute, 'LIKE', "%{$searchTerm}%");
                            }
                        );
                    },
                    function (Builder $query) use ($attribute, $searchTerm) {
                        $query->orWhere($attribute, 'LIKE', "%{$searchTerm}%");

                        // if (str_contains($attribute, '->')) {
                        //     [$column, $jsonAttribute] = explode('->', $attribute);
                        //     $query->where(DB::raw("json_extract(`$column`, '$.$jsonAttribute') COLLATE utf8mb4_general_ci"), 'LIKE', "%$searchTerm%");
    
                        // } else {
                        //     $query->orWhere($attribute, 'LIKE', "%{$searchTerm}%");
                        // }
    

                    }
                );
            }
        });
    }

    public function getValue(): mixed
    {
        return request()->input('search');
    }
}