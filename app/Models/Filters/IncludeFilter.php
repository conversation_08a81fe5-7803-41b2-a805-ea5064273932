<?php

namespace App\Models\Filters;

use App\Models\Filters\Interfaces\FilterInterface;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class IncludeFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {
        if (!request()->has('include')) {
            return $next($query);
        }

        $ids = request()->string('include')->explode(',')->toArray();

        return $next($query)->orWhereIn('id', $ids);
    }

    public function getValue(): mixed
    {
        return request()->input('include');
    }
}
