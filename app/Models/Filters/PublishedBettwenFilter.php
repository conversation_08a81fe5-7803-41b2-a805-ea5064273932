<?php

namespace App\Models\Filters;



use Closure;
use Carbon\Carbon;


use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;

class PublishedBettwenFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {

        $publishedAt = request()->input('publishedAt');
        $unPublishedAt = request()->input('unPublishedAt');
        return $next($query)->where('publishedAt', '>=',   $publishedAt )->where('publishedAt', '=<',   $unPublishedAt );

    }



    public function getValue(): mixed
    {

        return request()->has('publishedAt') ? request()->integer('publishedAt') : null;
    }



}