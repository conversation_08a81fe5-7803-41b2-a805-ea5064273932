<?php

namespace App\Models\Filters;

use Closure;
use Illuminate\Database\Eloquent\Builder;

trait Publishable
{
    public function handle(Builder $query, Closure $next): mixed
    {

        $table = $query->getModel()->getTable();
        return $query->where("$table.isPublished", PUBLISHED)
            ->where(function ($query) use ($table) {
                $query
                    ->orWhere(function ($query) use ($table) {
                        $query->whereRaw("`$table`.`publishedAt` <= NOW()")
                            ->whereRaw("`$table`.`unPublishedAt` >= NOW()");
                    })
                    ->orWhere(function ($query) use ($table) {
                        $query->whereRaw("`$table`.`publishedAt` <= NOW()")
                            ->whereNull("$table.unPublishedAt");
                    })
                    ->orWhere(function ($query) use ($table) {
                        $query->whereNull("$table.publishedAt")
                            ->whereRaw("`$table`.`unPublishedAt` >= NOW()");
                    })
                    ->orWhere(function ($query) use ($table) {
                        $query->whereNull("$table.publishedAt")
                            ->whereNull("$table.unPublishedAt");
                    });
            });

    }

}
;