<?php

namespace App\Models\Filters;

use Closure;

use Illuminate\Database\Eloquent\Builder;
use App\Models\Filters\Interfaces\FilterInterface;

class CategoriesAttributesFilter implements FilterInterface
{
    public function handle(Builder $query, Closure $next): mixed
    {

        $categories = $this->getValue();
        return is_null($categories) ? $next($query) : $next($query)->whereHas('categoriesAttributes', function ($q) use ($categories) {
            $q->whereIn('categories_attributes.categoryId', $categories);
        });

    }

    public function getValue(): mixed
    {

        return request()->has('categories') && request()->filled('categories') ? collect(request()->input('categories'))->map(
            function ($item) {
                return (int) $item;
            }
        ) : null;
    }


}
