<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class TransactionsRule
 * 
 * @property int $transactionRuleId
 * @property int $transactionId
 * @property string $rule
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @package App\Models
 */
class TransactionsRule extends BaseModel
{
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'transactions_rules';
	protected $primaryKey = 'transactionRuleId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'transactionId' => 'int'
	];

	protected $fillable = [
		'transactionId',
		'rule'
	];
}