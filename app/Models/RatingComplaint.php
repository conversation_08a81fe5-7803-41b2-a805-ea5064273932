<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use App\Models\Filters\StatusFilter;

/**
 * Class Rating
 *
 * @property int $ratingId
 * @property string $review
 * @property int $rating
 * @property int $userId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property User $user
 *
 * @package App\Models
 */
class RatingComplaint extends BaseModel
{
	use HasFactory, HasFilters, Searchable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'rating_complaint';
	protected $primaryKey = 'ratingComplaintId';

	protected $casts = [
		'rating' => 'int',
		'userId' => 'int',
		'visitorId' => 'int',
		'complaintId' => 'int',
	];

	protected $fillable = [
		'review',
		'rating',
		'userId',
		'visitorId',
		'complaintId',
		'model_type',
		'model_id',
		'status',
	];

	public function visitor(): BelongsTo
	{
		return $this->belongsTo(Visitor::class, 'visitorId');
	}


	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'userId');
	}

	public function complaint(): BelongsTo
	{
		return $this->belongsTo(Complaint::class, 'complaintId');
	}

	public function model()
	{
		return $this->morphTo();
	}

	public function allowedFilters(): array
	{
		return [
			'status' => StatusFilter::class,
		];
	}

	public function allowedSearchAttributes(): array
	{
		return [
			'ratingComplaintId',
			'review',
			'rating',
			'createdAt',
			'updatedAt',
			'status',
		];
	}


}