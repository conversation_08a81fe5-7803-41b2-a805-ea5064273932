<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class SuppliersAddress
 *
 * @property int $supplierAddressId
 * @property int $supplierId
 * @property int $addressId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Address $address
 * @property Supplier $supplier
 *
 * @package App\Models
 */
class SuppliersAddress extends BaseModel
{
	use HasFactory;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'suppliers_addresses';
	protected $primaryKey = 'supplierAddressId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'supplierId' => 'int',
		'addressId' => 'int'
	];

	protected $fillable = [
		'supplierId',
		'addressId'
	];

	public function address(): BelongsTo
	{
		return $this->belongsTo(Address::class, 'addressId');
	}

	public function supplier(): BelongsTo
	{
		return $this->belongsTo(Supplier::class, 'supplierId');
	}
}