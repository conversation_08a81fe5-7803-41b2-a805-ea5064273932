<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use App\Models\Filters\StatusFilter;

/**
 * Class Rating
 *
 * @property int $ratingId
 * @property string $review
 * @property int $rating
 * @property int $userId
 * @property int $orderId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property User $user
 *
 * @package App\Models
 */
class OrderRating extends BaseModel
{
	use HasFactory, HasFilters, Searchable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'order_rating';
	protected $primaryKey = 'orderRatingId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'rating' => 'int',
		'userId' => 'int',
		'orderId' => 'int',
		'status' => 'string',
		'review' => 'string',
		'createdAt' => 'datetime',
		'updatedAt' => 'datetime',
	];

	protected $fillable = [
		'review',
		'rating',
		'userId',
		'orderId',
		'status',
	];

	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'userId');
	}

	public function order(): BelongsTo
	{
		return $this->belongsTo(Order::class, 'orderId');
	}

	public function allowedFilters(): array
	{
		return [
			'status' => StatusFilter::class,
		];
	}

	public function allowedSearchAttributes(): array
	{
		return [
			'orderRatingId',
			'review',
			'rating',
			'status',
			'orderId',
			'createdAt',
			'updatedAt',

		];
	}


}