<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin Builder
 */
class ShippingsAddress extends BaseModel
{
    use HasFactory;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'shippings_addresses';
    protected $primaryKey = 'shippingAddressId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'shippingId' => 'int',
        'addressId' => 'int',

    ];

    protected $fillable = [
        'shippingId',
        'addressId',
    ];

    public function address(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'addressId');
    }

    // public function shipping(): BelongsTo
    // {
    //     return $this->belongsTo(Shipping::class, 'shippingId');
    // }
}