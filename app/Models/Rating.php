<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use App\Models\Filters\StatusFilter;

/**
 * Class Rating
 *
 * @property int $ratingId
 * @property string $review
 * @property int $rating
 * @property int $userId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property User $user
 *
 * @package App\Models
 */
class Rating extends BaseModel
{
	use HasFactory,HasFilters, Searchable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'ratings';
	protected $primaryKey = 'ratingId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'rating' => 'int',
		'userId' => 'int'
	];

	protected $fillable = [
		'review',
		'rating',
		'userId'
	];

	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'userId');
	}
	public function productGroup(): BelongsTo
	{
		return $this->belongsTo(Group::class, 'groupId');
	}
	public function product(): BelongsTo
	{
		return $this->belongsTo(Product::class, 'productId');
	}

	public function allowedFilters(): array
	{
		return [
			'status' => StatusFilter::class,
		];
	}

	public function allowedSearchAttributes(): array
	{
		return [
			'ratingId',
			'review',
			'rating',
			'createdAt',
			'updatedAt',

		];
	}


}