<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class TelescopeEntry
 * 
 * @property int $sequence
 * @property string $uuid
 * @property string $batch_id
 * @property string|null $family_hash
 * @property bool $should_display_on_index
 * @property string $type
 * @property string $content
 * @property Carbon|null $created_at
 * 
 * @property TelescopeEntriesTag $telescopeEntriesTag
 *
 * @package App\Models
 */
class TelescopeEntry extends BaseModel
{
	protected $table = 'telescope_entries';
	protected $primaryKey = 'sequence';
	protected $perPage = 24;
	public $timestamps = false;
	public static $snakeAttributes = false;

	protected $casts = [
		'should_display_on_index' => 'bool'
	];

	protected $dates = [
		'created_at'
	];

	protected $fillable = [
		'uuid',
		'batch_id',
		'family_hash',
		'should_display_on_index',
		'type',
		'content',
		'created_at'
	];

	public function telescopeEntriesTag(): HasOne
	{
		return $this->hasOne(TelescopeEntriesTag::class, 'entry_uuid', 'uuid');
	}
}