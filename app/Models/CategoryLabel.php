<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class VariancesStock
 *
 * @property int $brandLabelId
 * @property int $brandId
 * @property int $labelId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Label $label
 * @property Brand $brand
 *
 * @package App\Models
 */
class CategoryLabel extends BaseModel
{

	use HasFactory;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'category_labels';
	protected $primaryKey = 'categoryLabelId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'categoryId' => 'int',
		'labelId' => 'int'
	];

	protected $fillable = [
		'categoryId',
		'labelId'
	];


	public function label(): BelongsTo
	{
		return $this->belongsTo(Label::class, 'labelId');
	}

	public function category(): BelongsTo
	{
		return $this->belongsTo(Category::class, 'categoryId');
	}
}