<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Offers;

use Carbon\Carbon;
use App\Models\Product;
use App\Models\Variance;
use App\Models\BaseModel;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class BuyOneGetOneFree
 *
 * @property int $buyOneGetOneFreeId
 * @property array $jsonIds
 * @property int $productIdFree
 * @property Carbon|null $publishedAt
 * @property Carbon|null $unPublishedAt
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @package App\Models\Offers
 */
class BuyOneGetOneFree extends BaseModel
{
	use HasFactory, Lookable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'buy_one_get_one_free';
	protected $primaryKey = 'buyOneGetOneFreeId';
	protected $perPage = 24;
	public static $snakeAttributes = false;
	protected $dates = [
		'publishedAt',
		'unPublishedAt'
	];

	protected $casts = [
		'productId' => 'int',
		'varianceId' => 'int',
		'bundleId' => 'int',
		'jsonProductIds' => 'json'
	];
	protected $fillable = [
		'publishedAt',
		'unPublishedAt',
        'jsonProductIds',
        'productId',
		'varianceId',
		'bundleId'
	];


	public function product(): HasOne
	{
		return $this->hasOne(Product::class, 'productId');
	}
	
	public function variance(): HasOne
	{
		return $this->hasOne(Variance::class, 'varianceId');
	}

	public function bundle(): HasOne
	{
		return $this->hasOne(Bundle::class, 'bundleId');
	}


}