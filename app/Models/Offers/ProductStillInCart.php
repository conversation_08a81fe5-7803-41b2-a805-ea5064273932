<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Offers;

use Carbon\Carbon;
use App\Models\BaseModel;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class BuyOneGetOneFree
 *
 * @property int $productStillInCartId
 * @property array $jsonIds
 * @property int $jsonIds
 * @property Carbon|null $publishedAt
 * @property Carbon|null $unPublishedAt
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @package App\Models\Offers
 */
class ProductStillInCart extends BaseModel
{
	use HasFactory, Lookable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'product_still_in_cart';
	protected $primaryKey = 'productStillInCartId';
	protected $perPage = 24;
	public static $snakeAttributes = false;
	protected $dates = [
		'publishedAt',
		'unPublishedAt'
	];

	protected $fillable = [
		'publishedAt',
		'unPublishedAt',
        'jsonProductIds',
        'discount',
        "discountType"
	];

	


}