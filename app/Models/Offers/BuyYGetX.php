<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models\Offers;

use Carbon\Carbon;
use App\Models\Product;
use App\Models\Variance;
use App\Models\BaseModel;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class BuyOneGetOneFree
 *
 * @property int $buyYGetXId
 * @property array $jsonIds
 * @property int $productIdY
 * @property int $bundleIdY
 * @property int $varianceIdY
  * @property int $productIdX
 * @property int $bundleIdX
 * @property int $varianceIdX
 * @property array $config
 * @property int $isPublished
 * @property Carbon|null $publishedAt
 * @property Carbon|null $unPublishedAt
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @package App\Models\BuyYGetX
 */
class BuyYGetX extends BaseModel
{
	use HasFactory, Lookable;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'buy_y_get_x';
	protected $primaryKey = 'buyYGetXId';
	protected $perPage = 24;
	public static $snakeAttributes = false;
	protected $dates = [
		'publishedAt',
		'unPublishedAt'
	];

	protected $casts = [
		'productIdY' => 'int',
		'varianceIdY' => 'int',
		'bundleIdY' => 'int',
		'productIdX' => 'int',
		'varianceIdX' => 'int',
		'bundleIdX' => 'int',
		'isPublished' => 'int',
		'config' => 'json'
	];
	protected $fillable = [
		'publishedAt',
		'unPublishedAt',
		'productIdY' ,
		'varianceIdY',
		'bundleIdY' ,
		'productIdX' ,
		'varianceIdX' ,
		'bundleIdX' ,
		'isPublished',
		'config' 
	];


	public function productX(): HasOne
	{
		return $this->hasOne(Product::class, 'productIdX');
	}
	public function productY(): HasOne
	{
		return $this->hasOne(Product::class, 'productIdY');
	}
	
	public function varianceY(): HasOne
	{
		return $this->hasOne(Variance::class, 'varianceIdY');
	}

	public function varianceX(): HasOne
	{
		return $this->hasOne(Variance::class, 'varianceIdX');
	}

	public function bundleY(): HasOne
	{
		return $this->hasOne(Bundle::class, 'bundleIdY');
	}


	public function bundleX(): HasOne
	{
		return $this->hasOne(Bundle::class, 'bundleIdX');
	}


}