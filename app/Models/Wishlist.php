<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class Wishlist
 *
 * @property int $wishlistId
 * @property int $productId
 * @property int $productId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 *
 * @package App\Models
 */

class Wishlist extends BaseModel
{
    use HasFactory;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'wishlists';
    protected $primaryKey = 'wishlistId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $fillable = [
        'user_id',
        'wishlistId',
        'visitorId'
    ];



    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'productId');
    }


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}