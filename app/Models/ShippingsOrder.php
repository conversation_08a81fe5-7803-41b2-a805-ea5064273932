<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin Builder
 */
class ShippingsOrder extends BaseModel
{
    use HasFactory;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'shippings_orders';
    protected $primaryKey = 'shippingOrderId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'shippingId' => 'int',
        'orderId' => 'int',

    ];

    protected $fillable = [
        'shippingId',
        'orderId',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'orderId');
    }

    // public function shipping(): BelongsTo
    // {
    //     return $this->belongsTo(Shipping::class, 'shippingId');
    // }
}
