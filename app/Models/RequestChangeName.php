<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin Builder
 */
class RequestChangeName extends BaseModel
{
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'requests_change_name';
    protected $primaryKey = 'requestsChangeNameId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $timestamps = false;

    protected $fillable = [
        'firstName',
        'lastName',
        'previousFirstName',
        'previousLastName',
        'status',
        'userId'
    ];
}