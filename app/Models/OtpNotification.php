<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * Class Price
 *
 * @property int $otpNotificationId
 * @property string $method
 * @property int $userId
 * @property Carbon $expiryDate
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Currency|null $currency
 *
 * @package App\Models
 */
class OtpNotification extends BaseModel
{

    use HasFactory;

    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'otp_notifications';
    protected $primaryKey = 'otpNotificationId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $fillable = [
        'otp',
        'userId',
        'method',
        'expiryDate'
    ];


}