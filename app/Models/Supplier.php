<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use App\Traits\HasTranslations;
use App\Traits\Models\Lookable;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Supplier
 *
 * @property int $supplierId
 * @property array $name
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Media $media
 * @property Collection|Stock[] $stocks
 * @property Collection|Address[] $addresses
 *
 * @package App\Models
 */
class Supplier extends BaseModel
{
	use HasTranslations, HasFactory, Lookable,HasFilters,Searchable;

	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'suppliers';
	protected $primaryKey = 'supplierId';
	protected $perPage = 24;
	public static $snakeAttributes = false;
	public $translatable = ['name'];

	protected $casts = [
		'name' => 'json',
	];

	protected $fillable = [
		'name'
	];

	public function media(): BelongsTo
	{
		return $this->belongsTo(Media::class, 'mediaId');
	}

	public function stocks(): HasMany
	{
		return $this->hasMany(Stock::class, 'supplierId');
	}

	public function addresses(): BelongsToMany
	{
		return $this->belongsToMany(Address::class, 'suppliers_addresses', 'supplierId', 'addressId')
			->withPivot('supplierAddressId')
			->withTimestamps();
	}
	public function getLookupResourceConfig(): array
	{
		return [
			'text_column' => 'name',
			'value_column' => 'supplierId',
		];
	}

	public function allowedFilters(): array
	{
		return [];
	
	}

	public function allowedSearchAttributes(): array
	{
		return [
			'name->ar',
			'name->en',
			'createdAt',
			'updatedAt',

		];
	}


}