<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Class PasswordReset
 * 
 * @property string $email
 * @property string $token
 * @property Carbon $createdAt
 *
 * @package App\Models
 */
class PasswordReset extends BaseModel
{
	const CREATED_AT = 'createdAt';
	protected $table = 'password_resets';
	protected $primaryKey = 'passwordResetId';
	protected $perPage = 24;
	public $timestamps = false;
	public static $snakeAttributes = false;


	protected $casts = [
		'phone' => 'json',

	];
	protected $fillable = [
		'email',
		'code',
		'userId',
		'phone',
		'expiryDate'
	];


	protected $hidden = [
		'code',
	];



	public function user(): BelongsTo
	{
		return $this->belongsTo(User::class, 'userId');
	}
}