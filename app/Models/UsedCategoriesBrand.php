<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

/**
 * @mixin Builder
 */
class UsedCategoriesBrand extends BaseModel
{
    use HasFactory;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'used_categories_brands';
    protected $primaryKey = 'usedCategoryBrandId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'usedCategoryId' => 'int',
        'brandId' => 'int'
    ];

    protected $fillable = [
        'usedCategoryId',
        'brandId'
    ];

    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class, 'brandId');
    }

    public function usedCategory(): BelongsTo
    {
        return $this->belongsTo(UsedCategory::class, 'usedCategoryId');
    }
}
