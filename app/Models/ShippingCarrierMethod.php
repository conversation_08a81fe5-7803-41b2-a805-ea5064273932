<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use App\Traits\Models\HasFilters;
use App\Traits\Models\Searchable;
use App\Traits\Models\Lookable;
use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * Class ShippingCarrierMethod
 *
 * @property int $shippingCarriersMethodsId
 * @property array $config
 * @property int $shippingCarrierId
 * @property int $shippingId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *

 * @package App\Models
 */
class ShippingCarrierMethod extends BaseModel
{

    use HasFactory, Lookable, HasFilters, Searchable;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'shipping_carriers_methods';
    protected $primaryKey = 'shippingCarriersMethodsId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $allowPluckPrimaryKey = true;

    protected $casts = [
        'config' => 'json',

    ];

    protected $fillable = [
        'shippingCarrierId',
        'shippingId',
        'config',
        'label'

    ];


    public static function getDefaultShipping()
    {
        return [
            "price" => [
                "value" => "55",
                "basePrice" => "55",
                "currencyId" => "1",
            ],
        ];
    }

    // public function shipping()
    // {
    //     return $this->belongsTo(Shipping::class, 'shippingId');
    // }
    /**
     * 
     */
    public function shippingCarrier()
    {
        return $this->belongsTo(ShippingCarriers::class, 'shippingCarrierId');
    }

    public function allowedFilters(): array
    {
        return [];

    }

    public function allowedSearchAttributes(): array
    {
        return [
            'shippingCarrierId',
            'shippingId',
            'label',
            'createdAt',
            'updatedAt',

        ];
    }

    public function getLookupResourceConfig(): array
    {
        return [
            'text_column' => 'label',
            'value_column' => 'shippingCarriersMethodsId',
            'meta' => [
                'config',

            ]
        ];
    }


}