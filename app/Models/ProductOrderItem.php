<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class ProductOrderItem
 *
 * @property int $orderItemsId
 * @property int $orderId
 * @property int $productId
 * @property int $varianceId
 * @property int $stockId
 * @property array $snapshot
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Order $order
 * @property Product $product
 * @property Stock $stock
 * @property Variance $variance
 *
 * @package App\Models
 */
class ProductOrderItem extends BaseModel
{
    use SoftDeletes;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    const DELETED_AT = 'deletedAt';
    protected $table = 'product_order_items';
    protected $primaryKey = 'productOrderItemId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'productId' => 'int',
        'varianceId' => 'int',
        'bundleId' => 'int',
        'stockId' => 'int',
        'snapshot' => 'json',
        'quantity' => 'int',
        'status' => 'string',
        'price' => 'float',
        'originalPrice' => 'float',
        'discount' => 'float',

    ];

    protected $fillable = [
        'productId',
        'varianceId',
        'bundleId',
        'stockId',
        'snapshot',
        'quantity',
        'status',
        'price',
        'discount',
        'originalPrice',
    ];


    public function model(): MorphTo
    {
        return $this->morphTo();
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'productId');
    }


    public function stock(): BelongsTo
    {
        return $this->belongsTo(Stock::class, 'stockId');
    }

    public function variance(): BelongsTo
    {
        return $this->belongsTo(Variance::class, 'varianceId');
    }

    // public function price(): BelongsTo
    // {
    // 	return $this->belongsTo(Price::class, 'priceId');
    // }
    // public function originalPrice(): BelongsTo
    // {
    // 	return $this->belongsTo(Price::class, 'originalPriceId');
    // }

    public function orderItem()
    {
        return $this->morphOne(OrderItem::class, 'model');
    }
}