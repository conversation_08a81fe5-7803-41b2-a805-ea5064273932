<?php

namespace App\Models\Scopes\Website;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Database\Eloquent\Builder;

class IsPublishedScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {

        try {
            //code...
            $routePrefix =request()->route()->getPrefix();
            if (!str_contains($routePrefix, 'admin')) {
                $now = Carbon::now();  
                $builder->where('isPublished', true)
                    ->where(function($query)use ($now){
                        $query->where(function (Builder $query)use ($now) {
                            $query->where('products.publishedAt', '<=', $now )
                                  ->where('products.unPublishedAt', '>=',  $now );
                        })
                        ->orWhere(function (Builder $query) use ($now) {
                            $query->where('products.publishedAt', '>=', $now)
                                  ->where('products.unPublishedAt', '>=', $now);
                        })
                        ->orWhere(function (Builder $query) use ($now) {
                            $query->whereNull('products.publishedAt')
                                  ->whereNull('products.unPublishedAt');
                        });
                    });
            }
        } catch (\Throwable $th) {
            //throw $th;
        }
       
       
            
    }
}