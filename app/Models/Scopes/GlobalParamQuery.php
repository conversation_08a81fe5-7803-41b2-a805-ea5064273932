<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class GlobalParamQuery implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $builder
     * @param  \Illuminate\Database\Eloquent\Model  $model
     * @return void
     */
    public function apply(Builder $builder, Model $model)
    {

        if (request()->has("attributes") && request()->get("attributes") != "") {
            $attributes = explode('|',request()->get("attributes"));
            foreach ($attributes as $key => $attribute) {
                $attributes[$key] =     $model->getTable() . '.' . $attributes[$key];
                if (!in_array($attribute, $model->getAllAttributes())) {
                    unset($attributes[$key]);
                }
            }

            $builder->select($attributes);
        }
    }
}