<?php

/**
 * Created by Reliese Model.
 */

// namespace App\Models;

// use Carbon\Carbon;
// use App\Traits\HasTranslations;
// use App\Traits\Models\HasFilters;
// use App\Traits\Models\Searchable;
// use App\Traits\Models\Lookable;
// use App\Traits\GetIdsAsArrayTrait;
// use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\Collection;
// use Illuminate\Database\Eloquent\Relations\BelongsTo;
// use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Relations\BelongsToMany;

// /**
//  * Class Shipping
//  *
//  * @property int $shippingId
//  * @property array $name
//  * @property string $phone
//  * @property Carbon $createdAt
//  * @property Carbon $updatedAt
//  *
//  * 
//  * @property Collection|Address[] $addresses
//  * @package App\Models
//  */
// class Shipping extends BaseModel
// {

//     use HasFactory, HasTranslations, Lookable, HasFilters, Searchable;
//     const CREATED_AT = 'createdAt';
//     const UPDATED_AT = 'updatedAt';
//     protected $table = 'shippings';
//     protected $primaryKey = 'shippingId';
//     protected $perPage = 24;
//     public static $snakeAttributes = false;

//     public $allowPluckPrimaryKey = true;

//     public $translatable = ['name'];

//     protected $casts = [
//         'name' => 'json',

//     ];
//     protected $dates = [
//         'publishedAt',
//         'unPublishedAt'

//     ];
//     protected $fillable = [
//         'name',
//         'publishedAt',
//         'unPublishedAt',
//         'isPublished'

//     ];
//     public function addresses(): BelongsToMany
//     {
//         return $this->belongsToMany(Address::class, 'shipping_carriers', 'shippingId', 'addressId')
//             ->withPivot('shippingCarrierId')
//             ->withTimestamps();
//     }


//     // public function shippingCarriersMethod()
//     // {
//     //     return $this->hasMany(ShippingCarrierMethod::class, 'shippingId');
//     // }




//     public function shippingCarriers()
//     {

//         return $this->hasManyThrough(
//             ShippingCarriers::class,
//             ShippingCarrierMethod::class,
//             'shippingId',
//             // Foreign key on VariancesAttribute table
//             'shippingCarrierId',
//             'shippingId',
//             'shippingCarrierId'
//         );
//     }

//     public function allowedFilters(): array
//     {
//         return [];

//     }

//     public function allowedSearchAttributes(): array
//     {
//         return [
//             'name->ar',
//             'name->en',
//             'phone',
//             'createdAt',
//             'updatedAt',

//         ];
//     }

//     public function getLookupResourceConfig(): array
//     {
//         return [
//             'text_column' => 'name',
//             'value_column' => 'shippingId',
//             'meta' => ['slug']
//         ];
//     }


// }