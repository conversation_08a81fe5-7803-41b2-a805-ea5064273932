<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Class VariancesColor
 *
 * @property int $productShippingId
 * @property int $productId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt

 *
 * @package App\Models
 */
class ProductShipping extends BaseModel implements HasMedia
{
	use HasFactory, InteractsWithMedia;
	const CREATED_AT = 'createdAt';
	const UPDATED_AT = 'updatedAt';
	protected $table = 'product_shippings';
	protected $primaryKey = 'productShippingId';
	protected $perPage = 24;
	public static $snakeAttributes = false;

	protected $casts = [
		'productId' => 'int',
		'shippingCarrierId' => 'int'
	];

	protected $fillable = [
		'productId',
		'shippingCarrierId'
	];


	public function product(): BelongsTo
	{
		return $this->belongsTo(Product::class, 'productId');
	}


	public function shippingCompany(): BelongsTo
	{
		return $this->belongsTo(ShippingCarriers::class, 'shippingCarrierId');
	}


}