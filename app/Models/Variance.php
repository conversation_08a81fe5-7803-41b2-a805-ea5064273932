<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Helper\CategoryExtractor;
use App\Traits\Models\ActiveStocks;
use App\Traits\Models\CustomInteractsWithMedia;
use Carbon\Carbon;

use Lara<PERSON>\Scout\Searchable;
use App\Traits\HasTranslations;
use Meilisearch\Meilisearch;
use Spatie\MediaLibrary\HasMedia;
use App\Traits\Models\HasMeilisearchFilters;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use MeiliSearch\Client;

/**
 * Class Variance
 *
 * @property int $varianceId
 * @property int $brandId
 * @property int $SKU
 * @property string $slug
 * @property string $type
 * @property array|null $metaTitle
 * @property array|null $metaDescription
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Attribute $attribute
 * @property Brand $brand
 * @property Collection|Alternative[] $alternatives
 * @property Collection|AttributesValue[] $attributesValues
 * @property Collection|Bundle[] $bundles
 * @property Collection|Cart[] $carts
 * @property Collection|OrderItem[] $orderItems
 * @property Collection|Attribute[] $attributes
 * @property Collection|Stock[] $stocks
 *
 * @package App\Models
 */
class Variance extends BaseModel implements HasMedia
{
    use SoftDeletes, HasTranslations, HasFactory, HasTranslations, CustomInteractsWithMedia, Searchable, HasMeilisearchFilters, ActiveStocks;

    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    const DELETED_AT = 'deletedAt';
    protected $table = 'variances';
    protected $primaryKey = 'varianceId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $translatable = ['metaTitle', 'metaDescription', 'name', 'oldSlug'];
    public $imagesCollection = [
        'gallery',
        'cover'
    ];
    protected $casts = [
        'brandId' => 'int',
        'SKU' => 'string',
        'slug' => 'string',
        'name' => 'json',
        // 'description' => 'json',
        'metaTitle' => 'json',
        'metaDescription' => 'json',
        'isDefault' => 'bool',
        'productId' => 'int',
        'auctionId' => 'int',
        'oldSlug' => 'json',
    ];

    protected $dates = [
        'publishedAt',
        'unPublishedAt'
    ];

    protected $fillable = [
        'brandId',
        'SKU',
        'slug',
        'name',
        'type',
        'isDefault',
        'metaTitle',
        'metaDescription',
        'productId',
        'isPublished',
        'publishedAt',
        'unPublishedAt',
        'auctionId',
        //  'description',
        'modelNumber'
    ];

    public static $sortableAttributes = [
        'name',
        'createdAt',
        'productId',
        'basePrice',
        'avgRate',
        'brandId',
    ];
    public static $filterableAttributes = [
        'name',
        'basePrice',
        'categories',
        'brandId',
        'attributeValues',
        'avgRate',
        'isListed',
        'hasOffer',
    ];
    protected $cachedFilters = [];

    protected $isFiltersCached = false;



    protected static function booted()
    {
        static::deleting(function ($variance) {

            if ($variance->isForceDeleting()) {
                // Forcibly deleting the variance, remove from Meilisearch
                $variance->unsearchable();
            }
        });
    }


    public static function getFilterableAttributes()
    {
        return self::$filterableAttributes;
    }

    public static function getSortableAttributes()
    {
        return self::$sortableAttributes;
    }

    // public static function boot()
    // {
    //     parent::boot();

    //     // static::saved(function ($model) {
    //     //   //  $model->toSearchableArray(); // Call the toSearchableArray method after relationships are loaded and saved.
    //     // });

    //     // static::creating(function ($model) {

    //     //     $model->toSearchableArray(); // Call the toSearchableArray method after relationships are loaded and saved.
    //     //   });
    // }


    public function shouldBeSearchable(): bool
    {
        // $this->loadMissing(['product']);
        return is_null($this->deletedAt) && $this->isPublished();
    }



    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class, 'brandId');
    }


    public function attributesValues(): HasMany
    {
        return $this->hasMany(AttributesValue::class, 'varianceId');
    }

    public function bundles(): HasMany
    {
        return $this->hasMany(Bundle::class, 'varianceId');
    }

    public function carts(): HasMany
    {
        return $this->hasMany(Cart::class, 'varianceId');
    }

    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'varianceId');
    }


    public function stocks(): BelongsToMany
    {
        return $this->belongsToMany(Stock::class, 'variances_stocks', 'varianceId', 'stockId')
            ->withPivot('varianceStockId')
            ->withTimestamps();
    }


    public function variancesStocks(): BelongsToMany
    {
        return $this->belongsToMany(VariancesStock::class, 'variances_stocks', 'varianceId', 'stockId');
    }

    public function activeStocks(): BelongsToMany
    {
        return $this->belongsToMany(Stock::class, 'variances_stocks', 'varianceId', 'stockId')
            ->where('stocks.isPublished', PUBLISHED)
            ->where(function ($query) {
                $query
                    ->orWhere(function ($query) {
                        $query->whereRaw('`stocks`.`publishedAt` <= NOW()')
                            ->whereRaw('`stocks`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereRaw('`stocks`.`publishedAt` <= NOW()')
                            ->whereNull('stocks.unPublishedAt');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('stocks.publishedAt')
                            ->whereRaw('`stocks`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('stocks.publishedAt')
                            ->whereNull('stocks.unPublishedAt');
                    });
            })
            ->where('stocks.quantity', '>', 0)
            ->where('stocks.quantity', '>', 'stocks.sold');

    }


    public function lastStock()
    {
        return $this->belongsTo(Stock::class, 'variances_stocks', 'varianceId', 'stockId')
            ->orderBy('stocks.sort', 'asc');
    }

    public function activeStock()
    {
        // PLEASE DONT CHANGE
        return $this->hasOneThrough(Stock::class, VariancesStock::class, 'varianceId', 'stockId', 'varianceId', 'stockId', )
            ->where('stocks.isPublished', PUBLISHED)
            ->where(function ($query) {
                $query
                    ->orWhere(function ($query) {
                        $query->whereRaw('`stocks`.`publishedAt` <= NOW()')
                            ->whereRaw('`stocks`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereRaw('`stocks`.`publishedAt` <= NOW()')
                            ->whereNull('stocks.unPublishedAt');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('stocks.publishedAt')
                            ->whereRaw('`stocks`.`unPublishedAt` >= NOW()');
                    })
                    ->orWhere(function ($query) {
                        $query->whereNull('stocks.publishedAt')
                            ->whereNull('stocks.unPublishedAt');
                    });
            })
            ->where('stocks.quantity', '>', 0)
            ->where('stocks.quantity', '>', 'stocks.sold')
            ->orderBy('stocks.sort', 'asc');
        //->with(['price', 'priceBeforeOffer']);
        // ->withTimestamps();

    }



    public function auction(): BelongsTo
    {
        return $this->belongsTo(Auction::class, 'auctionId');
    }


    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'productId');
    }


    public static function getNextId()
    {
        // Get the maximum ID from the table
        $maxId = self::max('id');

        // Calculate the next ID
        $nextId = $maxId + 1;

        return $nextId;
    }



    //TODO: remove this method and use natural scout update


    // public function searchable(): bool
    // {
    //     return $this->activeStocks->count() && $this->attributesValues->count() &&  $this->product->categories->count();
    // }

    public function toSearchableArray()
    {

        $this->translatable = [];
        if ($this->product)
            $this->product->translatable = [];


        $item = $this->loadMissing(['product', 'activeStock']);

        $item = $item->toArray();


        $isOffer = $item['activeStock']['isOffer'] ?? false;
        $priceBeforeOffer = $isOffer ? $item['activeStock']['priceBeforeOffer'] : null;

        $result = [
            'varianceId' => $item['varianceId'],
            'productId' => $item['productId'],
            'name' => [
                'en' => $item['product']['name']['en'] ?? '' . ' ' . $item['name']['en'],
                'ar' => $item['product']['name']['ar'] ?? '' . ' ' . $item['name']['ar'],
            ],
            'slug' => $item['slug'],
            'SKU' => (string) $item['SKU'],
            'brandId' => (string) $item['brandId'],
            'isOffer' => $isOffer,
            'priceBeforeOffer' => $priceBeforeOffer,
            'basePrice' => (float) isset($item['activeStock']['price']) && $item['activeStock']['price'] ? $item['activeStock']['price'] : null,
            'createdAt' => Carbon::parse($item['createdAt'])->timestamp,
        ];


        return $result;


        //$item = $this->loadMissing(['product', 'activeStock', 'attributesValues', 'product.categories', 'product.categories.parent', 'product.attributeValues',/*  'product.categories.groupFilters.filters', 'product.categories.groupFilters.filters.attribute'*/]);
        /* calculate item score based on:
           1- product priority entered by admin, factor 10
           2- product avgRate, factor 20
           3- product publishedAt, the older the less score (it should not exceed 100 )
        */
        // $score = 0;
        // $product = $item['product'];
        // $score += isset($product['priority']) ? $product['priority'] * 10 : 0;
        // $score += $product['avgRate'] * 20;
        // $score += round(100 - (Carbon::parse($product['publishedAt'])->diffInDays(Carbon::now()) / 365) * 100);

        // $varianceAttributes = (array) collect($item['attributesValues'])->pluck('attributeOptionId')->map(fn($i) => (string) $i)->all();
        // $productAttributes = (array) collect($item['product']['attributeValues'])->pluck('attributeOptionId')->map(fn($i) => (string) $i)->all();
        // $attributeValues = array_merge($varianceAttributes, $productAttributes);
        // get array of categoryId and its parents ids
        // Instantiate the helper class
        // $categoryExtractor = new CategoryExtractor();

        // $categoriesCollection = collect($item['product']['categories']);

        // Flatten multiple arrays into a single collection and ensure unique values.
        // $allCategoriesWithParents = $categoriesCollection->flatMap(function ($category) use ($categoryExtractor) {
        //     return $categoryExtractor->extractIds($category);
        // })->unique()->values();

        // $isOffer = $item['activeStock']['isOffer'] ?? false;
        // $priceBeforeOffer = $isOffer ? $item['activeStock']['priceBeforeOffer'] : null;

        // $result = [
        //     'varianceId' => $item['varianceId'],
        //     'productId' => $item['productId'],
        //     'name' => [
        //         'en' => $item['product']['name']['en'] ?? '' . ' ' . $item['name']['en'],
        //         'ar' => $item['product']['name']['ar'] ?? '' . ' ' . $item['name']['ar'],
        //     ],
        //     'slug' => [
        //         'en' => $item['slug']['en'],
        //         'ar' => $item['slug']['ar'],
        //     ],
        //     // 'isListed' => (bool) $item['product']['isListed'],
        //     'SKU' => (string) $item['SKU'],
        //     'brandId' => (string) $item['brandId'],
        //     // 'categories' => $allCategoriesWithParents,
        //     // 'productPublishedAt' => (string) $item['product']['publishedAt'],
        //     // 'stock' => $item['activeStock'],
        //     'isOffer' => $isOffer,
        //     'priceBeforeOffer' => $priceBeforeOffer,
        //     'basePrice' => (float) isset($item['activeStock']['price']) && $item['activeStock']['price'] ? $item['activeStock']['price'] : null,
        //     // 'attributeValues' => $attributeValues,
        //     // 'avgRate' => $item['product']['avgRate'],
        //     'createdAt' => Carbon::parse($item['createdAt'])->timestamp,
        // ];


        // return $result;
    }



    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'variances_attributes', 'varianceId', 'attributeId')
            ->withPivot('varianceAttributeId')
            ->withPivot('varianceId')
            ->withTimestamps();
    }


    public function attributesWithValue(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'variances_attributes', 'varianceId', 'attributeId')
            ->join('attributes_values', function ($join) {
                $join->on('attributes_values.varianceId', '=', 'variances_attributes.varianceId');
                $join->on('attributes_values.attributeId', '=', 'attributes.attributeId');
            })
            ->join('attributes_options', 'attributes_options.attributeOptionId', '=', 'attributes_values.attributeOptionId')
            ->select(
                'attributes.*',
                'attributes_values.*',
                // 'attributes_options.*',
                'attributes_options.name as option_name',
                'attributes_options.slug as option_slug',
                'attributes_options.hexCode',
                'attributes_options.number',
                'variances_attributes.varianceId as pivot_varianceId',
                'variances_attributes.attributeId as pivot_attributeId',
                'variances_attributes.varianceAttributeId as  pivot_varianceAttributeId',
                'variances_attributes.createdAt as pivot_createdAt',
                'variances_attributes.updatedAt as pivot_updatedAt'
            );
    }




    // public function attributes()
    // {

    //     return $this->hasManyThrough(
    //         Attribute::class,
    //         ProductsAttribute::class,
    //         'productId',
    //         'attributeId',
    //         'productId',
    //         'attributeId'
    //     );
    // }



    // public function varianceAttributes()
    // {
    //     return $this->hasMany(VariancesAttribute::class, 'varianceId');
    // }

    public function varianceAttributes(): BelongsToMany
    {
        return $this->belongsToMany(VariancesAttribute::class, 'variances_attributes', 'varianceId', 'attributeId');
    }


    public function attributeValues()
    {
        return $this->hasMany(AttributesValue::class, 'varianceId');
    }


}