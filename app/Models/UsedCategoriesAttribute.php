<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class CategoriesAttribute
 *
 * @property int $categoryAttributeId
 * @property int $usedCategoryId
 * @property int $attributeId
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 * @property Attribute $attribute
 * @property Category $usedCategory
 *
 * @package App\Models
 */
/**
 * @mixin Builder
 */
class UsedCategoriesAttribute extends BaseModel
{
    use HasFactory;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'used_categories_attributes';
    protected $primaryKey = 'usedCategoryAttributeId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    protected $casts = [
        'usedCategoryId' => 'int',
        'attributeId' => 'int'
    ];

    protected $fillable = [
        'usedCategoryId',
        'attributeId'
    ];

    public function attribute(): BelongsTo
    {
        return $this->belongsTo(Attribute::class, 'attributeId');
    }

    public function usedCategory(): BelongsTo
    {
        return $this->belongsTo(UsedCategory::class, 'usedCategoryId');
    }
}
