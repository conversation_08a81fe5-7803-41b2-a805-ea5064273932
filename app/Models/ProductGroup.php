<?php

namespace App\Models;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use App\Traits\HasTranslations;
use App\Traits\Models\Searchable;
use App\Traits\Models\HasFilters;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

/**
 * Class Group
 *
 * @property int $groupId
 * @property string $name
 * @property Carbon $createdAt
 * @property Carbon $updatedAt
 *
 *
 * @package App\Models
 */

class ProductGroup extends BaseModel
{

    use HasFactory, HasTranslations, Searchable, HasFilters;
    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'product_groups';
    protected $primaryKey = 'productGroupId';
    protected $perPage = 24;
    public static $snakeAttributes = false;

    public $translatable = ['name'];

    protected $casts = [
        'name' => 'json',
        'slug' => 'string',

    ];

    protected $fillable = [
        'name',
        'slug',

    ];


    /* public static function boot()
     {
         parent::boot();

         self::creating(function ($model) {
             $slug['ar'] = Str::slug(request()->name['ar'], "-");
             $slug['en'] = Str::slug(request()->name['en'], "-");
             $model->attributes['slug'] = json_encode($slug);
         });
     }
     */

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'productGroupId');
    }

    public function allowedFilters(): array
    {
        return [
        ];
    }

    public function allowedSearchAttributes(): array
    {
        return [
            'name->ar',
            'name->en',
            'productGroupId',
            'createdAt',
            'updatedAt',

        ];
    }


}