<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use App\Traits\Models\CustomInteractsWithMedia;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\HasMedia;

/**
 * Class Slider
 *
 * @property int $sliderId
 * @property array $title
 * @property array $body
 * @property array $extra
 * @property array $metaTitle
 * @property array $metaDescription
 * @property Carbon $createdAt
 * @property Carbon $updatedAt

 *
 * @package App\Models
 */
class Slider extends BaseModel implements HasMedia
{
    use HasFactory, CustomInteractsWithMedia;

    const CREATED_AT = 'createdAt';
    const UPDATED_AT = 'updatedAt';
    protected $table = 'sliders';
    protected $primaryKey = 'sliderId';
    protected $perPage = 24;
    public static $snakeAttributes = false;
    public $imagesCollection = 'banner';



    protected $dates = [
        'publishedAt',
        'unPublishedAt'
    ];

    protected $casts = [
        'title' => 'json',
        'body' => 'json',
        'extra' => 'json',
        'metaTitle' => 'json',
        'metaDescription' => 'json'

    ];


    protected $fillable = [
        'title',
        'body',
        'extra',
        'metaTitle',
        'metaDescription',
        'isPublished',
        'publishedAt',
        'unPublishedAt'
    ];







}

