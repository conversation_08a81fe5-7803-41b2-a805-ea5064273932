<?php

namespace App\Exports;

use App\Repositories\Language\LanguageRepositoryInterface;
use App\Repositories\TranslationKey\TranslationKeyRepositoryInterface;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithCustomStartCell;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Events\BeforeSheet;
use PhpOffice\PhpSpreadsheet\Exception;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class TranslationsExport implements
    FromCollection,
    WithMapping,
    WithHeadings,
    WithCustomStartCell,
    WithEvents,
    WithStyles,
    WithColumnWidths
{
    private static string $source;
    private static string $target;

    public function __construct(
        protected TranslationKeyRepositoryInterface $translationKeyRepository,
        protected LanguageRepositoryInterface $languageRepository,
        protected string $sourceLanguage,
        protected string $targetLanguage,
    ) {
        static::$source = $this->sourceLanguage;
        static::$target = $this->targetLanguage;
    }

    public function map($staticTranslation): array
    {
        return [
            "$staticTranslation->group.$staticTranslation->key",
            $staticTranslation->languages->firstWhere('abbreviation', $this->sourceLanguage)?->pivot?->value?->value,
            $staticTranslation->languages->firstWhere('abbreviation', $this->targetLanguage)?->pivot?->value?->value,
        ];
    }

    public function headings(): array
    {
        return [
            'Key',
            $this->languageRepository->findByAbbreviation($this->sourceLanguage)?->name,
            $this->languageRepository->findByAbbreviation($this->targetLanguage)?->name,
        ];
    }

    /**
     * @return Collection
     */
    public function collection(): Collection
    {
        return $this->translationKeyRepository->getAllBasedAbbreviation($this->sourceLanguage);
    }

    public function startRow(): int
    {
        return 3;
    }

    public function startCell(): string
    {
        return 'A3';
    }

    public function registerEvents(): array
    {
        return [
            BeforeSheet::class => [self::class, 'beforeSheet'],
        ];
    }

    /**
     * @param BeforeSheet $event
     * @return void
     * @throws Exception
     */
    public static function beforeSheet(BeforeSheet $event)
    {
        $sheet = $event->getSheet();
        $sheet->getCell('B1')->setValue(static::$source);
        $sheet->getCell('A1')->setValue('Source');
        $sheet->getCell('B2')->setValue(static::$target);
        $sheet->getCell('A2')->setValue('Target');
    }

    public function styles(Worksheet $sheet)
    {
        return [
            "A1:A2" => [
                'fill' => [
                    'fillType' => 'solid',
                    'rotation' => 0,
                    'color' => ['rgb' => 'D9D9D9'],
                ],
                'font' => ['bold' => true]
            ],
            "A3:C3" => [
                'fill' => [
                    'fillType' => 'solid',
                    'rotation' => 0,
                    'color' => ['rgb' => 'D9D9D9'],
                ],
                'font' => ['bold' => true]
            ],
        ];
    }

    public function columnWidths(): array
    {
        return [
            'A' => 45,
            'B' => 45,
            'C' => 45,
        ];
    }
}