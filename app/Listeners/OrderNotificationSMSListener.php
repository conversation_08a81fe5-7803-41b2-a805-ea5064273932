<?php

namespace App\Listeners;

use App\Enums\SMSProvidersEnum;
use App\Events\OrderNotificationSMSEvent;
use App\SMSProviders\SMSProvidersFactory;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class OrderNotificationSMSListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle(OrderNotificationSMSEvent $event)
    {

        $phone = '';
        if (is_null($event->order->userId)) {
            $phone = $event->order->address->phone['code'] . $event->order->address->phone['number'];
        } else {
            $phone = $event->order->user->phone['code'] . $event->order->user->phone['number'];
        }
        $SMSGateway = SMSProvidersFactory::createSMSGateway(SMSProvidersEnum::arabiacell->value);
        $SMSGateway->send($phone, "Thank you for your purchase! Order #" . $event->order->orderId . " confirmed");

    }
}