<?php

namespace App\Listeners;

use App\Events\CreateProductEvent;
use App\Models\Alternative;
use App\Models\Bundle;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CreateProductListener
{
    public $product;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\CreateProductEvent  $event
     * @return void
     */
    public function handle(CreateProductEvent $event)
    {

        if ($event->product->type == "bundle") {
            Bundle::create([
                "productId" => $event->product->productId,
                'auctionId' => null
            ]);
        } else {
            Alternative::create([
                "productId" => $event->product->productId,
                'auctionId' => null
            ]);
        }
    }
}