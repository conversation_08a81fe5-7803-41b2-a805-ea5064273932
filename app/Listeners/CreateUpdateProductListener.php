<?php

namespace App\Listeners;

use App\Events\CreateUpdateProductEvent;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

use function Clue\StreamFilter\fun;

class CreateUpdateProductListener
{

    public $product;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(CreateUpdateProductEvent $event)
    {
        $this->product = $event->product;
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\CreateUpdateProductEvent  $event
     * @return void
     */
    public function handle(CreateUpdateProductEvent $event)
    {


        $now = Carbon::now("Asia/Amman");
        $timestamp1 = strtotime($now->toDateTimeString());


        foreach ($event->product->variances as $variance) {
            foreach ($variance->stocks->where('isOffer', true)->where('publishedAt', '>', $now) as $stock) {
                $jobExists = DB::table('job_meta_data')
                    ->where('productId', $event->product->productId)
                    ->where('stockId', $stock->stockId)
                    ->exists();

                if ($stock->publishedAt && !$jobExists) {
                    $jobUuid = (string) Str::uuid();
                    DB::table('job_meta_data')->insert([
                        'job_uuid' => $jobUuid,
                        'productId' => $event->product->productId,
                        'stockId' => $stock->stockId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    $timestamp2 = strtotime($stock->publishedAt);
                    if ($timestamp2 > $timestamp1) {
                        $differenceInSeconds = $timestamp2 - $timestamp1;
                        dispatch(job: new \App\Jobs\StocksJob(product: $event->product, stock: $stock, variance: $variance))
                            ->onQueue('high')->delay($differenceInSeconds)->afterDispatch(function () use ($event) {

                                dd('after dispatch');
                                // $payload = $processed->job->payload();
                                // $command = unserialize($payload['data']['command']);
    
                                // if (
                                //     property_exists($command, 'mailable') &&
                                //     method_exists($command->mailable, 'completed')
                                // ) {
                                //     $command->mailable->completed();
                                // }
    
                                // if (method_exists($command, 'completed')) {
                                //     $command->completed();
                                // }
                            });

                    }
                }

            }

            foreach ($variance->stocks->where('isOffer', true)->where('publishedAt', '<', 'now()')->where('unPublishedAt', '>', $now) as $stock) {

                $jobExists = DB::table('job_meta_data')
                    ->where('productId', $event->product->productId)
                    ->where('stockId', $stock->stockId)
                    ->exists();
                if ($stock->unPublishedAt && !$jobExists) {

                    $jobUuid = (string) Str::uuid();
                    DB::table('job_meta_data')->insert([
                        'job_uuid' => $jobUuid,
                        'productId' => $event->product->productId,
                        'stockId' => $stock->stockId,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                    $timestamp2 = strtotime($stock->unPublishedAt);
                    $differenceInSeconds = $timestamp2 - $timestamp1;

                    dispatch(job: new \App\Jobs\StocksJob(product: $event->product, stock: $stock, variance: $variance))
                        ->onQueue('high')->delay($differenceInSeconds)->appendPayload(['jobUuid' => $jobUuid]);




                }


            }
        }


    }
}