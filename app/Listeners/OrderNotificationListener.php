<?php

namespace App\Listeners;


use App\Models\Notification;
use App\Enums\WayNotificationEnum;
use App\Jobs\NotificationOrderJob;
use App\Events\OrderNotificationEvent;



class OrderNotificationListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\OrderNotificationEvent  $event
     * @return void
     */
    public function handle(OrderNotificationEvent $event)
    {
    
        $data = collect($event->data);
        Notification::create($data->except(['phone','email'])->toArray());  
        dispatch(new NotificationOrderJob($data->toArray(), WayNotificationEnum::getAll()));

    }
}
