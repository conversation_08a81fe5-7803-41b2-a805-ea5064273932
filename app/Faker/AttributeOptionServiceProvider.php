<?php

namespace App\Faker;

use Faker\Provider\Base;;

class AttributeOptionServiceProvider extends Base
{
    protected static $names = [
        "2,4,8,12",
        "1,2",
        "1000,2000,5000",
        "1,2,3,4,5",
        "photo accessories,camera",
        "television,receiver",
        "keyboard,mouse,cases,pc and laptops",
        "Airpods ,Airpods Covers,Smart Watch,Headphones,Smart Watch Accessories,"

    ];

    public function attributeOptionName(): string
    {
        return static::randomElement(static::$names);
    }
}