<?php

namespace App\Faker;

use Faker\Provider\Base;;

class AttributeServiceProvider extends Base
{
    protected static $names = [
        "Ram",
        'Screen Size',
        'Main Camera',
        'Type',
        'Processor Cache',
        'Operation System',
        'Display Technology',
        'Graphic Memory',
        'Storage Capacity',
        'Keyboard',
        'Sim Card',
        'RAM Capacity',
        'Inputs & Outputs'

    ];

    public function attributeName(): string
    {
        return static::randomElement(static::$names);
    }
}