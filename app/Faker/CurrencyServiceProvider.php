<?php

namespace App\Faker;

use Faker\Provider\Base;


class CurrencyServiceProvider extends Base
{
    protected static $currency = [
        "JOD",
        "USD",
        "SAR"
    ];

    protected static $symbol = [
        "د.أ",
        "$",
        "ريال"
    ];

    public function currencyName(): string
    {
        return static::randomElement(static::$currency);
    }
    public function symbolName(): string
    {
        return static::randomElement(static::$symbol);
    }
}