<?php

namespace App\Faker;

use Faker\Provider\Base;;

class ColorServiceProvider extends Base
{
    protected static $names = [
        'Black',
        'Blue',
        'Yellow',
        'Grey',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
        'Orange',
        'Rose gold',
        '<PERSON>',
        'Aurora blue',
        'Ice jadeite',
        'Quetzal cyan',
        'yellowish green',


    ];


    protected static $hexCode = [
        '#262626',
        '#a610eb',
        '#f5d2a6',
        '#1fdba3',
        '#115ef7',
        '#a610eb',
        '#871077',
        '#d5ad36',
        '#A98D41',
        '#A98D41',
        '#ff7700',
        '#ff8b26',
        '#ffa538'
    ];


    public function colorName(): string
    {
        return static::randomElement(static::$names);
    }

    public function hexCode(): string
    {
        return static::randomElement(static::$hexCode);
    }
}