<?php

namespace App\Jobs;

use App\Models\Product;
use App\Models\Stock;
use App\Models\Variance;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class StocksJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $product;
    private $stock;
    private $variance;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Product $product, Stock $stock, Variance $variance)
    {
        $this->product = $product;
        $this->stock = $stock;
        $this->variance = $variance;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        foreach ($this->product->variances as $variance) {
            $variance->searchable();

            foreach ($variance->stocks->where('isOffer', true) as $stock) {

                $stock->update([
                    'price' => $stock->priceBeforeOffer,
                    'isOffer' => false,
                    'publishedAt' => null,
                    'unPublishedAt' => null
                ]);

                DB::table('job_meta_data')
                    ->where('productId', $this->product->productId)
                    ->where('stockId', $stock->stockId)
                    ->delete();
            }

        }

        $this->product->searchable();
    }




}