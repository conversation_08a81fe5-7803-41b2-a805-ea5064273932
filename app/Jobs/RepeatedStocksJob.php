<?php

namespace App\Jobs;

use App\Models\Stock;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RepeatedStocksJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {


        $stocks = Stock::where('isOffer', 1)
            ->where('unPublishedAt', '<', now())
            ->get();

        foreach ($stocks as $stock) {
            $stock->update(['price' => $stock->priceBeforeOffer, 'isOffer' => false, 'publishedAt' => null, 'unPublishedAt' => null, 'priceBeforeOffer' => null]);
            foreach ($stock->variances as $variance) {
                if (!is_null($variance->product)) {
                    $variance->product->touchWithCalculation();
                }
            }
        }




    }
}