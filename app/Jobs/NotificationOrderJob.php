<?php

namespace App\Jobs;

use App\Models\FCMToken;
use Illuminate\Bus\Queueable;
use App\Enums\SMSProvidersEnum;
use App\Enums\WayNotificationEnum;
use App\Mail\NotificationOrderMail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Sebastian<PERSON><PERSON><PERSON>n\Invoker\Exception;
use App\SMSProviders\SMSProvidersFactory;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Notifications\SendOrderNotification;
use Kutia\Larafirebase\Facades\Larafirebase;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class NotificationOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $ways;
    protected $data;
    protected $deviceTokens;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data , $ways)
    {
        Log::info('__construct: ' . json_encode($this->ways));
        $this->ways=$ways;
        $this->data= $data;
        if(in_array('fcm',$this->ways)){
            $this->deviceTokens= FCMToken::where('userId',$this->data['userId'])->get()->pluck('token');
        }

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {   
    
        foreach($this->ways as $way){
         

            switch ($way) {
                case WayNotificationEnum::sms->value:
                     // send sms 
                     $SMSGateway = SMSProvidersFactory::createSMSGateway(SMSProvidersEnum::arabiacell->value);
                     $SMSGateway->send($this->data['phone'],$this->data['title']['ar']);
                    break; 
                case WayNotificationEnum::fcm->value:
                       // send fcm 
                    Larafirebase::withTitle($this->data['title']['ar'])
                    ->withBody($this->data['body']['ar'])
                    ->withImage('https://firebase.google.com/images/social.png')
                    ->withIcon('https://seeklogo.com/images/F/firebase-logo-402F407EE0-seeklogo.com.png')
                    ->withSound('default')
                    ->withClickAction('https://www.google.com')
                    ->withPriority('high')
                    ->withAdditionalData([
                        'color' => '#rrggbb',
                        'badge' => 0,
                    ])
                    ->sendNotification($this->deviceTokens);
                    break;

                case WayNotificationEnum::email->value:
                    // send email 
                    
                    try{
                        Mail::to($this->data['email'])->send(new NotificationOrderMail($this->data));
                    }catch(Exception $e){
                        Log::info('email error : ' . $e->getMessage());
                    }

                    
                break;
                  
            }
        }
    

    }
}
