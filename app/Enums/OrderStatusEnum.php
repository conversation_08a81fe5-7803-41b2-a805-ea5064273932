<?php

namespace App\Enums;

use App\Traits\Common\GetterEnumTrait;

//filter types are: price, attribute, brand, category, rating, etc.
enum OrderStatusEnum: string
{

    use GetterEnumTrait;

    case draft = 'draft';
    case received = 'received';
    case processing = 'processing';
    case canceled = 'canceled';
    case completed = 'completed';
    case editing = 'editing';
    case failed = 'failed';
    case refunded = 'refunded';
    case forRefund = 'forRefund';



}