<?php

namespace App\Enums;

// "radio", "checkbox", "chips", "brand", "range", "color", "text", "select", "number"

use App\Traits\Common\GetterEnumTrait;

enum ComponentTypeEnum: string
{

    use GetterEnumTrait;
    case radio = 'radio';
    case checkbox = 'checkbox';
    case chips = 'chips';
    case brand = 'brand';
    case range = 'range';
    case color = 'color';
    // case text = 'text'; //not used
    case select = 'select';
    // case number = 'number'; //Not used
    case price = 'price';
}