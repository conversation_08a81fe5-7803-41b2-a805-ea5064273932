<?php

namespace App\Enums;

use App\Traits\Common\GetterEnumTrait;

//filter types are: price, attribute, brand, category, rating, etc.
enum OrderItemStatusEnum: string
{

    use GetterEnumTrait;

    case completed = 'completed';
    case refunded = 'refunded';
    case cancelled = 'cancelled';
    case failed = 'failed';
    case delivered = 'delivered';
    case forDelivery = 'forDelivery';
    case reseed = 'reseed';





}