<?php

namespace App\Helper;

use App\Exceptions\GeneralException;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class InternalRequestHelper
{
    /**
     * @param Request $request
     */
    public function __construct(protected Request $request)
    {
    }

    /**
     * @throws GeneralException
     */
    public function post(string $path, array $body = [])
    {
        if (!Str::startsWith($path, '/')) {
            $path = '/' . $path;
        }

        $postRequest = $this->request->create(http_host_name() . $path, 'POST');

        $postRequest->request->add($body);

        $response = app()->handle($postRequest);

        $data = json_decode($response->getContent(), true);

        if ($response->isSuccessful()) {
            return $data;
        } elseif (blank($data) || isset($data['error'])) {
            throw new GeneralException(
                message: $data['error'] . ' | ' . $data['error_description'],
                code: $response->getStatusCode()
            );
        }

        return $data;
    }
}