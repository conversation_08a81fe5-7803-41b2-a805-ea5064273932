<?php
namespace App\Helper;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SlugHelper
{
    public static function makeSlug($string = [], $separator = "-")
    {


        if (is_null($string)) {
            return "";
        }
        // ddh($string);
        $string["ar"] = isset($string["ar"]) ? strtolower($string["ar"]) : '';
        $string["en"] = isset($string["en"]) ? strtolower($string["en"]) : '';

        $string = preg_replace("/[^a-z0-9_\s\-ءاآؤئبيتثجچحخدذرزژسشصضطظعغفقكکگلمنوهی]/u", '', $string);

        // Remove multiple dashes or whitespaces
        $string = preg_replace("/[\s-]+/", " ", $string);

        // Convert whitespaces and underscore to the given separator
        $string = preg_replace("/[\s_]/", $separator, $string);

        return $string;
    }




    public static function makeSlugFromString($string, $separator = "-")
    {
        // Convert to UTF-8 encoding
        $string = mb_convert_encoding($string, 'UTF-8', 'auto');

        // Replace Arabic and non-Latin characters with approximate ASCII equivalents
        $string = self::convertArabicToASCII($string);

        // Replace any non-alphanumeric characters with spaces
        $string = preg_replace('/[^A-Za-z0-9\s]/u', '', $string);

        // Replace spaces or multiple separators with a single separator
        $slug = preg_replace('/[\s]+/', $separator, $string);

        // Trim any leading or trailing separators
        $slug = trim($slug, $separator);

        // Convert to lowercase
        return strtolower($slug);
    }

    private static function convertArabicToASCII($string)
    {
        $replacements = [
            'ا' => 'a',
            'ب' => 'b',
            'ت' => 't',
            'ث' => 'th',
            'ج' => 'j',
            'ح' => 'h',
            'خ' => 'kh',
            'د' => 'd',
            'ذ' => 'dh',
            'ر' => 'r',
            'ز' => 'z',
            'س' => 's',
            'ش' => 'sh',
            'ص' => 's',
            'ض' => 'd',
            'ط' => 't',
            'ظ' => 'z',
            'ع' => 'a',
            'غ' => 'gh',
            'ف' => 'f',
            'ق' => 'q',
            'ك' => 'k',
            'ل' => 'l',
            'م' => 'm',
            'ن' => 'n',
            'ه' => 'h',
            'و' => 'w',
            'ي' => 'y',
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $string);
    }







    public static function slugChecker($slug, $tableName)
    {

        $count = DB::table($tableName)->select('*')
            ->where("slug", '=', $slug)
            ->count();

        $loop = $count ? true : false;
        $index = 1;

        while ($loop) {
            $newSlug = $slug . "-$index";
            $count = DB::table($tableName)
                ->where("slug", '=', $newSlug)
                ->count();

            if ($count == 0) {
                $slug = $newSlug;
                $loop = false;
            }
            $index++;
        }

        return $slug;

    }



    public static function slugCheckerWithinCondition($slug, $tableName, $condition)
    {

        $count = DB::table($tableName)->select('*')
            ->where("slug", '=', $slug)
            ->where($condition)
            ->count();

        $loop = $count ? true : false;
        $index = 1;

        while ($loop) {
            $newSlug = $slug . "-$index";
            $count = DB::table($tableName)
                ->where("slug", '=', $newSlug)
                ->where($condition)
                ->count();

            if ($count == 0) {
                $slug = $newSlug;
                $loop = false;
            }
            $index++;
        }

        return $slug;

    }



    public static function slugCheckedCategory($slug, $parentId)
    {

        $count = DB::table('categories')->select('*')
            ->where("slug", '=', $slug)
            ->where('parentId', '=', $parentId)
            ->count();

        $loop = $count ? true : false;
        $index = 1;
        while ($loop) {
            $newSlug = $slug . "-$index";
            $count = DB::table('categories')
                ->where("slug", '=', $newSlug)
                ->where('parentId', '=', $parentId)
                ->count();

            if ($count == 0) {
                $slug = $newSlug;
                $loop = false;
            }
            $index++;
        }

        return $slug;

    }



}