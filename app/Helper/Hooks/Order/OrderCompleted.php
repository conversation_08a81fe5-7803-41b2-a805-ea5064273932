<?php

namespace App\Helper\Hooks\Order;

use App\Enums\DeliveryStatusEnum;
use App\Enums\ProductTypeEnum;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class OrderCompleted
{

    /*************  ✨ Codeium Command ⭐  *************/
    /**
     * Registers the hook that is called when an order is completed.
     *
     * The hook is called with the order instance as an argument.
     *
     * The hook is used to generate deliveries, generate order invoice, decrement stock and update the number of orders for the user.
     */
    /******  58880652-2d13-43b7-a85e-49591567228b  *******/
    public static function register()
    {
        add_filter('order.completed', function ($order) {
            return self::apply($order);
        }, 10, 10);
    }

    public static function apply($order)
    {
        self::generateDeliveries($order);
        self::generateOrderInvoice($order);
        self::decrementStock($order);
        self::numberOfOrders($order);
        return $order;
    }



    public static function numberOfOrders($order)
    {
        if (!is_null($order->user)) {
            $user = $order->user;
            $user->numberOfOrders = Order::query()->where('userId', $user->userId)->count();
            $user->save();
        }

        foreach ($order->orderItems as $orderItem) {
            $orderItem->model->product->increment('numberOfOrder');
        }


    }


    public static function generateDeliveries($order)
    {
        if (!$order->deliveries()->exists()) {
            $order->deliveries()->create([
                'orderId' => $order->orderId,
                'shippingCarrierId' => $order->shippingCarrierId,
                'price' => $order->shippingPrice,
                'status' => DeliveryStatusEnum::onDelivery->value,
                'addressId' => $order->addressId,
            ]);
        }


    }


    public static function decrementStock($order)
    {
        foreach ($order->orderItems as $orderItem) {
            if ($orderItem->model->product->type == ProductTypeEnum::bundle->value) {
                $orderItem->model->product->activeStock->decrement('quantity', $orderItem->model->quantity);
            } else {
                $orderItem->model->variance->activeStock->decrement('quantity', $orderItem->model->quantity);
            }
        }
    }


    public static function generateOrderInvoice($order)
    {
        if (!$order->invoices()->exists()) {

            $invoice = $order->invoices()->create([
                'total' => $order->total,
                'status' => "active",
                'versionNumber' => DB::raw("(SELECT IFNULL(MAX(inv.versionNumber),0) + 1 from invoices as inv where inv.orderId = $order->orderId)"),
                'issueDate' => Carbon::now(),
                'paymentStatus' => $order->paymentStatus
            ]);
            foreach ($order->orderItems as $orderItem) {

                $invoiceItem = $invoice->invoiceItems()->create([
                    'invoiceId' => $orderItem->model->invoiceId,
                    'orderItemId' => $orderItem->orderItemsId,
                    'quantity' => $orderItem->model->quantity,
                    'unitPrice' => $orderItem->model->price,
                ]);
            }

        }

    }



}