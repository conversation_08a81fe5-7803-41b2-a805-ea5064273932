<?php 

namespace App\Helper\Hooks\Order;
use App\Enums\DiscountTypeEnum;
use App\Models\Offers\ProductStillInCart as ProductStillInCartModel;

class ProductStillInCart
{
 
   

    public static function register()
    {
        add_filter('order.after-still-cart-order', function ($order) {

            return self::apply($order);
        }, 20, 1);
    }


    public static function apply($order){
        
        $model= new ProductStillInCartModel();
        $productStillInCart =$model->isPublished()->first();
        if(is_null($productStillInCart)){
            return $order;   
        }
        $jsonProductIds = $productStillInCart->jsonProductIds;
        $total = (float) $order->total ;
        $totalDiscount = 0;
        foreach($order->orderItems as $key => $item){
            if( in_array($item->productId,$jsonProductIds )){
                $discount =  $productStillInCart->discountType== DiscountTypeEnum::fixed->value ? (float)  $productStillInCart->discount : (float)  $item->price * (float) $productStillInCart->discount;
                $price =  (float) $item->price  -  $discount;
                $item->update([
                    'price'=>$price,
                    'discount'=>  $discount
                ]);
                $totalDiscount += $discount;
            } 
         }
        $order->total= $total - $totalDiscount;
        $order->save();
        return $order;
    }

    
    
}
