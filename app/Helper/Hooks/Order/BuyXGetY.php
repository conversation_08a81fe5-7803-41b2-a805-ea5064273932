<?php

namespace App\Helper\Hooks\Order;

use App\Models\OrderItem;
use App\Enums\OrderItemStatusEnum;
use App\Models\Offers\BuyXGetY as BuyXGetYModel;

class BuyXGetY // TBD: This should be buy X get Y
{

    public static function register()
    {
        add_filter('order.buy-x-get-y', function ($items) {
            return self::apply($items);
        }, 20, 1);
    }

    public static function apply($items)
    {
        // $itemsIds = $items->pluck('productId')->toArray();

        // $buyOneGetOneFree = BuyXGetYModel::where('status', 1)
        //     ->where('published', 1)
        //     ->whereHas('product', function ($query) use ($itemsIds) {
        //         $query->whereIn('productIdX', $itemsIds);
        //     })
        //     ->first();

        // if (is_null($buyOneGetOneFree)) {
        //     return $items;
        // }

        // // ids of found products that has buy one get one free
        // $buyOneGetOneFreeIds = $buyOneGetOneFree->jsonProductIds;
        // return $items->map(function ($item) use ($buyOneGetOneFreeIds) {
        //     //increase quantity of item if it is in buy one get one free
        //     if (in_array($item->productId, $buyOneGetOneFreeIds)) {
        //         $item->quantity = $item->quantity + 1;
        //     }

        //     return $item;


        // });

    }



}
