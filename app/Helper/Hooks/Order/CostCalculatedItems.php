<?php



namespace App\Helper\Hooks\Order;


class CostCalculatedItems
{

    public static function register()
    {
        add_filter('order.total-calculated-items', function ($params) {
            return self::apply($params);
        }, 20, 1);
    }

    public static function apply($params)
    {

        $totalCost = 0;

        foreach ($params[1] as $key => $item) {
            $type = $item->product->type ?? '';
            $cost = 0;
            if ($type == 'bundle') {
                $cost = $item->product->activeStock->cost
                    ?? $item->product->lastStock->cost
                    ?? 0;


            } else {
                $cost = (float) $item?->variance?->activeStock?->cost
                    ?? $item?->variance?->lastStock?->cost
                    ?? 0;
                ;
            }
            $totalCost += $cost * (int) $item->quantity;

            //  $total += (float) ($item->variance?->activeStock?->price?->price ?? 0) * (float) $item->quantity;
        }

        return $totalCost;
    }

}