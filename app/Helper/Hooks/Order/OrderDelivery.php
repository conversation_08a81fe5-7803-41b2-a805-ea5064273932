<?php

namespace App\Helper\Hooks\Order;

use App\Enums\DeliveryStatusEnum;



class OrderDelivery
{

    public static function register()
    {
        add_filter('order.deliveries.received', function ($order) {
            if (!$order->deliveries()->exists()) {
                return self::apply($order);
            }
        }, 10, 10);
    }

    public static function apply($order)
    {
        $order->deliveries()->create([
            'orderId' => $order->orderId,
            'shippingCarrierId' => $order->shippingCarrierId,
            'price' => $order->shippingPrice,
            'status' => DeliveryStatusEnum::onDelivery->value,
            'addressId' => $order->addressId,
        ]);

        return $order;
    }



}
