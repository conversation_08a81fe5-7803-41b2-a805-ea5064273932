<?php

namespace App\Helper\Hooks\Order;


use App\Enums\InvoicePaymentStatusEnum;
use App\Enums\InvoiceStatusEnum;
use App\Enums\OrderPaymentStatusEnum;
use App\Enums\TransactionStatusEnum;
use App\Models\Invoice;
use App\PaymentMethod\PaymentGatewayFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class InvoiceOrder
{

    public static function register()
    {
        add_filter('order.generate-order-invoice', function ($order, $amount, $isPaid) {
            return self::apply($order, $amount, $isPaid);
        }, 20, 1);


        // do when invoice received
        add_filter('order.invoices.received', function ($order) {
            if (!$order->invoices()->exists()) {
                return self::generateOrderInvoice($order);
            }
        }, 10, 10);



        add_filter('order.invoices.end.editing', function ($order) {
            return self::generateOrderInvoice($order, $order->total, false);
        }, 10, 10);


    }


    public static function generateOrderInvoice($order, float $amount = null, bool $isPaid = null)
    {

        if (is_null($isPaid)) {
            $paymentStatus = $order->paymentStatus;
        } else {
            $paymentStatus = $isPaid ? InvoicePaymentStatusEnum::paid->value : InvoicePaymentStatusEnum::unPaid->value;
        }

        if (is_null($amount)) {
            $amount = $order->total;
        }

        if ($order->invoices()->count()) {

            $amountPaid = $order->transactions->where('status', TransactionStatusEnum::success->value)->sum('amount');
            $amountUnPaid = $order->total - $amountPaid;

            $paymentGateway = PaymentGatewayFactory::createPaymentGateway($order->paymentsMethod->module);
            $paymentGateway->setAmount((float) $order->total);
            $paymentGateway->setOrderDetails($order);



            if ($amountPaid == $order->total) {
                $order->paymentStatus = OrderPaymentStatusEnum::paid->value;

                $order->invoices()->update([
                    'status' => InvoiceStatusEnum::canceled->value,
                ]);

            } elseif ($amountPaid == 0) {
                $order->paymentStatus = OrderPaymentStatusEnum::unPaid->value;

                $order->invoices()->update([
                    'status' => InvoiceStatusEnum::canceled->value,
                ]);

            } else {
                $order->paymentStatus = OrderPaymentStatusEnum::partialPaid->value;

            }
        }


        $invoice = $order->invoices()->create([
            'total' => $amount,
            'status' => InvoiceStatusEnum::active->value,
            'versionNumber' => DB::raw("(SELECT IFNULL(MAX(inv.versionNumber),0) + 1 from invoices as inv where inv.orderId = $order->orderId)"),
            'issueDate' => Carbon::now(),
            'paymentStatus' => $paymentStatus
        ]);


        foreach ($order->orderItems as $orderItem) {

            $invoiceItem = $invoice->invoiceItems()->create([
                'invoiceId' => $orderItem->model->invoiceId,
                'orderItemId' => $orderItem->orderItemsId,
                'quantity' => $orderItem->model->quantity,
                'unitPrice' => $orderItem->model->price,
            ]);
        }

        $order->save();
        return $invoice;

    }



    public static function apply($order, float $amount = null, bool $isPaid = null)
    {

        if (is_null($amount)) {
            $amount = $order->total;
        }
        if (is_null($isPaid)) {
            $paymentStatus = $order->paymentStatus;
        } else {
            $paymentStatus = $isPaid ? InvoicePaymentStatusEnum::paid->value : InvoicePaymentStatusEnum::unPaid->value;
        }

        $maxVersion = Invoice::where('orderId', 28)->max('versionNumber') ?? 0 + 1;

        $invoice = $order->invoices()->create([
            'total' => $amount,
            'status' => "active",
            'versionNumber' => $maxVersion,// DB::raw("(SELECT IFNULL(MAX(versionNumber),0) + 1 from invoices where orderId = $this->orderId)"),
            'issueDate' => Carbon::now(),
            'paymentStatus' => $paymentStatus
        ]);


        foreach ($order->orderItems as $orderItem) {
            $invoiceItem = $invoice->invoiceItems()->create([
                'invoiceId' => $orderItem->model->invoiceId,
                'orderItemId' => $orderItem->orderItemsId,
                'quantity' => $orderItem->model->quantity,
                'unitPrice' => $orderItem->model->price,
            ]);
        }

        return $invoice;

    }



}