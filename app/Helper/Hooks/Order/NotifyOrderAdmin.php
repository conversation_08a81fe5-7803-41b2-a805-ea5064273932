<?php

namespace App\Helper\Hooks\Order;


use App\Notifications\SendOrderNotification;
use Illuminate\Support\Facades\Notification;


class NotifyOrderAdmin // TBD: This should be buy X get Y
{

    public static function register()
    {
        add_filter('order.notify-admin-new-order', function ($order) {
            return self::apply($order);
        }, 20, 1);
    }

    public static function apply($order)
    {

        $title="asa";
        $message="ss";
        $fcmTokens=["sasasa"];
    
        Notification::send(null,new SendOrderNotification($title,$message,$fcmTokens));

    }



}
