<?php



namespace App\Helper\Hooks\Order;


class TotalCalculatedItems
{

    public static function register()
    {
        add_filter('order.total-calculated-items', function ($params) {
            return self::apply($params);
        }, 20, 1);
    }

    public static function apply($params)
    {

        $total = $params[0];

        foreach ($params[1] as $key => $item) {
            $type = $item->product->type ?? '';
            $price = 0;
            if ($type == 'bundle') {
                $price = $item->product->activeStock->price
                    ?? $item->product->lastStock->price
                    ?? 0;


            } else {
                $price = (float) $item?->variance?->activeStock?->price
                    ?? $item?->variance?->lastStock?->price
                    ?? 0;
                ;
            }
            $total += $price * (int) $item->quantity;

            //  $total += (float) ($item->variance?->activeStock?->price?->price ?? 0) * (float) $item->quantity;
        }

        return $total;
    }

}