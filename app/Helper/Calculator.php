<?php
namespace App\Helper;

use App\Enums\OrderItemStatusEnum;



class Calculator
{



    public static function totalFromCart($carts)
    {
        $total = 0;

        foreach ($carts as $key => $cart) {
            $type = $cart->product->type ?? '';
            $price = 0;
            if ($type == 'bundle') {
                $price = $cart->product->activeStock->price
                    ?? $cart->product->lastStock->price
                    ?? 0;

            } else {
                $price = $cart->variance->activeStock->price
                    ?? $cart->variance->lastStock->price
                    ?? 0;
            }
            $total += $price * (int) $cart->quantity;
        }
        return $total;
    }






    public static function subTotalOrder($order)
    {
        $total = 0;
        $price = 0;

        foreach ($order->orderItems as $orderItem) {
            if (OrderItemStatusEnum::cancelled->value !== $orderItem->status) {
                $type = $orderItem->model->product->type ?? '';
                $price = 0;
                if ($type == 'bundle') {
                    $price = (float) $orderItem->model->bundle->activeStock->price
                        ?? $orderItem->model->bundle->lastStock->price
                        ?? 0;
                } else {
                    $price = (float) $orderItem->model->variance->activeStock->price
                        ?? $orderItem->model->variance->lastStock->price
                        ?? 0;

                }

                $total += $price * (int) $orderItem->model->quantity;
            }

        }

        return $total;

    }


    public function total($order)
    {
        $subTotal = self::subTotalOrder($order);
        return $subTotal + $order->shippingPrice ?? 0;
    }






}




?>