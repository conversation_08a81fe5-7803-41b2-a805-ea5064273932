<?php

namespace App\Helper;

use Illuminate\Support\Facades\Storage;



class FileHelper
{

    protected $file;



    public function store($request, $path)
    {

        $this->file = $request->file;
        $data['src'] = Storage::disk(env('MEDIA_DISK'))->put($this->getDefaultTempPath(), $request->file);
        $data['preview'] = Storage::disk(env('MEDIA_DISK'))->url($data['src']);
        $data['fileSize'] = $this->file->getSize();
        $data['mimeType'] = $this->file->getMimeType();
        $data['disk'] = env('MEDIA_DISK');
        $request->replace(['image' => $path]);
        return $data;
    }


    public function getDefaultTempPath()
    {
        return 'temp';
    }


}
