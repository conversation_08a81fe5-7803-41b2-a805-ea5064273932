<?php

namespace App\Helper\ProviderSMS;

class ArabiacellHelper implements SMSInterfaceHelper
{
    /**
     *  send_message helper
     *
     * @param $message
     * @param  $mobile
     *
     */

    public function send($message, $mobile)
    {
        $pass = 'V2!zI1$HqZ2';
        $ch = curl_init("'https://notificationcenter.arabiacell.net/sms/api/SendSingleMessage.cfm?numbers=' . urlencode($mobile) . '&senderid=Action+Mob&AccName=action&AccPass=X6!zM3@JsU6&msg=' . urlencode($message) . '&requesttimeout=5000000'");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_exec($ch);
    }

    public function config()
    {

    }

}