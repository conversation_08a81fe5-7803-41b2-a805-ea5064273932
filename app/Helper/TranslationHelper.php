<?php

namespace App\Helper;

use App\Repositories\TranslationKey\TranslationKeyRepositoryInterface;
use Illuminate\Support\Str;

class TranslationHelper
{
    /**
     * Set translations keys in cache for .
     */
    const CACHED_FOR = '1 month';

    /**
     * @param CacheHelper $cacheHelper
     * @param TranslationKeyRepositoryInterface $translationKeyRepository
     */
    public function __construct(
        protected CacheHelper $cacheHelper,
        protected TranslationKeyRepositoryInterface $translationKeyRepository
    ) {
    }

    /**
     * Get the value of the given translation key .
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @return string
     */
    public function get(string $key, array $replace = [], string $locale = null): string
    {
        if (is_null($locale)) {
            $locale = current_locale();
        }

        $translations = $this->getTranslations($locale);

        $value = data_get($translations, $key);

        if (is_null($value)) {
            $locale = config('app.fallback_locale');

            $translations = $this->getTranslations($locale);

            $fallbackValue = data_get($translations, $key);

            if (is_null($fallbackValue)) {
                return $key;
            }

            $value = $fallbackValue;
        }

        return $this->makeReplacements($value, $replace);
    }

    /**
     * Make the place-holder replacements on a translation .
     *
     * @param string $line
     * @param array $replace
     * @return string
     */
    private function makeReplacements(string $line, array $replace): string
    {
        if (empty($replace)) {
            return $line;
        }

        $shouldReplace = [];

        foreach ($replace as $key => $value) {
            $shouldReplace['{' . Str::ucfirst($key ?? '') . '}'] = Str::ucfirst($value ?? '');
            $shouldReplace['{' . Str::upper($key ?? '') . '}'] = Str::upper($value ?? '');
            $shouldReplace['{' . $key . '}'] = $value;
        }

        return strtr($line, $shouldReplace);
    }

    /**
     * Get the translations based keys from cache .
     *
     * @param string $locale
     * @return mixed
     */
    private function getTranslations(string $locale): mixed
    {
        return $this->cacheHelper->remember(
            key: "translations.{$locale}",
            callback: function () use ($locale) {
                return $this->translationKeyRepository->getAllBasedKeys($locale);
            },
            tags: ['translation_keys', 'translation_values'],
            ttl: \DateInterval::createFromDateString(self::CACHED_FOR),
        );
    }
}