<?php

namespace App\Helper;

use Closure;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class CacheHelper
{
    /**
     * @var bool
     */
    private bool $cacheEnabled;

    /**
     * @var int
     */
    private int $ttl; //time to live

    /**
     * @var string
     */
    private string $currentCacheStore;

    /*   public function __construct()
       {
           $this->ttl = config('cache.default_ttl');
           $this->cacheEnabled = config('cache.enabled');
           $this->currentCacheStore = config('cache.default');
       }
       */

    /**
     * Get an item from the cache, or execute the given Closure and store the result.
     *
     * @param string|array|null $tags
     * @param string $key
     * @param Closure $callback
     * @param \DateInterval|\DateTimeInterface|int|null $ttl
     * @return mixed
     */
    public function remember(
        string $key,
        Closure $callback,
        string|array $tags = null,
        \DateInterval|\DateTimeInterface|int $ttl = null
    ): mixed {
        /*   if (is_null($ttl)) {
               $ttl = $this->ttl;
           }

           if (!$this->cacheEnabled) {
               return $callback->__invoke();
           }

           if (!blank($tags) && $this->isCacheTagsSupported()) {
               return Cache::tags($tags)->remember($key, $ttl, $callback);
           }
           */

        return Cache::remember($key, $ttl, $callback);
    }

    /**
     * Remove an key from the cache.
     *
     * @param string $key
     * @param string|array|null $tags
     * @return bool
     */
    public function forget(string|array $keys, string|array $tags = null): bool
    {
        //  if (!blank($tags) && $this->isCacheTagsSupported()) {
        //       Cache::tags($tags)->forget($key);
        //  }
        if (is_array($keys)) {
            foreach ($keys as $key => $key) {
                # code...
                Cache::forget($key);
            }
            return true;
        } else {
            return Cache::forget($keys);
        }

    }

    /**
     * Remove and flush all items under the given tags .
     *
     * @param string|array $tags
     * @return bool
     */
    public function flushTags(string|array $tags): bool
    {
        //  if (!$this->isCacheTagsSupported()) {
        //  return false;
        // }

        return Cache::tags($tags)->flush();
    }

    /**
     * Check if the current cache driver supported tags feature .
     *
     * @return bool
     */
    //  private function isCacheTagsSupported()://bool
    // {
    //  return in_array($this->currentCacheStore, ['redis', 'memcached']);
    // }


    public function deletePattern(string|array $patterns)
    {


        if (is_array($patterns)) {

            foreach ($patterns as $key => $pattern) {
                # code...
                Redis::connection('cache')->del(Redis::connection('cache')->keys($pattern));
                Redis::connection('default')->del(Redis::connection('default')->keys($pattern));
            }
        } else {
            Redis::connection('cache')->del(Redis::connection('cache')->keys($patterns));
            Redis::connection('default')->del(Redis::connection('default')->keys($patterns));
        }


    }


    public function forever(
        string $key,
        Closure $callback,
    ): mixed {

        return Cache::forever($key, $callback);
    }



}