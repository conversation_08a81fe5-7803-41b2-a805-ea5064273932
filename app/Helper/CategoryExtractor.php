<?php
namespace App\Helper;

use Illuminate\Support\Collection;

class CategoryExtractor
{
    /**
     * Extract category IDs recursively and return them as a collection.
     *
     * @param  array  $category
     * @return \Illuminate\Support\Collection
     */
    public function extractIds(array $category): Collection
    {
        $categoriesWithParents = collect([(string) $category['categoryId']]);

        if (isset($category['parent']) && $category['parent']) {
            $parentCategories = $this->extractIds($category['parent']);
            $categoriesWithParents = $categoriesWithParents->merge($parentCategories);
        }

        return $categoriesWithParents;
    }
}
