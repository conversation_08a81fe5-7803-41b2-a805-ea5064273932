<?php
namespace App\Helper;

use App\Models\Currency as CurrencyModel;
use Illuminate\Support\Facades\Cache;

class Currency
{

    private $originalCurrency;
    private $originalPrice;

    private $originalBasePrice;

    private $originalBaseCurrency = 'JOD';

    private $newCurrency;

    private $newPrice;
    private static $cachedCurrencies = null;

    private $currencies = [];
    public function __construct()
    {

        if (is_null(self::$cachedCurrencies)) {

            self::$cachedCurrencies = Cache::rememberForever('currencies', function () {
                return CurrencyModel::all();
            });
        }


        $this->currencies = self::$cachedCurrencies;
    }

    public static function convert($originalPrice)
    {
        $currency = new Currency();
        $currency->originalPrice = $originalPrice;
        return $currency;

    }

    public function from($originalCurrency)
    {
        $this->originalCurrency = $originalCurrency;
        return $this;
    }

    public function fromBase()
    {
        $this->originalCurrency = $this->originalBaseCurrency;
        return $this;
    }

    public function toBase()
    {
        // convert to $originalBasePrice and $originalBaseCurrency and return $this to chain
        // find from collection
        $originalCurrencyData = [];
        if (is_numeric($this->originalCurrency)) {
            $originalCurrencyData = $this->currencies->filter(function ($value, $key) {
                return $value->currencyId == $this->originalCurrency;
            })->first();
        } else {
            $originalCurrencyData = $this->currencies->filter(function ($value, $key) {
                return $value->name == $this->originalCurrency;
            })->first();
        }
        // calculate new price
        $this->originalBasePrice = $this->originalPrice / $originalCurrencyData->valueToBasePrice;
        //in case there is no no to() in the chain
        $this->newCurrency = $this->originalBaseCurrency;
        $this->newPrice = $this->originalBasePrice;

        // return new price
        return $this;
    }

    public function to($currency)
    {
        if (!$currency)
            throw new \Exception('Currency not found');

        $this->toBase();

        $this->newCurrency = $currency;
        // find from collection
        $newCurrencyData = $this->currencies->filter(function ($value, $key) use ($currency) {
            return $value->name == $this->newCurrency;
        })->first();

        // if currency not found, return original price
        // if (!$newCurrencyData) {
        //     return $this->originalBasePrice;
        // }

        // calculate new price
        $this->newPrice = $this->originalBasePrice * $newCurrencyData->valueToBasePrice;

        // return new price
        return $this;
    }

    public function get()
    {
        return round($this->newPrice, 3);
    }

    public function getBasePrice()
    {
        return round($this->originalBasePrice, 3);
    }

    public function symbol()
    {
        // find from collection
        $newCurrencyData = $this->currencies->filter(function ($value, $key) {
            return $value->name == $this->newCurrency;
        })->first();
        // dd($this->currencies);
        // return new price
        return $newCurrencyData->symbol;
    }


    public function getCurrencyId()
    {
        // find from collection
        $newCurrencyData = $this->currencies->filter(function ($value, $key) {
            return $value->name == $this->newCurrency;
        })->first();
        // dd($this->currencies);
        // return new price
        return $newCurrencyData->currencyId;
    }

    public function getOriginalPrice()
    {
        return $this->originalPrice;
    }

    public function getOriginalCurrency()
    {
        return $this->originalCurrency;
    }

}