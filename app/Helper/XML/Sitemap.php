<?php
namespace App\Helper\XML;

use Illuminate\Support\Facades\Storage;
use Spatie\Sitemap\Sitemap as BaseSitemap;

class Sitemap extends BaseSitemap
{
    public function writeToDisk(string $disk, string $path): static
    {
        $xml = $this->render();
        $xml = preg_replace('/<priority>.*?<\/priority>/', '', $xml);
        $xml = preg_replace('/\s*hreflang="(en|ar)"\s*/', ' ', $xml);
        Storage::disk($disk)->put($path, $xml);
        return $this;
    }
}