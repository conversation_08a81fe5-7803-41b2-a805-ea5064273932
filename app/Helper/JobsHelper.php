<?php

namespace App\Helper;

use App\Jobs\StocksJob;
use Carbon\Carbon;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;



class JobsHelper
{



    public static function dispatchSync($product, $stocks)
    {
        $now = Carbon::now("Asia/Amman");


        $timestamp1 = strtotime($now->toDateTimeString());

        foreach ($stocks as $stock) {
            if (!is_null($stock['publishedAt']) && Carbon::parse($stock['publishedAt']) > $now) {
                $timestamp2 = strtotime($stock['publishedAt']);
                $differenceInSeconds = $timestamp2 - $timestamp1;
                dispatch(new \App\Jobs\StocksJob($product))->onQueue('high')->delay($differenceInSeconds);

            }
        }



    }


}
