<?php

namespace App\Helper;

class SortableHelper
{

    public static function sort($array)
    {
        foreach ($array as $key => $value) {
            $array[$key]['sort'] = $key;
        }

        return $array;
    }

    public static function sortCollecttion($collections)
    {

        foreach ($collections as $key => $collection) {
            foreach ($collection as $keySort => $value) {
                $collections[$key][$keySort]['sort'] = $keySort;
            }
        }
        return $collections;
    }


}
