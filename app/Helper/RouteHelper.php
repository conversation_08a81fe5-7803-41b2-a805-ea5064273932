<?php

namespace App\Helper;

use Illuminate\Support\Facades\Route;




class RouteHelper
{


    public static function getRoutesAdmin()
    {
        $routes = Route::getRoutes();

        return collect($routes)
            ->reject(function ($route) {
                $actionName = $route->getActionName();
                $methodSegments = explode('@', $actionName);
                $namespace = head($methodSegments);
                $head = head($methodSegments);
                $uri = $route->uri();
                return strpos($uri, 'telescope') || (strpos($head, 'App\Http\Controllers') === false && strpos($head, '\App\Http\Controllers') === false) || !is_admin_route($namespace);
            })->map(function ($route) {

                $actionName = $route->getActionName();
                $methodSegments = explode('@', $actionName);
                $methodName = last($methodSegments);
                $namespace = head($methodSegments);
                $uri = $route->uri();
                $name = $route->getName();
                // generate_route_name($route);
    
                $prefix = strpos($name, '/admin/') === false && is_admin_route($namespace) ? "admin." : "";

                $routeName = "$prefix$name";

                return [
                    'method' => implode('|', $route->methods()),
                    'uri' => $uri,
                    'name' => $name,
                    'action' => $actionName,
                    'methodName' => $methodName,
                    'routeName' => $routeName
                ];
            })->toArray();
        ;


    }





}
