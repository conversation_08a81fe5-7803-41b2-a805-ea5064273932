<?php
namespace App\Helper;



class ArrayHelper
{
    public static function appendArray(array $array, int $id, $sourceKey, string $targetKey)
    {


        if (isset($array) && is_array($array)) {
            return array_map(function ($relatedTargetProduct) use ($id, $targetKey, $sourceKey) {
                return [$targetKey => $relatedTargetProduct, $sourceKey => $id];
            }, $array);
        }


        return [];
    }



    public static function mapFormateded(array $array, int $id, $sourceKey, string $targetKey)
    {

        $arrayReturn = [];
        if (isset($array) && is_array($array)) {
            $arrayReturn = array_map(function ($relatedTarget) use ($id, $targetKey, $sourceKey) {
                return [$targetKey => $relatedTarget[$targetKey], $sourceKey => $id];
            }, $array);
        }
        return $arrayReturn;
    }


}
