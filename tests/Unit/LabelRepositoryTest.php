<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Label;
use App\Repositories\Label\LabelRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;

class LabelRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private LabelRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new LabelRepository();
    }

    /** @test */
    public function it_can_find_label_by_single_slug()
    {
        // Create test labels
        $label1 = Label::factory()->create(['slug' => 'electronics']);
        $label2 = Label::factory()->create(['slug' => 'books']);

        $result = $this->repository->findBySlugLabel('electronics');

        $this->assertNotNull($result);
        $this->assertEquals($label1->labelId, $result->labelId);
        $this->assertEquals('electronics', $result->slug);
    }

    /** @test */
    public function it_returns_null_when_label_not_found()
    {
        $result = $this->repository->findBySlugLabel('non-existent');

        $this->assertNull($result);
    }

    /** @test */
    public function it_can_get_labels_by_array_of_slugs()
    {
        // Create test labels
        $label1 = Label::factory()->create(['slug' => 'electronics']);
        $label2 = Label::factory()->create(['slug' => 'books']);
        $label3 = Label::factory()->create(['slug' => 'clothing']);

        $result = $this->repository->getLabelsBySlug(['electronics', 'books']);

        $this->assertCount(2, $result);
        $this->assertTrue($result->contains('slug', 'electronics'));
        $this->assertTrue($result->contains('slug', 'books'));
        $this->assertFalse($result->contains('slug', 'clothing'));
    }

    /** @test */
    public function it_can_get_labels_by_comma_separated_string()
    {
        // Create test labels
        $label1 = Label::factory()->create(['slug' => 'electronics']);
        $label2 = Label::factory()->create(['slug' => 'books']);
        $label3 = Label::factory()->create(['slug' => 'clothing']);

        $result = $this->repository->getLabelsBySlug('electronics,books');

        $this->assertCount(2, $result);
        $this->assertTrue($result->contains('slug', 'electronics'));
        $this->assertTrue($result->contains('slug', 'books'));
        $this->assertFalse($result->contains('slug', 'clothing'));
    }

    /** @test */
    public function it_can_get_labels_by_single_string()
    {
        // Create test labels
        $label1 = Label::factory()->create(['slug' => 'electronics']);
        $label2 = Label::factory()->create(['slug' => 'books']);

        $result = $this->repository->getLabelsBySlug('electronics');

        $this->assertCount(1, $result);
        $this->assertTrue($result->contains('slug', 'electronics'));
        $this->assertFalse($result->contains('slug', 'books'));
    }

    /** @test */
    public function it_handles_whitespace_in_comma_separated_string()
    {
        // Create test labels
        $label1 = Label::factory()->create(['slug' => 'electronics']);
        $label2 = Label::factory()->create(['slug' => 'books']);

        $result = $this->repository->getLabelsBySlug('electronics, books, non-existent');

        $this->assertCount(2, $result);
        $this->assertTrue($result->contains('slug', 'electronics'));
        $this->assertTrue($result->contains('slug', 'books'));
    }

    /** @test */
    public function it_can_get_label_ids_by_slugs()
    {
        // Create test labels
        $label1 = Label::factory()->create(['slug' => 'electronics']);
        $label2 = Label::factory()->create(['slug' => 'books']);

        $result = $this->repository->getLabelIdsBySlugs(['electronics', 'books']);

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertContains($label1->labelId, $result);
        $this->assertContains($label2->labelId, $result);
    }

    /** @test */
    public function it_returns_empty_array_when_no_labels_found()
    {
        $result = $this->repository->getLabelIdsBySlugs(['non-existent-1', 'non-existent-2']);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /** @test */
    public function it_can_get_label_ids_by_comma_separated_string()
    {
        // Create test labels
        $label1 = Label::factory()->create(['slug' => 'electronics']);
        $label2 = Label::factory()->create(['slug' => 'books']);

        $result = $this->repository->getLabelIdsBySlugs('electronics,books');

        $this->assertIsArray($result);
        $this->assertCount(2, $result);
        $this->assertContains($label1->labelId, $result);
        $this->assertContains($label2->labelId, $result);
    }
}
