<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use App\Repositories\Product\ProductRepositoryInterface;
use App\Models\Product;

class ProductRepositoryOptimizationTest extends TestCase
{
    use RefreshDatabase;

    protected $productRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->productRepository = app(ProductRepositoryInterface::class);
    }

    /**
     * Test that getProductDetails method works without N+1 queries
     * This test verifies that our optimization doesn't break functionality
     * and reduces the number of database queries.
     */
    public function test_get_product_details_optimization()
    {
        // Skip if no products exist in database
        $product = Product::with(['variance', 'variances'])->first();

        if (!$product) {
            $this->markTestSkipped('No products found in database for testing');
        }

        // Enable query logging to count queries
        DB::enableQueryLog();

        try {
            // Call the optimized method
            $result = $this->productRepository->getProductDetails($product->productId);

            // Get the number of queries executed
            $queries = DB::getQueryLog();
            $queryCount = count($queries);

            // Verify the result structure is correct
            $this->assertNotNull($result);
            $this->assertEquals($product->productId, actual: $result->productId);
            $this->assertObjectHasAttribute('variationsAttributes', $result);
            $this->assertObjectHasAttribute('paymentMethods', $result);

            // Log the number of queries for monitoring
            // In a real optimization, we'd compare this with the previous count
            echo "\nNumber of queries executed: " . $queryCount . "\n";

            // Basic assertion that we don't have an excessive number of queries
            // This number should be significantly lower after optimization
            $this->assertLessThan(
                50,
                $queryCount,
                "Too many queries executed. Possible N+1 query issue."
            );

        } catch (\Exception $e) {
            // If the method fails, we want to know about it
            $this->fail("getProductDetails method failed: " . $e->getMessage());
        } finally {
            DB::disableQueryLog();
        }
    }

    /**
     * Test that the method handles different product types correctly
     */
    public function test_get_product_details_handles_different_types()
    {
        // Test with different product types if they exist
        $productTypes = ['simple', 'alternative', 'bundle'];

        foreach ($productTypes as $type) {
            $product = Product::where('type', $type)->with(['variance', 'variances'])->first();

            if ($product) {
                try {
                    $result = $this->productRepository->getProductDetails($product->productId);
                    $this->assertNotNull($result);
                    $this->assertEquals($type, $result->type);
                    echo "\nSuccessfully tested product type: " . $type . "\n";
                } catch (\Exception $e) {
                    $this->fail("Failed to get details for product type '$type': " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Test that the method works with product slugs as well as IDs
     */
    public function test_get_product_details_with_slug()
    {
        $product = Product::whereNotNull('slug')->first();

        if (!$product) {
            $this->markTestSkipped('No products with slugs found for testing');
        }

        try {
            $result = $this->productRepository->getProductDetails($product->slug);
            $this->assertNotNull($result);
            $this->assertEquals($product->productId, $result->productId);
            echo "\nSuccessfully tested with product slug: " . $product->slug . "\n";
        } catch (\Exception $e) {
            $this->fail("Failed to get details using product slug: " . $e->getMessage());
        }
    }
}