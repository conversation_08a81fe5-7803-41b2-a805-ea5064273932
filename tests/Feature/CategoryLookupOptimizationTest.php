<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Category;
use App\Http\Controllers\Lookups\CategoryLookupController;
use App\Repositories\Category\CategoryRepositoryInterface;
use App\Helper\CacheHelper;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class CategoryLookupOptimizationTest extends TestCase
{
    protected CategoryLookupController $controller;
    protected CategoryRepositoryInterface $categoryRepository;
    protected CacheHelper $cacheHelper;
    protected Category $model;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->categoryRepository = app(CategoryRepositoryInterface::class);
        $this->cacheHelper = app(CacheHelper::class);
        $this->model = app(Category::class);
        
        $this->controller = new CategoryLookupController(
            $this->categoryRepository,
            $this->model,
            $this->cacheHelper
        );
    }

    /**
     * Test that the CategoryLookupController index method doesn't have N+1 queries
     * This test verifies that our optimization reduces the number of database queries.
     */
    public function test_category_lookup_index_optimization()
    {
        // Skip if no categories exist in database
        $categoryCount = Category::count();
        
        if ($categoryCount === 0) {
            $this->markTestSkipped('No categories found in database for testing');
        }

        // Clear any existing cache to ensure we're testing actual database queries
        $this->cacheHelper->deletePattern('*categories*');

        // Enable query logging to count queries
        DB::enableQueryLog();
        
        try {
            // Create a mock request
            $request = Request::create('/api/lookups/categories', 'GET');
            app()->instance('request', $request);
            
            // Call the index method
            $response = $this->controller->index();
            
            // Get the number of queries executed
            $queries = DB::getQueryLog();
            $queryCount = count($queries);
            
            // Verify the response is successful
            $this->assertEquals(200, $response->getStatusCode());
            
            // Parse the response data
            $responseData = json_decode($response->getContent(), true);
            $this->assertArrayHasKey('data', $responseData);
            $this->assertArrayHasKey('success', $responseData);
            $this->assertTrue($responseData['success']);
            
            // Log the number of queries for monitoring
            echo "\nNumber of queries executed: " . $queryCount . "\n";
            echo "Number of categories in database: " . $categoryCount . "\n";
            
            // The key optimization: we should have a very limited number of queries
            // regardless of the number of categories and their nested children
            // Ideally, this should be 1-3 queries maximum:
            // 1. Main categories query with all eager loaded relationships
            // 2. Possibly a cache check query
            // 3. Any other system queries
            $this->assertLessThan(10, $queryCount, 
                "Too many queries executed ({$queryCount}). Possible N+1 query issue not resolved.");
                
            // More strict assertion: for a properly optimized query, 
            // we should have very few queries regardless of data size
            if ($categoryCount > 10) {
                $this->assertLessThan(5, $queryCount, 
                    "With {$categoryCount} categories, query count should be minimal with proper eager loading.");
            }
                
        } catch (\Exception $e) {
            // If the method fails, we want to know about it
            $this->fail("CategoryLookupController index method failed: " . $e->getMessage());
        } finally {
            DB::disableQueryLog();
        }
    }

    /**
     * Test that the OptionsResource doesn't trigger additional queries when processing children
     */
    public function test_options_resource_children_no_additional_queries()
    {
        // Get a category with children
        $categoryWithChildren = Category::with(['children', 'children.children', 'media'])
            ->whereHas('children')
            ->first();
            
        if (!$categoryWithChildren) {
            $this->markTestSkipped('No categories with children found for testing');
        }

        // Clear query log
        DB::enableQueryLog();
        
        try {
            // Create OptionsResource with the pre-loaded category
            $resource = new \App\Http\Resources\OptionsResource(
                resource: collect([$categoryWithChildren]),
                textColumn: 'name',
                valueColumn: 'categoryId',
                meta: ['slug', 'children', 'media', 'sort']
            );
            
            // Convert to array (this is where N+1 queries would occur)
            $result = $resource->toArray(request());
            
            // Get queries executed during resource processing
            $queries = DB::getQueryLog();
            $queryCount = count($queries);
            
            // Verify the result structure
            $this->assertIsArray($result);
            $this->assertNotEmpty($result);
            $this->assertArrayHasKey('meta', $result[0]);
            $this->assertArrayHasKey('children', $result[0]['meta']);
            
            // The key test: no additional queries should be executed
            // since all relationships were already eager loaded
            $this->assertEquals(0, $queryCount, 
                "OptionsResource triggered {$queryCount} additional queries. This indicates N+1 query issue.");
                
        } finally {
            DB::disableQueryLog();
        }
    }
}
