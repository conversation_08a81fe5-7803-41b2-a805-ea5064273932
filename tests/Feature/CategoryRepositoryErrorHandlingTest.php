<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Category;
use App\Repositories\Category\CategoryRepositoryInterface;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Exceptions\ModelNotFoundException as CustomModelNotFoundException;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CategoryRepositoryErrorHandlingTest extends TestCase
{
    protected CategoryRepositoryInterface $categoryRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->categoryRepository = app(CategoryRepositoryInterface::class);
    }

    /**
     * Test that findBySlugCategoryLevels throws proper exception for non-existent category
     */
    public function test_findBySlugCategoryLevels_throws_exception_for_non_existent_category()
    {
        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage('Category not found');

        // Try to find a category that doesn't exist
        $this->categoryRepository->findBySlugCategoryLevels('non-existent-category', []);
    }

    /**
     * Test that findBySlugCategory throws proper exception for non-existent category
     */
    public function test_findBySlugCategory_throws_exception_for_non_existent_category()
    {
        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage('Category not found');

        // Try to find a category that doesn't exist
        $this->categoryRepository->findBySlugCategory('non-existent-category');
    }

    /**
     * Test that findBySlugCategory throws proper exception for non-existent subcategory
     */
    public function test_findBySlugCategory_throws_exception_for_non_existent_subcategory()
    {
        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage('Category not found');

        // Try to find a subcategory that doesn't exist
        $this->categoryRepository->findBySlugCategory('some-category', 'non-existent-subcategory');
    }

    /**
     * Test that methods work correctly when category exists
     */
    public function test_methods_work_when_category_exists()
    {
        // Skip if no categories exist in database
        $existingCategory = Category::first();
        
        if (!$existingCategory) {
            $this->markTestSkipped('No categories found in database for testing');
        }

        // Test findBySlugCategoryLevels with existing category
        $result = $this->categoryRepository->findBySlugCategoryLevels($existingCategory->slug, []);
        $this->assertInstanceOf(Category::class, $result);
        $this->assertEquals($existingCategory->slug, $result->slug);

        // Test findBySlugCategory with existing category
        $result2 = $this->categoryRepository->findBySlugCategory($existingCategory->slug);
        $this->assertInstanceOf(Category::class, $result2);
        $this->assertEquals($existingCategory->slug, $result2->slug);
    }

    /**
     * Test that oldSlug redirect logic works (if applicable)
     * This test will be skipped if no categories with oldSlug exist
     */
    public function test_oldSlug_redirect_logic()
    {
        // Find a category that has oldSlug data
        $categoryWithOldSlug = Category::whereNotNull('oldSlug')->first();
        
        if (!$categoryWithOldSlug) {
            $this->markTestSkipped('No categories with oldSlug found for testing redirect logic');
        }

        // Get the first oldSlug value for the current locale
        $oldSlugData = $categoryWithOldSlug->oldSlug;
        $currentLocale = current_locale();
        
        if (!isset($oldSlugData[$currentLocale]) || empty($oldSlugData[$currentLocale])) {
            $this->markTestSkipped('No oldSlug data for current locale found');
        }

        $oldSlug = is_array($oldSlugData[$currentLocale]) 
            ? $oldSlugData[$currentLocale][0] 
            : $oldSlugData[$currentLocale];

        // Test that searching by oldSlug throws redirect exception
        try {
            $this->categoryRepository->findBySlugCategoryLevels($oldSlug, []);
            $this->fail('Expected CustomModelNotFoundException for oldSlug redirect was not thrown');
        } catch (CustomModelNotFoundException $e) {
            $this->assertEquals(301, $e->getCode());
            $this->assertStringContains('redirecting', $e->getMessage());
            $this->assertNotEmpty($e->getRedirectUrl());
        }
    }

    /**
     * Test that the error handling doesn't break when dealing with multiple slugs
     */
    public function test_multiple_slugs_error_handling()
    {
        // Test with multiple non-existent slugs
        $this->expectException(ModelNotFoundException::class);
        $this->expectExceptionMessage('Category is invalid');

        $this->categoryRepository->findBySlugCategoryLevels('last-slug', ['first-slug', 'second-slug', 'last-slug']);
    }
}
