<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\City;
use App\Models\ShippingCarrierPrice;
use App\Models\ShippingCarriers;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // $shippingCarriers = ShippingCarriers::updateOrCreate([
        //     'slug' => 'free-shipping',
        // ], [
        //     'name' => ['ar' => 'توصيل مجاني', 'en' => 'Free shipping'],
        //     'slug' => 'free-shipping',
        //     'label' => ['ar' => 'توصيل مجاني', 'en' => 'Free shipping'],
        //     'description' => ['ar' => 'توصيل مجاني', 'en' => 'Free shipping'],
        //     'phone' => [],
        //     'default' => false,
        //     'haveFastShipping' => false,
        //     'config' => [
        //         'minItems' => 2,
        //     ]
        // ]);

        // $cities = City::all();
        // foreach ($cities as $city) {
        //     ShippingCarrierPrice::updateOrCreate([
        //         'cityId' => $city->cityId,
        //         'shippingCarrierId' => $shippingCarriers->shippingCarrierId
        //     ], [
        //         'price' => 0,
        //         'cityId' => $city->cityId,
        //         'shippingCarrierId' => $shippingCarriers->shippingCarrierId
        //     ]);
        // }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('free_shipping');
    }
};