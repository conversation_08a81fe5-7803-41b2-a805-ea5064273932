<?php

use App\Models\Label;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $ourChoicesAreForYou = Label::updateOrCreate([
            'slug' => 'our-choices-are-for-you',
        ], [
            'name' => ['en' => 'our choices are for you', 'ar' => 'اختياراتنا لك'],
            'slug' => 'our-choices-are-for-you',
            'color' => '#000000'
        ]);

        // need random products has stock  and associated with this label
        $ourChoicesAreForYou->products()->sync(
            \App\Models\Product::inRandomOrder()->where('hasStock', '>', 0)->take(10)->pluck('productId')
        );

        $bestBrands = Label::updateOrCreate([
            'slug' => 'best-brands',
        ], [
            'name' => ['en' => 'best brands', 'ar' => 'أفضل العلامات التجارية'],
            'slug' => 'best-brands',
            'color' => '#000000'
        ]);


        $bestBrands->brands()->sync(
            \App\Models\Brand::inRandomOrder()->take(10)->pluck('brandId')
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
};