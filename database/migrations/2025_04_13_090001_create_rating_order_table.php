<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_rating', function (Blueprint $table) {
            $table->increments('orderRatingId');
            $table->text('review');
            $table->tinyInteger('rating');
            $table->integer('userId')->unsigned();
            $table->integer('orderId')->index()->unsigned();
            $table->enum('status', ["Published", "Not Published"])->default('Not Published');
            $table->timestamp('createdAt')->useCurrent();
            $table->timestamp('updatedAt')->useCurrent();
            $table->foreign('orderId')->references('orderId')->on('orders')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreign('userId')->references('userId')->on('users')->cascadeOnDelete()->cascadeOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_rating');
    }
};