<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        Schema::create('subscription_stocks', function (Blueprint $table) {

            $table->increments('subscriptionStockId');

            $table->integer('productId')->unsigned()->nullable()->index();
            $table->integer('varianceId')->unsigned()->nullable();

            $table->integer('userId')->unsigned()->nullable();
            $table->bigInteger('visitorId')->unsigned()->nullable();

            $table->string("email")->nullable();
            $table->json('phone')->nullable();

            $table->timestamp('createdAt')->useCurrent();
            $table->timestamp('updatedAt')->useCurrent();


            $table->foreign('varianceId')->references('varianceId')->on('variances')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreign('productId')->references('productId')->on('products')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreign('visitorId')->references('visitorId')->on('visitors')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreign('userId')->references('userId')->on('users')->cascadeOnDelete()->cascadeOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('subscription');
    }
};