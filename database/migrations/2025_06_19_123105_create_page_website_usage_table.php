<?php

use App\Models\Page;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Page::updateOrCreate([
            'slug' => 'website-usage'
        ], [
            'name' => ['ar' => 'سياسة استخدام الموقع', 'en' => 'Website Usage Policy'],
            'slug' => 'website-usage',
            'body' => ['ar' => 'سياسة استخدام الموقع', 'en' => 'Website Usage Policy'],
            'metaTitle' => ['ar' => 'سياسة استخدام الموقع', 'en' => 'Website Usage Policy'],
            'metaDescription' => ['ar' => 'سياسة استخدام الموقع', 'en' => 'Website Usage Policy'],
            'source' => ['ar' => 'سياسة استخدام الموقع', 'en' => 'Website Usage Policy']
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

    }
};