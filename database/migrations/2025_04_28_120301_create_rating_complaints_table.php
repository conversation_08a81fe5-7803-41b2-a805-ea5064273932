<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rating_complaint', function (Blueprint $table) {
            $table->increments('ratingComplaintId');
            $table->text('review');
            $table->tinyInteger('rating');
            $table->integer('userId')->unsigned()->nullable();
            $table->bigInteger('visitorId')->unsigned()->nullable();
            $table->integer('complaintId')->index()->unsigned();

            $table->morphs('model');

            $table->enum('status', ["Published", "Not Published"])->default('Not Published');

            $table->timestamp('createdAt')->useCurrent();
            $table->timestamp('updatedAt')->useCurrent();

            $table->foreign('complaintId')->references('complaintId')->on('complaints')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreign('userId')->references('userId')->on('users')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreign('visitorId')->references('visitorId')->on('visitors')->cascadeOnDelete()->cascadeOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rating_complaints');
    }
};