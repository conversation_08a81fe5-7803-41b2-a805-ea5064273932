<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fcm_token', function (Blueprint $table) {
            $table->bigInteger('visitorId')->unsigned()->nullable()->after('userId');
            $table->foreign('visitorId')->references('visitorId')->on('visitors')->cascadeOnDelete()->cascadeOnUpdate();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('fcm', function (Blueprint $table) {
            //
        });
    }
};