<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('category_labels', function (Blueprint $table) {
            $table->increments('categoryLabelId');
            $table->integer('categoryId')->unsigned();
            $table->integer('labelId')->unsigned();
            $table->foreign('categoryId')->references('categoryId')->on('categories')->cascadeOnDelete()->cascadeOnUpdate();
            $table->foreign('labelId')->references('labelId')->on('labels')->cascadeOnDelete()->cascadeOnUpdate();
            $table->timestamp('createdAt')->useCurrent();
            $table->timestamp('updatedAt')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('category_labels_tables');
    }
};