name: Deploy ECS
run-name: ${{ github.actor }} trigger production deployment 🚀

on:
  push:
     branches:    
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1
      - uses: ksivamuthu/aws-copilot-github-action@v0.0.1
        with:
          command: install
      - run: |
          copilot --version
      - uses: ksivamuthu/aws-copilot-github-action@v0.0.1
        with:
          command: deploy
          app: v2
          env: prod
          force: true
